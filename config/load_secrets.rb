require "aws-sdk-secretsmanager"
require "json"

def load_secrets
  unless ENV["RAILS_ENV"] == "development"
    get_secret_value_response = Aws::SecretsManager::Client.new(region: "us-east-1").get_secret_value(secret_id: "prod/stocksync")
    secrets = get_secret_value_response.secret_string
    JSON.parse(secrets).each do |key, value|
      ENV[key] = value
    end
    if ENV["RAILS_ENV"] == "staging"
      ENV["SHOPIFY_API_KEY"] = ENV["STAGING_SHOPIFY_API_KEY"]
      ENV["SHOPIFY_API_SECRET"] = ENV["STAGING_SHOPIFY_API_SECRET"]
    end
  end
end

load_secrets
