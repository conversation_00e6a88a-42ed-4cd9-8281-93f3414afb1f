{
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,

  "i18n-ally.localesPaths": ["app/javascript/locales"],
  "i18n-ally.keystyle": "flat",

  "[ruby]": {
    "editor.defaultFormatter": "Shopify.ruby-lsp"
  },

  "rubyLsp.formatter": "standard",
  "rubyLsp.linters": ["standard"],
  // Line below override the settings.json of vscode to default, due to other repo still using ruby ver 2 that need specific path of ruby 3 Gemfile
  "rubyLsp.bundleGemfile": "",
  "rubyLsp.enabledFeatures": {
    "codeActions": true,
    "diagnostics": true,
    "documentHighlights": true,
    "documentLink": true,
    "documentSymbols": true,
    "foldingRanges": true,
    "formatting": true,
    "hover": true,
    "inlayHint": true,
    "onTypeFormatting": true,
    "selectionRanges": true,
    "semanticHighlighting": true,
    "completion": true,
    "codeLens": true,
    "definition": true,
    "workspaceSymbol": true,
    "signatureHelp": true,
    "typeHierarchy": true,
    "references": true
  },

  "zencoder.enableRepoIndexing": true
}
