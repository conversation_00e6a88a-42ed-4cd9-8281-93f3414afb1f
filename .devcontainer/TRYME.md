# Databases in containers instead of local

Spin up 2 servers: <PERSON>gres and Redis in containers.

Download a container tool - [<PERSON><PERSON>](https://podman.io/) is preferred but [<PERSON><PERSON>](https://www.docker.com/) works great too.

Here's to the nerds. `¯\_(ツ)_/¯`

## Podman

Download [<PERSON><PERSON>](https://podman.io/) CLI and/or [Podman Desktop](https://podman-desktop.io) (optional but makes life more colorful)

## Redis

### Build image

```
podman build -f .devcontainer/redis/Dockerfile -t localhost/redis:alpine
```

### Run image in container

Stop local redis. (to avoid `6379` port binding conflict)

To start container redis, run:

```
podman run --name redis -dti -p 6379:6379/tcp localhost/redis:alpine
```

## Postgres

### Build image

```
podman build -f .devcontainer/postgres/Dockerfile -t localhost/postgres:alpine
```

### Run image in container

Stop local postgres. (to avoid `5432` port binding conflict)

To start container postgres, run:

```
podman run --name postgres -dti -p 5432:5432/tcp localhost/postgres:alpine
```

## Cloning Postgres DB from local to container (Linux)

### Export (from local postgres)

Make sure local postgres is running, and run:

```
pg_dumpall -h localhost -U postgres > dump.sql
```

### Drop all database tables (DANGEROUS!)

**SKIP THIS STEP UNLESS REQUIRED!**

This will drop **ALL** databases in db!

**Always** export backup to `.sql` first before running this, just in case.

```
psql -abe -h localhost -U postgres -d postgres < <( psql -h localhost -U postgres -Atc "select 'DROP DATABASE \"'||datname||'\" WITH (FORCE);' from pg_database where datistemplate=false;")
```

### Import (to container postgres)

- Stop local postgres.
- Run container postgres.
- Run:

```
psql -h localhost -U postgres -f dump.sql
```

## Cloning Redis DB from local to container

TODO:

## Containerize Kafka DB

TODO:
