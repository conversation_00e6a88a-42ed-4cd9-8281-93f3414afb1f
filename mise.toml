# https://mise.jdx.dev/configuration.html
# to extend, create your own mise.local.toml and add scripts:

# [tasks.dev-my-task]
# run = "pnpm audit"

# when `mise dev` is run, `dev-my-task` will also run due to `depends = ["dev-*"]`

[tasks.bundle-install]
run = "bundle install"
sources = ['Gemfile', 'Gemfile.lock']
outputs = { auto = true }

[tasks.pnpm-install]
run = "pnpm install"
sources = ['package.json', 'pnpm-lock.yaml']
outputs = { auto = true }

[tasks.check-lint]
run = "pnpm lint"

[tasks.check-tsc]
depends = ["pnpm-install"]
run = "pnpm typecheck"

[tasks.check]
depends = ["check-*"]

[tasks.build-production]
depends = ["bundle-install", "pnpm-install"]
run = "pnpm build:production"

[tasks.rails-db-migrate]
alias = "dbm"
depends = ["bundle-install"]
run = "RAILS_ENV=development bundle exec rails db:migrate"

[tasks.rails-console]
alias = "rc"
depends = ["bundle-install"]
run = "RAILS_ENV=development bundle exec rails c"

[tasks.dev-rails]
depends = ["bundle-install"]
run = ["RAILS_ENV=development bundle exec rails s"]

[tasks.dev-vite]
depends = ["bundle-install", "pnpm-install"]
run = "RAILS_ENV=development bundle exec vite dev"

[tasks.dev]
depends = ["dev-*"]
