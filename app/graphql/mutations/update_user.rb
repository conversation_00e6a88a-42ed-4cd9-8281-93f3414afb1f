module Mutations
  class UpdateUser < BaseMutation
    argument :notification_email, type: String, required: false
    argument :timezone, String, required: false
    argument :auto_get_higher_qty, <PERSON><PERSON><PERSON>, required: false
    argument :locale, String, required: false
    argument :close_feedback, <PERSON><PERSON><PERSON>, required: false
    argument :welcome_toast_hash, String, required: false
    argument :email_subscriptions, [String], required: false
    argument :seen_first_usage_modal, Boolean, required: false
    argument :user_using_whitelisting_ip_address, String, required: false
    argument :warning_zero_qty_update, String, required: false
    argument :warning_remove_products, Integer, required: false
    argument :prestashop_api_key, String, required: false
    argument :is_embedded, <PERSON><PERSON>an, required: false
    field :status, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :shop, Types::StoreType, null: true

    def resolve(**args)
      status = true
      errors = []

      begin
        if current_shop.provider == "prestashop" &&
            current_shop.prestashop_api_key != args[:prestashop_api_key]
          ps_api = PrestashopApi.new(current_shop)
          test_res = ps_api.test_new_api_key(api_key: args[:prestashop_api_key])
          if !test_res
            raise StandardError.new("Invalid API Key")
          end
        end

        current_shop.update(args)
        raise ArgumentError, current_shop.errors.full_messages.join(", ") unless current_shop.save
      rescue => e
        status = false
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        shop: current_shop
      }
    end
  end
end
