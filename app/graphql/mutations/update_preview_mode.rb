module Mutations
  class UpdatePreviewMode < BaseMutation
    argument :preview, <PERSON><PERSON><PERSON>, required: true
    argument :feed_id, Integer, required: true
    field :status, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :feed, Types::UserProfileType, null: true
    def resolve(preview:, feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      errors = []
      begin
        feed.preview = preview
        feed.save(validate: false)
      rescue => e
        status = false
        feed = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        feed: feed
      }
    end
  end
end
