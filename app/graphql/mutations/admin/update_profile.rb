module Mutations
  module Admin
    class UpdateProfile < BaseMutation
      argument :feed_id, Integer, required: true
      argument :feed_attributes, Arguments::FeedInput, required: true

      field :status, Boolean, null: false
      field :error, String, null: false
      field :feed, Types::UserProfileType, null: true

      def resolve(feed_id:, feed_attributes:)
        status = true
        error = ""

        begin
          profile = UserProfile.with_deleted.find(feed_id)
          feed_attributes = feed_attributes.to_h
          profile.update(feed_attributes)
        rescue => e
          status = false
          error = e.message
        end

        {status: status, error: error, feed: profile}
      end
    end
  end
end
