module Mutations
  module Admin
    class ProcessFeed < BaseMutation
      argument :feed_id, Integer, required: true
      argument :run_on_local, <PERSON><PERSON><PERSON>, required: false, default_value: false
      argument :process_limit, Integer, required: false

      field :status, <PERSON><PERSON><PERSON>, null: false
      field :error, String, null: false
      field :feed, Types::UserProfileType, null: true

      def resolve(feed_id:, run_on_local:, process_limit:)
        status = true
        error = ""

        begin
          profile = UserProfile.with_deleted.find(feed_id)
          profile.update(checksum: nil, checksum_file_size: nil)

          opts = {trigger_by: "support", run_on_local: run_on_local}
          opts[:process_limit] = process_limit if process_limit

          feed_process_job = ProcessFeedJob.new(profile.id, opts)
          _job = Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue)
          profile.queuing
        rescue => e
          status = false
          error = e.message
        end

        {status: status, error: error, feed: profile}
      end
    end
  end
end
