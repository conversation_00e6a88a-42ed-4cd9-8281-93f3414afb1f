module Mutations
  module Admin
    class CopyProfileToStore < BaseMutation
      argument :store_id, Integer, required: true
      argument :feed_id, Integer, required: true

      field :status, <PERSON><PERSON><PERSON>, null: false
      field :remark, String, null: false

      def resolve(store_id:, feed_id:)
        status = true
        remark = ""

        profile = UserProfile.with_deleted.find(feed_id)
        target_user = User.find(store_id)

        if profile && target_user
          begin
            new_profile = profile.duplicate
            new_profile.user_id = target_user.id
            new_profile.save

            if new_profile.save
              remark = "New feed copied"
            else
              status = false
              remark = "Not able to copy to store ID #{store_id}"
            end
          rescue => e
            status = false
            remark = e.message
          end
        end

        {status: status, remark: remark}
      end
    end
  end
end
