module Mutations
  module Admin
    class ClearProfileCache < BaseMutation
      argument :feed_id, Integer, required: true

      field :status, <PERSON><PERSON><PERSON>, null: false
      field :message, String, null: false

      def resolve(feed_id:)
        begin
          Rails.cache.delete("active_admin/load_#{feed_id}") # clear load source cache
          RedisManager.remove_sample_data(feed_id)
          profile = UserProfile.with_deleted.find(feed_id)
          Rails.cache.delete("profile#{profile.id}-#{profile.updated_at.to_i}-#{profile.sync_fields.maximum(:updated_at).to_i}", namespace: "arbre")

          # Clear load cache
          working_dir = Rails.env.production? ? "/home/<USER>/efs_storage/stock_sync/source_cache" : Rails.root.join("public", "system", "source_cache").to_s
          job = JobApi.new(user_profile_id: feed_id)
          cache_key, _expiry = job.source_cache_key[:message]
          if Pathname("#{working_dir}/#{cache_key}").exist?
            fork { exec("echo '' | sudo -S rm #{working_dir}/#{cache_key}") }
            # File.delete("#{working_dir}/#{cache_key}")
          end
        rescue
        end

        {status: true, message: "Cache cleared"}
      end
    end
  end
end
