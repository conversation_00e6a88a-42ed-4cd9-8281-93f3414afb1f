module Mutations
  module Admin
    class CreateTemplate < BaseMutation
      argument :feed_id, Integer, required: true

      field :status, <PERSON><PERSON><PERSON>, null: false
      field :message, String, null: false

      def resolve(feed_id:)
        status = true
        message = ""

        profile = UserProfile.with_deleted.find(feed_id)

        if profile.fast_track?
          status = false
          message = "This is a Snappy profile."
        else
          template = profile.make_as_template
          message = "Successfully copied as template ##{template.id}"
        end

        {status: status, message: message}
      end
    end
  end
end
