module Mutations
  module Admin
    class CheckProcessCacheKey < BaseMutation
      argument :feed_id, Integer, required: true

      field :status, <PERSON><PERSON><PERSON>, null: false
      field :message, String, null: false

      def resolve(feed_id:)
        message = "Invalid environment, use only in production"
        if Rails.env.production?
          working_dir = "/home/<USER>/efs_storage/stock_sync/source_cache"
          job = JobApi.new(user_profile_id: feed_id)
          cache_key, expiry = job.source_cache_key[:message]
          data = "Cache key: #{cache_key} (valid? #{expiry})<br>Location: #{working_dir}/#{cache_key} (exists? #{Pathname("#{working_dir}/#{cache_key}").exist?})"
          data
        end

        {status: true, message: message}
      end
    end
  end
end
