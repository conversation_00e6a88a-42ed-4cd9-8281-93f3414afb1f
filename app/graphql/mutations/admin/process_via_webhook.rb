module Mutations
  module Admin
    class ProcessViaWebhook < BaseMutation
      argument :feed_id, Integer, required: true
      argument :run_on_local, <PERSON><PERSON><PERSON>, required: false, default_value: false

      field :status, <PERSON><PERSON><PERSON>, null: false
      field :remark, String, null: false
      field :feed, Types::UserProfileType, null: true

      def resolve(feed_id:, run_on_local:)
        remark = ""
        status = true
        profile = UserProfile.with_deleted.find(feed_id)
        user = profile.user
        # user.upsert_webhooks(Settings.shopify_eventbridge.bulk_operations.to_h)

        if (profile.start? || profile.pause?) && profile.location_id.present?
          if (status = (response = ShopifyAPI::LightGraphQL.bulk_query(user.shopify_domain, user.shopify_token, profile)).dig("data", "bulkOperationRunQuery", "bulkOperation") || {})["status"] == "CREATED"
            profile.fargate_task_id = "bulk::#{status["id"]}"
            profile.save
            profile.queuing
            remark = "#{remark} Successfully started bulk op"
          else
            remark = "#{remark} Failed to start bulk op"
          end
          profile.webhook_logs.create(external_id: status["id"], metadata: "[#{(run_on_local == true) ? "\"local,\"" : ""}#{response.to_json}]", trigger_by: "admin")
        else
          status = false
          remark = "#{remark} Aborted, profile is queued/running or location is blank"
        end

        {status: status, remark: remark, feed: profile}
      end
    end
  end
end
