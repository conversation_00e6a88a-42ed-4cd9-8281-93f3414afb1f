module Mutations
  class UpdateFeedSource < BaseMutation
    argument :feed_id, Integer, required: true
    argument :combined_source_type, String, required: false
    argument :acc_name, String, required: false
    argument :password, String, required: false
    argument :source_url, String, required: false
    argument :one_drive_share_url, String, required: false
    argument :file_format, String, required: false
    argument :parent_node, String, required: false
    argument :variant_node, String, required: false
    argument :has_header, Bo<PERSON>an, required: false
    argument :path_to_file, String, required: false
    argument :export_email, String, required: false
    argument :ssh_key, String, required: false
    argument :source_auth, Boolean, required: false
    argument :sheet_name, String, required: false
    argument :namespace_identifier, String, required: false
    argument :http_method, String, required: false
    argument :call_params, String, required: false
    argument :etsy_agree, Bo<PERSON>an, required: false
    argument :ftp_rename, Integer, required: false
    argument :custom_file_name, String, required: false
    argument :ftp_mode, String, required: false
    argument :file_encoding, String, required: false
    argument :file_name, String, required: false
    argument :header_params, String, required: false
    argument :body_raw, String, required: false
    argument :col_sep, String, required: false
    argument :s3_access_key_id, String, required: false
    argument :s3_secret_access_key, String, required: false
    argument :s3_bucket_name, String, required: false
    argument :custom_login_field, String, required: false
    argument :custom_login_password_field, String, required: false
    argument :clear_google_sheet, Boolean, required: false
    argument :google_out_insert, Boolean, required: false
    argument :unleashed_api_id, String, required: false
    argument :unleashed_api_key, String, required: false
    argument :aliexpress_app_id, String, required: false
    argument :aliexpress_app_secret, String, required: false
    argument :ftp_whitelist, Boolean, required: false
    argument :google_shopping_category, String, required: false
    argument :product_key_separator, String, required: false
    argument :woocommerce_api_version, String, required: false
    argument :woocommerce_consumer_key, String, required: false
    argument :woocommerce_consumer_secret, String, required: false
    argument :walmart_client_id, String, required: false
    argument :walmart_client_secret, String, required: false
    argument :auth_type, String, required: false
    argument :filter_params, GraphQL::Types::JSON, required: false
    argument :connection_settings, GraphQL::Types::JSON, required: false
    argument :skip_total_rows, Integer, required: false
    argument :public_file, Boolean, required: false
    argument :auto_file_settings, Boolean, required: false

    field :status, Boolean, null: false
    field :errors, [String], null: false
    field :feed, Types::UserProfileType, null: true
    def resolve(**args)
      feed = current_shop.user_profiles.with_deleted.find(args[:feed_id])
      status = true
      errors = []
      begin
        permitted_args = sanitize_feed_args(args)
        feed.assign_attributes(permitted_args)
        update_parent_remove_feed(feed)
        feed.check_job_type_change
        feed.strip_key_space
        raise ArgumentError, feed.errors.full_messages.to_sentence unless feed.save
      rescue => e
        status = false
        feed = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        feed: feed
      }
    end

    def sanitize_feed_args(params)
      source_infos = params[:combined_source_type].split("_") if params[:combined_source_type].present?
      params[:io_mode] = if ["in", "out"].include? source_infos&.last
        source_infos.pop
      else
        "in"
      end
      params[:source_type] = source_infos.join("_") if source_infos.present?

      unless params[:password].blank?
        password = PasswordManager.encrypt(params[:password])
        params[:acc_password] = password[:password]
        params[:password_salt] = password[:salt]
      end
      params[:connection_settings] = params[:connection_settings].permit!.to_h if params[:connection_settings].present?
      params.delete(:combined_source_type)
      params.delete(:password)
      params.delete(:feed_id)
      params
    end

    def update_parent_remove_feed(feed)
      return unless feed.is_remove_type?
      # ? auto clear not in feed source url is parent feed id
      # ? this line check if source url is integer or string
      return unless feed.source_url.match?(/^\d+$/)

      if feed.source_url_changed?
        previous_value = feed.source_url_was

        # ? update previous parent feed
        if previous_value.present? && previous_value.match?(/^\d+$/)
          previous_parent_feed = UserProfile.find_by_id(previous_value.to_i)
          previous_parent_feed.update(remove_profile_id: nil) if previous_parent_feed.remove_profile_id == feed.id
        end

        # ? update new parent feed
        new_value = feed.source_url
        new_parent_feed = UserProfile.find_by_id(new_value.to_i)
        new_parent_feed.update(remove_profile_id: feed.id)
      end
    rescue => e
      Rails.logger.error("Error in update_parent_remove_feed #{e}")
      Airbrake.notify(e, params: {feed_id: feed.id})
    end
  end
end
