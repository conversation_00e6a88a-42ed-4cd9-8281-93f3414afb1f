module Mutations
  class CreateWixCheckout < BaseMutation
    argument :plan_name, String, required: true
    argument :is_yearly, <PERSON><PERSON>an, required: false, default_value: false

    field :status, Boolean, null: false
    field :error, String, null: true
    field :log, String, null: true

    def resolve(plan_name:, is_yearly:)
      status = true
      error = ""

      begin
        url = current_shop.create_checkout_url(plan_name, is_yearly)
      rescue => e
        status = false
        error = e.message
      end

      {status: status, error: error, log: url}
    end
  end
end
