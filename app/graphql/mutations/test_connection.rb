module Mutations
  class TestConnection < BaseMutation
    argument :feed_id, Integer, required: true
    argument :combined_source_type, String, required: false
    argument :source_url, String, required: false
    argument :path_to_file, String, required: false
    argument :acc_name, String, required: false
    argument :password, String, required: false
    argument :source_auth, <PERSON><PERSON><PERSON>, required: false
    argument :ssh_key, String, required: false
    argument :s3_access_key_id, String, required: false
    argument :s3_secret_access_key, String, required: false
    argument :s3_bucket_name, String, required: false
    argument :woocommerce_consumer_key, String, required: false
    argument :woocommerce_consumer_secret, String, required: false
    field :status, <PERSON><PERSON>an, null: false
    field :errors, [String], null: true
    field :message, String, null: true
    def resolve(**args)
      connection = {status: false, message: :please_check_settings}
      message = nil
      errors = []
      begin
        feed = current_shop.user_profiles.with_deleted.find(args[:feed_id])
        permitted_args = sanitize_feed_args(args)
        if feed.update(permitted_args)
          # klass = feed.klass_for_file_reade
          # connection = klass.check_connection(feed
          begin
            connection = if Rails.env.development?
              command = "cd ../stock-sync-job && bundle exec rake job:test_connection[#{feed.id}]"
              shell = IO.popen(command)
              resp = shell.read.delete("\n")
              shell.close
              resp.scan(/\{"status":.*\}/).each do |r|
                connection = JSON.parse(r, symbolize_names: true)
                break
              rescue JSON::ParserError
                next
              end
              connection
            else
              job = JobApi.new(user_profile_id: feed.id)
              job.test_connection
            end
          rescue => e
            # Airbrake.notify(e, message: "test connection error: profile.id - #{feed.id}, #{e.message}")
            message = e.message
            errors = [e.message]
          end
          raise ArgumentError, connection[:message] unless connection[:status]
        # raise: { message: connection[:message].to_sym })
        else
          raise ArgumentError, feed.errors.full_messages.to_sentence
        end
      rescue => e
        message = e.message
        errors = [e.message]
      end
      {
        status: connection[:status] || false,
        errors: errors,
        message: message
      }
    end

    def sanitize_feed_args(params)
      source_infos = params[:combined_source_type].split("_") if params[:combined_source_type].present?
      params[:io_mode] = if ["in", "out"].include? source_infos&.last
        source_infos.pop
      else
        "in"
      end
      params[:source_type] = source_infos.join("_") if source_infos.present?

      unless params[:password].blank?
        password = PasswordManager.encrypt(params[:password])
        params[:acc_password] = password[:password]
        params[:password_salt] = password[:salt]
      end
      params.delete(:combined_source_type)
      params.delete(:password)
      params.delete(:feed_id)
      params
    end
  end
end
