module Mutations
  class CreateShopifyCheckout < BaseMutation
    argument :plan_name, String, required: true
    argument :is_yearly, <PERSON><PERSON><PERSON>, required: false, default_value: false

    field :status, <PERSON><PERSON>an, null: false
    field :error, String, null: true
    field :log, String, null: true

    def resolve(plan_name:, is_yearly: false)
      confirmation_url = nil
      status = true
      error = ""

      begin
        if plan_name == "Trial"
          current_shop.unsubscribe
        elsif plan_name == "Snappy"
          current_shop.subscribe_to_snappy_plan
        elsif plan_name == "Free"
          current_shop.subscribe_to_free_plan
        end

        if ["Trial", "Snappy", "Free"].include?(plan_name)
          return {
            status: status,
            error: error,
            log: confirmation_url
          }
        end

        plan = Plan.non_customized.where(
          key: plan_name,
          version: current_shop.plan_version
        ).first
        charge = ShopifyAPI::LightGraphQL.create_subscription(current_shop.shopify_domain, current_shop.shopify_token, current_shop.public_token, plan, current_shop.remaining_trial_days, is_yearly, false, current_shop.affiliate)

        if charge&.has_key?("confirmationUrl")
          if current_shop.try(:id) && charge.has_key?("id")
            Rails.cache.write(CacheKey.charge_id(current_shop.id), charge["id"])
          end
          confirmation_url = charge["confirmationUrl"]
        end
      rescue => e
        status = false
        error = e.message
        confirmation_url = nil
      end
      {
        status: status,
        error: error,
        log: confirmation_url
      }
    end
  end
end
