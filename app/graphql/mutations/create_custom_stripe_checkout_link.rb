module Mutations
  class CreateCustomStripeCheckoutLink < BaseMutation
    argument :price, Float, required: true
    argument :variant_limit, Integer, required: true
    argument :profile_limit, Integer, required: true
    argument :schedule_limit, Integer, required: true
    argument :annually, <PERSON><PERSON><PERSON>, required: false, default_value: false

    field :status, Bo<PERSON>an, null: false
    field :error, String, null: true
    field :log, String, null: true # return Checkout link

    def resolve(price:, variant_limit:, profile_limit:, schedule_limit:, annually:)
      status = true
      error = ""
      package_name = "Custom Plan"
      charge_price_in_cents = (100 * price.to_r).to_i
      charge_price_in_cents *= 11 if annually
      return {status: false, error: "Fail", log: nil} if price < 25

      # Step 1: Validate if price and plan is correct
      success = CustomPlanValidator.check(price: price, variant_limit: variant_limit, profile_limit: profile_limit, schedule_limit: schedule_limit)
      return {status: false, error: "Validator fail", log: nil} unless success

      # Step 2: Create custom plan here based on params (e.g price, variant_limit, profile_limit, schedule_limit)
      total_schedule_per_day = 24 / schedule_limit
      package_attributes = {
        key: "Custom Plan #{current_shop.shopify_domain}_#{Time.now.to_i}", # key needs to be unique in order to be able to retrieve later on
        price: price,
        limit: variant_limit,
        source_limit: profile_limit,
        min_hour: total_schedule_per_day,
        schedule: "min #{total_schedule_per_day} hours",
        version: nil,
        published: false,
        is_custom_plan: true
      }
      package = Plan.create(package_attributes)

      # Create checkout URL
      payload = {
        success_url: File.join(Settings.hostname, "/billing?payment_success=true"),
        cancel_url: File.join(Settings.hostname, "/billing?payment_success=false"),
        payment_method_types: ["card"],
        mode: "subscription",
        line_items: [{
          price_data: {
            unit_amount: charge_price_in_cents,
            currency: "USD",
            product_data: {name: package_name},
            recurring: {
              interval: annually ? "year" : "month",
              interval_count: 1
            }
          },
          quantity: 1
        }],
        subscription_data: {
          metadata: {
            shop_id: current_shop.id,
            provider: current_shop.provider,
            custom_plan: true,
            plan_id: package.id
          }
        }
      }
      payload[:customer] = current_shop&.stripe_customer_id
      session = Stripe::Checkout::Session.create(payload)
      url = session.url

      {status: status, error: error, log: url}
    rescue => e
      {status: false, error: e.message, log: nil}
    end
  end
end
