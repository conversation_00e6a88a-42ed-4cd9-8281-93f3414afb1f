module Mutations
  class UpdateFeedSettings < BaseMutation
    argument :feed_id, Integer, required: true
    argument :shopify_product_key, String, required: false
    argument :shopify_variant_key, String, required: false
    argument :store_prefix, String, required: false
    argument :prefix, String, required: false
    argument :postfix, String, required: false
    argument :case_sensitive, Boolean, required: false
    argument :sync_fields_attributes, [GraphQL::Types::JSON], required: false
    argument :sync_field_settings, [GraphQL::Types::JSON], required: false
    argument :vendor_filter, String, required: false
    argument :exclude_vendors, String, required: false
    argument :exclude_product_types, String, required: false
    argument :exclude_tags, String, required: false
    argument :exclude_collections, String, required: false
    argument :extra_options, GraphQL::Types::JSON, required: false
    argument :include_skus, String, required: false
    argument :exclude_skus, String, required: false
    argument :include_tags, String, required: false
    argument :product_type_filter, String, required: false
    argument :collection_filter_title, String, required: false
    argument :collection_filter_id, String, required: false
    argument :product_title_filter, String, required: false
    argument :exclude_product_titles, String, required: false
    argument :auto_reset_quantity, Boolean, required: false
    argument :hide_unmatch_products, Boolean, required: false
    argument :is_auto_generate_metafield_definitions, Boolean, required: false
    argument :ignore_dont_track_inventory, Boolean, required: false
    argument :ignore_zero_quantity, Boolean, required: false
    argument :update_duplicate_product_key, Boolean, required: false
    argument :published_apply_to_all, Boolean, required: false
    argument :skip_import_with_zero_qty, Boolean, required: false
    argument :variant_image_link, Boolean, required: false
    argument :assign_variants_to_first_image, Boolean, required: false
    argument :unpublished_apply_to_all, Boolean, required: false
    argument :shopify_track_inventory, Boolean, required: false
    argument :published_apply_matching_products, Boolean, required: false
    argument :unpublished_apply_matching_products, Boolean, required: false
    argument :low_stock_level, Integer, required: false
    argument :location_id, String, required: false
    argument :location_name, String, required: false
    argument :has_header, Boolean, required: false
    argument :import_sort, Boolean, required: false
    argument :shopify_inventory_management, String, required: false
    argument :published_filter, String, required: false
    argument :force_override_compare_at_price, Boolean, required: false
    argument :import_tags, String, required: false
    argument :filter_params, GraphQL::Types::JSON, required: false
    argument :product_key_separator, String, required: false
    argument :delete_mode, Integer, required: false
    argument :partial_match, Boolean, required: false
    argument :bigcommerce_retain_availability, Boolean, required: false
    argument :auto_tagging, Boolean, required: false
    argument :remove_product_when_all_locations_nil, Boolean, required: false
    argument :bypass_blank_row, Boolean, required: false
    argument :store_filters, [GraphQL::Types::JSON], required: false
    argument :feed_filters, [GraphQL::Types::JSON], required: false
    argument :metafields, [GraphQL::Types::JSON], required: false
    argument :product_identifier_sync_field, GraphQL::Types::JSON, required: false
    argument :product_options_sync_field, [GraphQL::Types::JSON], required: false
    argument :update_product_level, Boolean, required: false
    argument :woocommerce_product_attributes, [GraphQL::Types::JSON], required: false
    argument :sales_channel_ids, [String], required: false
    field :status, Boolean, null: false
    field :errors, [String], null: false
    field :feed, Types::UserProfileType, null: true
    def resolve(**args)
      feed = current_shop.user_profiles.with_deleted.find(args[:feed_id])
      args.delete(:feed_id)
      metafields = args.delete(:metafields)
      woocommerce_product_attributes = args.delete(:woocommerce_product_attributes)
      product_identifier_sync_field = args.delete(:product_identifier_sync_field)
      product_options_sync_field = args.delete(:product_options_sync_field)
      status = true
      errors = []
      begin
        sync_field_attributes = args[:sync_fields_attributes] || args[:sync_field_settings]

        if feed.is_import_type? && sync_field_attributes
          sync_field_attributes.each do |sync_field|
            if sync_field["field_mapping"] && sync_field["field_name"] == "variant_group"
              if sync_field["field_mapping"].blank?
                sync_field["_destroy"] = 1
              end
            end
          end
        end

        args[:sync_field_settings] = append_product_identifier_to_sync_field_attributes(product_identifier_sync_field, args[:sync_field_settings]) if product_identifier_sync_field.present?
        args[:sync_field_settings] = append_product_options_to_sync_field_attributes(product_options_sync_field, args[:sync_field_settings]) if product_options_sync_field.present?
        args[:sync_field_settings] = append_metafields_to_sync_field_attributes(metafields, args[:sync_field_settings]) if metafields.present?
        args[:sync_field_settings] = append_metafields_to_sync_field_attributes(woocommerce_product_attributes, args[:sync_field_settings]) if woocommerce_product_attributes.present?
        formatted_profile = FeedFormatter.call(args)

        key = formatted_profile[:shopify_product_key]
        if key
          keys = key.split("++")

          if keys.count > 1
            formatted_profile[:shopify_product_key] = keys.first
            formatted_profile[:variant_key_1] = keys[1]

            if keys.count > 2
              formatted_profile[:variant_key_2] = keys[2]

              formatted_profile[:variant_key_3] = if keys.count > 3
                keys[3]
              else
                ""
              end
            else
              formatted_profile[:variant_key_2] = ""
              formatted_profile[:variant_key_3] = ""
            end
          else
            formatted_profile[:variant_key_1] = ""
            formatted_profile[:variant_key_2] = ""
            formatted_profile[:variant_key_3] = ""
          end

          product_field = sync_field_attributes.first
          if product_field[:field_name] == "product_id"
            keys = product_field[:field_mapping].to_s.split("++")
            if keys.count > 1
              product_field[:field_mapping] = keys[0]
              formatted_profile[:source_key_1] = keys[1]

              if keys.count > 2
                formatted_profile[:source_key_2] = keys[2]

                formatted_profile[:source_key_3] = if keys.count > 3
                  keys[3]
                else
                  ""
                end
              else
                formatted_profile[:source_key_2] = ""
                formatted_profile[:source_key_3] = ""
              end
            else
              formatted_profile[:source_key_1] = ""
              formatted_profile[:source_key_2] = ""
              formatted_profile[:source_key_3] = ""
            end
          end
        end

        if (sync_fields = formatted_profile[:sync_field_settings])
          marked_for_destruction = sync_fields.select { |field| field["id"].present? and field["_destroy"].present? }
          sync_fields = sync_fields.difference(marked_for_destruction)

          if marked_for_destruction.present?
            formatted_profile[:sync_fields_attributes] = marked_for_destruction
            formatted_profile.delete(:sync_field_settings)

            if formatted_profile[:sync_fields_attributes].present?
              formatted_profile[:sync_fields_attributes] = formatted_profile[:sync_fields_attributes].map do |field|
                field.permit!
              end
            end
            feed.update!(formatted_profile)
          end

          formatted_profile[:sync_fields_attributes] = sync_fields
          formatted_profile.delete(:sync_field_settings) if formatted_profile[:sync_field_settings].present?
        end

        if formatted_profile[:sync_fields_attributes].present?
          formatted_profile[:sync_fields_attributes] = formatted_profile[:sync_fields_attributes].map do |field|
            field.permit!
          end
        end

        # TODO figure out how to handle <ActionController::Parameters.. in the formatted_profile to refactor this
        prev_feed_filters = feed.feed_filters
        prev_store_filters = feed.store_filters

        if feed.update!(formatted_profile)

          # Handle ++ (delimiter) in incoming feed filter inside update_backwards_compatible_feed_filters
          update_backwards_compatible_feed_filters(feed, prev_feed_filters)
          update_backwards_compatible_store_filters(feed, prev_store_filters)
        else
          raise ArgumentError, feed.errors.full_messages.to_sentence
        end
      rescue => e
        Airbrake.notify(e, message: args)
        status = false
        feed = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        feed: feed
      }
    end

    # ? append metafields/custom_fields to sync_field_attributes
    def append_metafields_to_sync_field_attributes(metafields, sync_field_attributes)
      sync_field_attributes.concat(metafields)
    end

    # ? append product identifier to sync_field_attributes
    def append_product_identifier_to_sync_field_attributes(product_identifier_sync_field, sync_field_attributes)
      sync_field_attributes.unshift(product_identifier_sync_field)
    end

    # ? append product options to sync_field_attributes
    def append_product_options_to_sync_field_attributes(product_options_sync_field, sync_field_attributes)
      sync_field_attributes.concat(product_options_sync_field)
    end

    def update_backwards_compatible_feed_filters(feed, prev_feed_filters)
      UserProfile.process_old_feed_filters(feed) if prev_feed_filters != feed.feed_filters
    end

    def update_backwards_compatible_store_filters(feed, prev_store_filters)
      UserProfile.process_old_store_filters(feed) if prev_store_filters != feed.store_filters
    end
  end
end
