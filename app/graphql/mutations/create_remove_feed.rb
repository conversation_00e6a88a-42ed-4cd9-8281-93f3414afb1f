module Mutations
  class CreateRemoveFeed < BaseMutation
    argument :feed_id, Integer, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true
    field :update_feed, Types::UserProfileType, null: true

    def resolve(feed_id:)
      status = true
      error = ""
      feed = current_shop.user_profiles.with_deleted.find(feed_id)

      if feed
        begin
          remove_feed = feed.create_remove_feed_from_profile
          feed.update(remove_profile_id: remove_feed.id)
        rescue => e
          status = false
          error = e.message
        end
      end

      {status: status, error: error, feed: remove_feed, update_feed: feed}
    end
  end
end
