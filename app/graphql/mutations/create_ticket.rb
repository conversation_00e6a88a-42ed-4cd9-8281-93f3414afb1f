module Mutations
  class CreateTicket < BaseMutation
    argument :attributes, Arguments::TicketInput, required: true

    field :status, Bo<PERSON>an, null: false
    field :error, String, null: false

    def resolve(attributes:)
      status = true
      error = ""

      begin
        ticket_params = attributes.to_h.transform_keys(&:to_s)
        unless Rails.env.development?
          result = CrispClient.new(current_shop).create_ticket(ticket_params) || {}
          raise StandardError.new(result["data"]) if result["error"]
        end
      rescue => e
        status = false
        error = e.message
      end

      {status: status, error: error}
    end
  end
end
