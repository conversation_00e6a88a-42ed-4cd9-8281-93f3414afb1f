module Mutations
  class DeleteFeed < BaseMutation
    argument :feed_id, Integer, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: false
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = false
      error = ""

      if feed
        begin
          raise ArgumentError, :forbidden if feed.fast_track?
          raise ArgumentError, :cannot_remove_feed_while_processing if feed.status == "processing"
          if feed.destroy
            feed.clear_cache
            status = true
          else
            raise ArgumentError, feed.errors.full_messages.to_sentence
          end
        rescue => e
          status = false
          error = e.message
        end
      end

      {status: status, error: error, feed: feed}
    end
  end
end
