module Mutations
  class CreateCustomShopifyCheckout < BaseMutation
    argument :price, Float, required: true
    argument :variant_limit, Integer, required: true
    argument :profile_limit, Integer, required: true
    argument :schedule_limit, Integer, required: true
    argument :annually, <PERSON><PERSON><PERSON>, required: false, default_value: false

    field :status, <PERSON><PERSON>an, null: false
    field :error, String, null: true
    field :log, String, null: true # return shopify admin checkout link

    def resolve(price:, variant_limit:, profile_limit:, schedule_limit:, annually:)
      status = true
      error = ""
      confirmation_url = nil

      begin
        if price.to_f < 25
          raise ArgumentError, :custom_plan_price_below_25
        end

        is_custom_valid = CustomPlanValidator.check(
          price: price,
          variant_limit: variant_limit,
          profile_limit: profile_limit,
          schedule_limit: schedule_limit
        )

        if !is_custom_valid
          raise GraphQL::ExecutionError, :custom_plan_invalid
        end

        total_schedule_per_day = 24 / schedule_limit
        package_attributes = {
          key: "Custom Plan #{current_shop.shopify_domain}_#{Time.now.to_i}", # key needs to be unique in order to be able to retrieve later on
          price: price,
          limit: variant_limit,
          source_limit: profile_limit,
          min_hour: total_schedule_per_day,
          schedule: "min #{total_schedule_per_day} hours",
          version: nil,
          published: false,
          is_custom_plan: true
        }

        package = Plan.create(package_attributes)
        charge = ShopifyAPI::LightGraphQL.create_subscription(current_shop.shopify_domain, current_shop.shopify_token, current_shop.public_token, package, current_shop.remaining_trial_days, annually, true, current_shop.affiliate)

        if charge&.has_key?("confirmationUrl")
          if current_shop.try(:id) && charge.has_key?("id")
            Rails.cache.write(CacheKey.charge_id(current_shop.id), charge["id"])
          end
          confirmation_url = charge["confirmationUrl"]
        else
          raise GraphQL::ExecutionError, :custom_plan_invalid
        end
      rescue => e
        status = false
        error = e.message
        confirmation_url = nil
      end
      {
        status: status,
        error: error,
        log: confirmation_url
      }
    end
  end
end
