module Mutations
  class RunNowSample < BaseMutation
    argument :feed_id, Integer, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: false
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:)
      status = true
      error = ""
      process_limit = 3

      find_feed(feed_id) do |feed|
        begin
          check_subscription_plan(current_shop, {feed_type: feed.feed_type, feed: feed, skip_check_limit: true})
          raise ArgumentError, :configure_field_mapping_before_proceed if !feed.has_minimum_sync_fields?

          if feed.start? || feed.pause?
            feed.start
            feed_process_job = ProcessFeedJob.new(feed.id, trigger_by: "user", process_limit: process_limit)
            _job = Delayed::Job.enqueue(feed_process_job, queue: "local_main_jobs", priority: 1)
            feed.queuing
          end
        rescue => e
          feed = nil
          status = false
          error = e.message
        end

        return {status: status, error: error, feed: feed}
      end
    end
  end
end
