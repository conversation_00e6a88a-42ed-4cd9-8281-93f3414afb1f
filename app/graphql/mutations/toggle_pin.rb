module Mutations
  class TogglePin < BaseMutation
    argument :feed_id, Integer, required: true
    argument :to_pin, <PERSON><PERSON><PERSON>, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: false
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:, to_pin:)
      status = true
      error = ""
      feed = current_shop.user_profiles.with_deleted.find(feed_id)

      begin
        # unpin -> pin
        if to_pin
          raise ArgumentError, :invalid_operation if feed.pinned?
          raise ArgumentError, :pin_feeds_exceeds_limit if current_shop.user_profiles.where(pinned: true).count >= 5
        end

        raise ArgumentError, :error_while_updating_feed unless feed.update(pinned: to_pin)
      rescue => e
        status = false
        feed = nil
        error = e.message
      end

      {status: status, error: error, feed: feed}
    end
  end
end
