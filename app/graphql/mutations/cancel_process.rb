module Mutations
  class CancelProcess < BaseMutation
    argument :feed_id, Integer, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:)
      status = true
      error = ""

      find_feed(feed_id) do |feed|
        begin
          if feed.cancel_process("user GraphQL")
            current_shop.enabled_profiles
          else
            raise ArgumentError, :please_retry
          end
        rescue => e
          status = false
          error = e.message
        end
        return {status: status, error: error, feed: feed}
      end
    end
  end
end
