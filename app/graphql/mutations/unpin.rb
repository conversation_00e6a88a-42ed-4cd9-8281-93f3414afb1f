module Mutations
  class Unpin < BaseMutation
    argument :feed_id, Integer, required: true
    field :status, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :feed, Types::UserProfileType, null: true
    def resolve(feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      errors = []
      begin
        raise ArgumentError, :error_while_updating_feed unless feed.update(pinned: false)
      rescue => e
        status = false
        feed = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        feed: feed
      }
    end
  end
end
