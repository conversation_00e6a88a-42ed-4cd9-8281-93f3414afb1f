module Mutations
  class BaseMutation < GraphQL::Schema::Mutation
    def current_shop
      context[:current_shop]
    end

    def find_feed(feed_id)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      if feed
        yield feed if block_given?
      else
        raise GraphQL::ExecutionError.new("feed_not_found")
      end
    end

    def check_subscription_plan(shop, args)
      skip_check_limit = args.fetch(:skip_check_limit, false)
      feed = args.fetch(:feed, nil)
      feed_type = args.fetch(:feed_type)
      return nil if feed&.fast_track
      unless skip_check_limit
        raise GraphQL::ExecutionError, :exceed_import_limit if feed_type == "import" && !shop.sufficient_credits?
      end
      raise GraphQL::ExecutionError, :trial_expired unless shop.is_valid?(feed_type)
      if shop.package == Settings.first_plan && shop.charge_id
        # handling confuse user who think <PERSON><PERSON><PERSON> can do update and import
        if feed
          unless feed.fast_track
            if feed_type == "import"
              raise GraphQL::ExecutionError, :snappy_plan_run_import_feed
            elsif feed_type == "update"
              raise GraphQL::ExecutionError, :snappy_plan_run_update_feed
            end
          end
        end
      end
    end
  end
end
