module Mutations
  class RunNow < BaseMutation
    argument :feed_id, Integer, required: true
    argument :preview, <PERSON><PERSON><PERSON>, required: false
    argument :force_start, <PERSON><PERSON><PERSON>, required: false

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: false
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:, preview: false, force_start: false)
      status = true
      error = ""

      find_feed(feed_id) do |feed|
        begin
          check_subscription_plan(current_shop, {feed_type: feed.feed_type, feed: feed})
          raise ArgumentError, :configure_field_mapping_before_proceed if !feed.has_minimum_sync_fields?

          if feed.feed_type == "remove" && feed.source_type == "feed"
            update_feed = UserProfile.find(feed.source_url)
            key_mismatch = feed.mismatched_key_with?(update_feed)
            if key_mismatch
              feed.update(
                shopify_product_key: update_feed.shopify_product_key,
                variant_key_1: update_feed.variant_key_1,
                variant_key_2: update_feed.variant_key_2,
                variant_key_3: update_feed.variant_key_3
              )
            end
          end

          if feed.start? || feed.pause? || force_start == true
            feed.update(checksum: nil, checksum_file_size: nil)
            # TODO: wip only test estimated completion time for non-shopify platform
            Delayed::Job.enqueue(EstimateCompletionTimeJob.new(feed.id), queue: "ai_prediction_jobs", signature: "feed_completion_time_#{feed.id}", priority: 2) if current_shop.provider != "shopify"
            if feed.create_webhook_job(current_shop: current_shop, preview: preview) == false
              feed_process_job = ProcessFeedJob.new(feed.id, trigger_by: "user")
              _job = Delayed::Job.enqueue(feed_process_job, queue: feed.job_queue, priority: 1)
            end
            feed.queuing
          else
            raise ArgumentError, :feed_being_processed
          end
        rescue => e
          feed = nil
          status = false
          error = e.message
        end

        return {status: status, error: error, feed: feed}
      end
    end
  end
end
