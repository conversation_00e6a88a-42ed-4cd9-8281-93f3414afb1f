module Mutations
  class TransformProductLog < BaseMutation
    argument :feed_id, Integer, required: true
    argument :payload, GraphQL::Types::JSON, required: true

    field :log, Types::ProductLogType, null: false

    # Transform json to product log type for frontend use (nothing being saved here)
    def resolve(feed_id:, payload:)
      payload = permit_payload(payload)
      attribute = payload.to_h

      log = ProductLog.new(attribute)
      log.user_profile_id = feed_id
      {log: log}
    end

    private

    def permit_payload(params)
      params.permit(:id, :status, :total_store_skus, :number_product_updated, :number_product_update_failed, :total_left_over_ids, :remark, :remark_values, :cache, :trigger_by, :created_at, :updated_at, :undo_at, :partial, :file_name, :actual_product_updated, :resumed, :number_of_product_published, :number_of_product_hidden, :extra_params)
    end
  end
end
