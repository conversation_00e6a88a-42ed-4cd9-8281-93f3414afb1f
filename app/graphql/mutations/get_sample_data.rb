module Mutations
  class GetSampleData < BaseMutation
    argument :feed_id, Integer, required: true
    # ? relook at this file type
    argument :file, String, required: false
    # ? why file_format needed as params in legacy code
    argument :file_format, String, required: false
    field :status, Bo<PERSON>an, null: false
    field :errors, [String], null: false
    field :message, String, null: true
    def resolve(feed_id:, file: nil, file_format: nil)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      message = nil
      errors = []
      begin
        file_status = false
        job_queue = "feed_sample_jobs"
        begin
          sample_data = feed.get_sample_data_from_cache
          if sample_data.empty?
            if file.present?
              new_file = ActionDispatch::Http::UploadedFile.new(file)
              feed.source_file = new_file
              if feed.save && feed.source_file
                file_status = true
              end
            end
            feed_sample_job = ScanFeedSampleJob.new(feed.id, {file: file_status})
            Delayed::Job.where(queue: job_queue, user_profile_id: feed.id).delete_all
            Delayed::Job.enqueue(feed_sample_job, queue: job_queue, priority: 1)
          end
          message = "loading_sample_data"
        rescue => e
          # Airbrake.notify(e, message: "Get Sample Feed Data error: profile.id - #{feed.id}, #{e.message}")
          status = false
          message = nil
          errors = [e.message]
        end
      rescue => e
        status = false
        message = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        message: message
      }
    end
  end
end
