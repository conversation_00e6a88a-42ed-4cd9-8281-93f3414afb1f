module Mutations
  class UninstallWoocommerceUser < BaseMutation
    field :status, <PERSON><PERSON>an, null: false
    field :errors, [String], null: false
    def resolve
      status = true
      errors = []
      begin
        current_shop.process_uninstall_event
        current_shop.api_status = "inactive"
        current_shop.save(validate: false)
      rescue => e
        status = false
        errors = [e.message]
      end
      {
        status: status,
        errors: errors
      }
    end
  end
end
