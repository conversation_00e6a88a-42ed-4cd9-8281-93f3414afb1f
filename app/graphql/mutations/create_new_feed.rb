module Mutations
  class CreateNewFeed < BaseMutation
    argument :feed_type, String, required: true
    argument :supplier_id, String, required: false
    argument :feed_name, String, required: false

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true

    NON_UPDATE_FEEDS = ["import", "remove"]

    def resolve(feed_type:, supplier_id: nil, feed_name: nil)
      feed = nil
      webhook_mode = (current_shop.provider == "shopify") # && NON_UPDATE_FEEDS.include?(feed_type)

      all_profiles_of_type = NON_UPDATE_FEEDS.include?(feed_type) ? current_shop.user_profiles.where(fast_track: false, feed_type: NON_UPDATE_FEEDS) : current_shop.user_profiles.where(fast_track: false, feed_type: "update")
      profiles_count = all_profiles_of_type.count

      io_mode = "in"
      if feed_type === "export"
        feed_type = "update"
        io_mode = "out"
      end

      begin
        if supplier_id.blank?
          feed_params = {
            feed_type: feed_type,
            profile_name: feed_name,
            webhook_mode: webhook_mode
          }
          feed = current_shop.user_profiles.new(feed_params)
          feed.check_shopify

          if feed.save
            feed = current_shop.user_profiles.with_deleted.find(feed.id)
            {status: true, error: nil, feed: feed}
          else
            raise ArgumentError.new("create_feed_failed")
          end
        else

          supplier = Supplier.find(supplier_id)
          feeds = supplier.feed_templates.where(feed_type: feed_type, io_mode: io_mode).each_with_index.map do |template, index|
            # skip if template not enabled for particular platform
            next if template.supported_providers["on_#{current_shop.provider}"] == false

            feed = template.create_profile_from_template(user: current_shop)
            feed_name = [feed.profile_name, profiles_count += 1].join(" ") if feed_name.blank?

            feed_attrs = {profile_name: feed_name, webhook_mode: webhook_mode}
            feed.update(feed_attrs)
            # feed.update(profile_name: [feed.profile_name, profiles_count += 1].join(" "), webhook_mode: webhook_mode)
            feed
          end

          feed = feeds.compact.last
          if feeds.length >= 1
            {status: true, error: "no error less than 1", feed: feed}
          else
            {status: true, error: "no error more than 1", feed: feed}
          end
          # feed = current_shop.user_profiles.with_deleted.find(feed.id)
        end
      rescue => e
        {status: false, error: e.message.to_s, feed: nil}
      end
      # return { status: status, error: error, feed: feed }
    end
  end
end
