module Mutations
  class UpdateFeedName < BaseMutation
    argument :profile_name, String, required: true
    argument :feed_id, Integer, required: true
    field :status, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :feed, Types::UserProfileType, null: true
    def resolve(profile_name:, feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      errors = []
      begin
        feed.profile_name = profile_name
        feed.save(validate: false)
      rescue => e
        status = false
        feed = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        feed: feed
      }
    end
  end
end
