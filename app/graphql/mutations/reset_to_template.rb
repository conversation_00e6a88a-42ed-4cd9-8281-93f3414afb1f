module Mutations
  class ResetToTemplate < BaseMutation
    argument :feed_id, Integer, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:)
      status = true
      error = ""

      begin
        feed = current_shop.user_profiles.with_deleted.find(feed_id)
        if feed.template_id.present?
          template = FeedTemplate.find(feed.template_id)
          if template.present? && template.sync_fields.present?
            feed.sync_fields.destroy_all
            feed.update(shopify_product_key: template.shopify_product_key) unless feed.shopify_product_key == template.shopify_product_key
            template.sync_fields.order("created_at asc").each do |field|
              args = field.attributes.except(*sync_field_exclude_attributes)
              feed.sync_fields.create(args)
            end
          end
        end
      rescue => e
        status = false
        error = e.message
        feed = nil
      end

      {status: status, error: error, feed: feed}
    end

    private

    def sync_field_exclude_attributes
      %w[id user_profile_id created_at updated_at]
    end
  end
end
