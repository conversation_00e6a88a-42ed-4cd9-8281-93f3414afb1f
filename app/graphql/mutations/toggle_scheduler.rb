module Mutations
  class ToggleScheduler < BaseMutation
    argument :feed_id, Integer, required: true
    field :status, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :feed, Types::UserProfileType, null: true
    def resolve(feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      errors = []
      begin
        if feed.scheduler_enabled?
          # feed.update(scheduler_enabled: false, status: "pause")
          # feed.disable_scheduler("Manual - GQL")
          feed.deactivate("Manual - GQL", "user")
        else
          check_subscription_plan(current_shop, {feed_type: feed.feed_type, feed: feed})
          # feed.update(scheduler_enabled: true, status: "start")
          # event = Schedulable::CreateEvent.new(schedule: feed.as_schedule, trigger_by: 'enable_schedule', queue: feed.job_queue).call
          feed.activate_profile_scheduler("enable_schedule", "user")
        end
      rescue => e
        status = false
        feed = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        feed: feed
      }
    end
  end
end
