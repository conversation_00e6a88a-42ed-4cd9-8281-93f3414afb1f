module Mutations
  class ContactSupplier < BaseMutation
    argument :supplier_email, String, required: false
    argument :message, String, required: false
    field :status, Boolean, null: false
    field :errors, [String], null: false
    def resolve(**args)
      status = true
      errors = []
      begin
        current_shop.contact_supplier(args)
      rescue => e
        status = false
        errors = [e.message]
      end
      {
        status: status,
        errors: errors
      }
    end
  end
end
