module Mutations
  class DuplicateFeed < BaseMutation
    argument :feed_id, Integer, required: true
    argument :convert_to_update_feed, <PERSON><PERSON><PERSON>, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true

    def resolve(convert_to_update_feed:, feed_id:)
      status = false
      error = ""
      feed = current_shop.user_profiles.with_deleted.find(feed_id)

      if feed
        begin
          # Check for profile limit
          limit_reach_message = :reached_limit
          if feed.feed_type == "update"
            profiles_count = current_shop.user_profiles.where(fast_track: false, feed_type: "update").count
            profile_limit = current_shop.profile_limit
          elsif feed.feed_type == "import"
            limit_reach_message = :reached_limit_import
            profiles_count = current_shop.user_profiles.where(fast_track: false, feed_type: "import").count
            profile_limit = current_shop.import_profile_limit
          elsif feed.feed_type == "remove"
            limit_reach_message = :reached_limit_remove
            profiles_count = current_shop.user_profiles.where(fast_track: false, feed_type: "remove").count
            profile_limit = current_shop.remove_profile_limit
          end

          raise ArgumentError, limit_reach_message if profiles_count >= profile_limit && !convert_to_update_feed

          if convert_to_update_feed
            if current_shop.user_profiles.where(fast_track: false, feed_type: "update").count >= current_shop.profile_limit
              raise ArgumentError, :reached_limit
            else
              duplicate_feed = feed.duplicate({is_update_feed: true})
            end
          else
            duplicate_feed = feed.duplicate
          end

          status = true
        rescue => e
          status = false
          error = e.message
          duplicate_feed = duplicate_feed.present? ? duplicate_feed : feed
        end
      end

      {status: status, error: error, feed: duplicate_feed}
    end
  end
end
