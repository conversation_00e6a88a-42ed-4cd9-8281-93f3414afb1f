module Mutations
  class UndeleteFeed < BaseMutation
    argument :feed_id, Integer, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: false
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = false
      error = ""

      if feed
        begin
          if feed.restore
            current_shop.clear_cache_enable_profiles
            status = true
          else
            raise ArgumentError, feed.errors.full_messages.to_sentence
          end
        rescue => e
          status = false
          error = e.message
        end
      end

      {status: status, error: error, feed: feed}
    end
  end
end
