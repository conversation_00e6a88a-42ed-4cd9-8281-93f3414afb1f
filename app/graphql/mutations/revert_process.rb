module Mutations
  class RevertProcess < BaseMutation
    argument :product_log_id, String, required: true

    field :status, <PERSON><PERSON>an, null: false
    field :error, String, null: false
    field :data, String, null: true

    def resolve(product_log_id:)
      product_log = ProductLog.find(product_log_id)
      raise ArgumentError, "Unable to find this activity feed." if product_log.nil?

      job = product_log.create_revert_job

      {
        status: true,
        error: "",
        data: job.id
      }
    rescue => e
      {
        status: false,
        error: e.message,
        data: nil
      }
    end
  end
end
