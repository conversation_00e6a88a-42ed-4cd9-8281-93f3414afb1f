module Mutations
  class UpdateWoocommerceCredentials < BaseMutation
    argument :woocommerce_key, String, required: true
    argument :woocommerce_secret, String, required: true
    field :status, Boolean, null: false
    field :error, String, null: false
    field :shop, Types::StoreType, null: true
    def resolve(woocommerce_key:, woocommerce_secret:)
      status = true
      begin
        error = ""
        raise StandardError.new "Both fields must not be left empty" unless woocommerce_key.present? && woocommerce_secret.present?

        current_shop.woocommerce_key = woocommerce_key
        current_shop.woocommerce_secret = woocommerce_secret
        valid_user, error_message = WoocommerceClient.new(current_shop).valid_user?
        raise StandardError.new error_message.to_s unless valid_user
        current_shop.uninstalled_at = nil
        current_shop.save(validate: false)
      rescue => e
        status = false
        shop = nil
        error = e.message
      end
      {
        status: status,
        error: error,
        shop: shop
      }
    end
  end
end
