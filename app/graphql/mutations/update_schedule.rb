module Mutations
  class UpdateSchedule < BaseMutation
    argument :feed_id, Integer, required: true
    argument :attributes, Arguments::ScheduleInput, required: true

    field :status, <PERSON><PERSON>an, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true
    def resolve(attributes:, feed_id:)
      status = false
      error = nil
      find_feed(feed_id) do |feed|
        attributes = attributes.to_h
        feed.assign_attributes(attributes)

        if feed.save
          status = true
          begin
            if feed.scheduler_enabled
              feed.activate_profile_scheduler("update_schedule", "user", false)
            else
              feed.deactivate(nil, "user", false)
            end
          rescue => e
            Airbrake.notify(e, feed_id: feed_id, attributes: attributes)
            error = "An error occurred while updating the schedule."
          end
          # event = Schedulable::CreateEvent.new(schedule: feed.as_schedule, trigger_by: 'update_schedule', queue: feed.job_queue).call
        else
          error = feed.errors.full_messages.to_sentence
        end

        return {status: status, error: error, feed: feed}
      end
    end
  end
end
