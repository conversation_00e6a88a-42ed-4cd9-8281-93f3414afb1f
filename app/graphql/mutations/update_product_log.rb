module Mutations
  class UpdateProductLog < BaseMutation
    argument :product_log_id, Integer, required: true
    argument :nodejs_status, String, required: false
    argument :nodejs_message, String, required: false
    argument :kue_finish_time, String, required: false  # !DateTime
    argument :number_product_updated, Integer, required: false
    argument :number_product_update_failed, Integer, required: false
    argument :total_left_over_ids, Integer, required: false
    argument :status, <PERSON><PERSON><PERSON>, required: false
    argument :total_store_skus, Integer, required: false
    argument :remark, String, required: false
    argument :number_out_of_stocks, Integer, required: false
    argument :actual_product_updated, Integer, required: false
    argument :end_time, String, required: false # !DateTime
    field :status, Bo<PERSON>an, null: false
    field :errors, [String], null: false
    field :product_log, Types::ProductLogType, null: true
    def resolve(**args)
      product_log = ProductLog.find(args[:product_log_id])
      status = true
      errors = []
      begin
        args.delete(:product_log_id)
        args.keys.each do |key|
          product_log[key] = args[key]
        end

        raise ArgumentError, :update_failed unless product_log.save
      rescue => e
        status = false
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        product_log: product_log
      }
    end
  end
end
