module Mutations
  class CacheProductLog < BaseMutation
    argument :product_log_id, Integer, required: true
    argument :feed_id, Integer, required: true
    field :status, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :feed, Types::UserProfileType, null: true
    def resolve(product_log_id:, feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      errors = []
      begin
        last_log = feed.product_logs.where("end_time is not null").order("created_at desc").first
        if last_log.present? && last_log.status && last_log.created_at + 3.seconds < feed.updated_at
          new_log = last_log.dup
          new_log.remark = "by nodejs"
          new_log.download_id = last_log.download_id
          new_log.cache = true
          new_log.save
        end

        ProductLog.find(product_log_id).destroy
      rescue => e
        status = false
        feed = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        feed: feed
      }
    end
  end
end
