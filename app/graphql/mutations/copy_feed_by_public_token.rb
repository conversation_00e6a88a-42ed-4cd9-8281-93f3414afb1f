module Mutations
  class CopyFeedByPublicToken < BaseMutation
    argument :public_token, String, required: true
    argument :feed_id, Integer, required: true

    field :status, Bo<PERSON>an, null: false
    field :error, String, null: false

    def resolve(public_token:, feed_id:)
      status = false
      error = ""

      store = User.where(public_token: public_token).first

      if store
        find_feed(feed_id) do |feed|
          feed.duplicate({user: store})
          status = true
        rescue => e
          status = false
          error = e.message
        end
      else
        error = "Store not found"
      end

      {status: status, error: error}
    end
  end
end
