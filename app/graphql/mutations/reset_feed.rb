module Mutations
  class ResetFeed < BaseMutation
    argument :feed_id, Integer, required: true
    argument :supplier_id, String, required: true
    argument :remove_existing_mapping, <PERSON><PERSON><PERSON>, required: false, default_value: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true

    def resolve(feed_id:, supplier_id:, remove_existing_mapping:)
      status = true
      error = ""

      begin
        feed = current_shop.user_profiles.with_deleted.find(feed_id)
        template = Supplier.find(supplier_id).feed_templates.where(feed_type: feed.feed_type).last

        if feed.present? && template.present?
          template_data = template.return_profile_from_template(user: current_shop)

          if remove_existing_mapping
            # Remove all existing sync fields and assign template's sync fields to feed
            feed.sync_fields.delete_all
            template_data.sync_fields.each do |field|
              args = field.attributes.except(*sync_field_exclude_attributes)
              feed.sync_fields.new(args)
            end

            feed.assign_attributes(template_data.attributes.except(*profile_exclude_attributes))
            feed.save
          else
            # Only update source type
            feed.update(source_type: template_data.source_type, template_id: template_data.template_id)
          end

        end
      rescue => e
        status = false
        error = e.message
        feed = nil
      end

      {status: status, error: error, feed: feed}
    end

    private

    def profile_exclude_attributes
      %w[id created_at updated_at profile_name email received_email match_subject]
    end

    def sync_field_exclude_attributes
      %w[id user_profile_id created_at updated_at]
    end
  end
end
