module Mutations
  class RevokeAccess < BaseMutation
    argument :provider, String, required: true

    field :status, <PERSON><PERSON>an, null: false
    field :errors, [String], null: false
    def resolve(provider:)
      status = true
      errors = []
      begin
        client = "#{provider.titleize}Client".constantize.new(current_shop)
        client.revoke_access
      rescue => e
        status = false
        errors = [e.message]
      end
      {
        status: status,
        errors: errors
      }
    end
  end
end
