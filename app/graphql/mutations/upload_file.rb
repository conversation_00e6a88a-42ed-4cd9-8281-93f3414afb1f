module Mutations
  class UploadFile < BaseMutation
    argument :feed_id, Integer, required: true
    argument :process_now, <PERSON><PERSON><PERSON>, required: true, default_value: false
    argument :run_as_sample, <PERSON><PERSON><PERSON>, required: true, default_value: false
    argument :file, type: ApolloUploadServer::Upload, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :feed, Types::UserProfileType, null: true

    # `file` is a ApolloUploadServer::Wrappers::UploadedFile object
    def resolve(process_now:, feed_id:, file:, run_as_sample:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      error = nil
      process_limit = 3

      begin
        check_subscription_plan(current_shop, {feed_type: feed.feed_type, feed: feed, skip_check_limit: run_as_sample})

        if process_now && !feed.has_minimum_sync_fields?
          raise ArgumentError, "configure_field_mapping_before_proceed"
        end

        feed.upload_origin = ENV["ORIGIN_SERVER"] if ENV["ORIGIN_SERVER"]
        feed.source_file = file
        if feed.save && feed.source_file
          if process_now
            if (run_as_sample === true) || (feed.create_webhook_job(current_shop: current_shop) == false)
              feed_process_job = (run_as_sample === true) ? ProcessFeedJob.new(feed.id, trigger_by: "user", process_limit: process_limit) : ProcessFeedJob.new(feed.id, trigger_by: "user")
              _job = Delayed::Job.enqueue(feed_process_job, queue: (run_as_sample === true) ? "local_main_jobs" : feed.job_queue, priority: 1)
            end
            feed.queuing
          end
        else
          raise ArgumentError, feed.errors.full_messages.to_sentence
        end
      rescue => e
        status = false
        feed = nil
        error = e.message
      end

      {status: status, error: error, feed: feed}
    end
  end
end
