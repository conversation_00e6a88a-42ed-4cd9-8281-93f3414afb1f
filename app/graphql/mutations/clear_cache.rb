module Mutations
  class ClearCache < BaseMutation
    argument :feed_id, Integer, required: true
    field :status, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :message, String, null: true
    def resolve(feed_id:)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      status = true
      message = nil
      errors = []
      begin
        feed.checksum = nil
        if feed.save
          message = (feed.source_type == "email") ? :please_proceed_email : :please_proceed
        else
          raise ArgumentError, :please_contact_stock_sync
        end
      rescue => e
        status = false
        message = nil
        errors = [e.message]
      end
      {
        status: status,
        errors: errors,
        message: message
      }
    end
  end
end
