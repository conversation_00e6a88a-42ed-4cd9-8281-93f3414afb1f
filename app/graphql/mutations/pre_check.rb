module Mutations
  class PreCheck < BaseMutation
    argument :feed_type, String, required: true

    field :status, <PERSON><PERSON>an, null: false
    field :error, String, null: true

    def resolve(feed_type:)
      status = true
      error = ""

      begin
        check_subscription_plan(current_shop, {
          feed_type: feed_type,
          skip_check_limit: true
        })

        profiles_count = current_shop.user_profiles.where(fast_track: false, feed_type: feed_type).count
        if feed_type == "update"
          limit_reach_message = "reached_limit"
          profile_limit = current_shop.profile_limit
        elsif feed_type == "import"
          limit_reach_message = "reached_limit_import"
          profile_limit = current_shop.import_profile_limit
        elsif feed_type == "remove"
          limit_reach_message = "reached_limit_remove"
          profile_limit = current_shop.remove_profile_limit
        end

        raise ArgumentError.new(limit_reach_message) if profiles_count >= profile_limit
      rescue => e
        status = false
        error = e.message
      end
      {status: status, error: error}
    end
  end
end
