module Mutations
  class CreateStripeLinkForCredits < BaseMutation
    argument :total_credits, Integer, required: true

    field :status, <PERSON><PERSON>an, null: false
    field :error, String, null: true
    field :log, String, null: true

    def resolve(total_credits:)
      status = true
      error = ""

      if !total_credits.to_i.between?(Settings.credit_tier.min_allowable, Settings.credit_tier.max_allowable)
        return {status: false, error: "min_credits_required", log: ""}
      end

      begin
        url = current_shop.create_flexi_charge(total_credits)["url"]
      rescue => e
        status = false
        error = e.message
      end

      {status: status, error: error, log: url}
    end
  end
end
