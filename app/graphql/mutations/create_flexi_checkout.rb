module Mutations
  class CreateFlexiCheckout < BaseMutation
    argument :import_limit, Integer, required: true

    field :status, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true
    field :log, String, null: true

    def resolve(import_limit:)
      confirmation_url = nil
      status = true
      error = ""
      begin
        if !import_limit.between?(Settings.credit_tier.min_allowable, Settings.credit_tier.max_allowable)
          raise GraphQL::ExecutionError, :credit_not_within_boundary
        end

        charge = current_shop.create_flexi_charge(import_limit)

        if charge.instance_of?(Stripe::Checkout::Session)
          Rails.cache.write(CacheKey.flexi_charge_id(current_shop.id), charge["id"]) if current_shop.id && charge["id"]
          confirmation_url = charge["url"]
        elsif charge.is_a?(Hash)
          Rails.cache.write(CacheKey.flexi_charge_id(current_shop.id), charge.dig("appPurchaseOneTime", "id")) if current_shop.id && charge.dig("appPurchaseOneTime", "id")
          confirmation_url = charge["confirmationUrl"]
        end

        if confirmation_url.blank?
          raise GraphQL::ExecutionError, :credit_purchase_invalid
        end
      rescue => e
        status = false
        error = e.message
        confirmation_url = nil
      end
      {
        status: status,
        error: error,
        log: confirmation_url
      }
    end
  end
end
