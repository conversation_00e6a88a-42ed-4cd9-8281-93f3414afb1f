module Mutations
  class CreateStripeCheckout < BaseMutation
    argument :plan_name, String, required: true
    argument :is_yearly, <PERSON><PERSON><PERSON>, required: false, default_value: false

    field :status, <PERSON><PERSON>an, null: false
    field :error, String, null: true
    field :log, String, null: true

    def resolve(plan_name:, is_yearly:)
      status = true
      error = ""

      if plan_name == "Free"
        current_shop.subscribe_to_free_plan
        return {status: status, error: error, log: Settings.hostname}
      end

      # Find Stripe plan
      stripe_plans = list_all_stripe_plans[is_yearly ? :yearly : :monthly]
      plan_key = convert_to_downcase(plan_name)

      begin
        plan_found = stripe_plans.detect { |plan| /#{plan_key}$/.match?(convert_to_downcase(plan.nickname)) }
        raise StandardError.new("Plan #{plan_name} not found") unless plan_found

        # Create checkout URL
        payload = {
          success_url: File.join(Settings.hostname, "/billing?payment_success=true"),
          cancel_url: File.join(Settings.hostname, "/billing?payment_success=false"),
          payment_method_types: ["card"],
          mode: "subscription",
          line_items: [{
            quantity: 1,
            price: plan_found.id
          }],
          subscription_data: {
            metadata: {
              shop_id: current_shop.id,
              provider: current_shop.provider,
              plan_name: plan_name
            }
          }
        }
        payload[:customer] = current_shop&.stripe_customer_id
        session = Stripe::Checkout::Session.create(payload)
        url = session.url
      rescue => e
        status = false
        error = e.message
      end

      {status: status, error: error, log: url}
    end

    private

    def list_all_stripe_plans
      Rails.cache.fetch("stripe_subscription_plans") do
        all_plans = Stripe::Plan.list({limit: 100, product: Settings.stripe.product_id}).data
        monthly_plans = all_plans.select { |plan| plan.interval == "month" }
        yearly_plans = all_plans - monthly_plans
        {monthly: monthly_plans, yearly: yearly_plans}
      end
    end

    def convert_to_downcase(value)
      value.strip.gsub(/[[:space:]]/, "").downcase.delete("+") # remove whitespace and remove + symbol
    end
  end
end
