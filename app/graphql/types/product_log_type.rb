module Types
  class ProductLogType < BaseObject
    description "Activity Log"
    include ActionView::Helpers::DateHelper

    field :id, ID, null: false
    field :status, Boolean, null: false
    field :total_store_skus, Integer, null: true
    field :percentage_matched, String, null: false
    field :number_product_updated, Integer, null: true
    field :number_product_update_failed, Integer, null: false
    field :total_left_over_ids, Integer, null: false
    field :total_out_of_stock, Integer, null: true
    field :remark, String, null: true
    field :remark_values, GraphQL::Types::JSON, null: true
    field :download_links, GraphQL::Types::JSON, null: true
    field :cache, Boolean, null: false
    field :trigger_by, String, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: true
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: true
    field :undo_at, GraphQL::Types::ISO8601DateTime, null: true
    field :partial, Boolean, null: false
    field :time_taken, String, null: true
    field :file_name, String, null: true
    field :import_tag_used, String, null: true
    field :merge_tag_used, String, null: true
    field :actual_product_updated, Integer, null: false
    field :actual_product_retry_updated, Integer, null: true
    field :resumed, Boolean, null: false
    field :number_of_product_published, Integer, null: false
    field :number_of_product_hidden, Integer, null: false
    field :extra_params, GraphQL::Types::JSON, null: true
    field :error_list, String, null: true
    field :remaining_skus_to_process, Integer, null: false
    field :total_feed_filtered_rows, Integer, null: false
    field :total_feed_rows, Integer, null: false
    field :is_reverting_import, Boolean, null: true
    field :end_time, GraphQL::Types::ISO8601DateTime, null: true

    def serve_s3_link_for(file, disk_name = nil)
      File.join(Settings.hostname, "user_profiles", "download_s3_link?profile_id=#{object.user_profile_id}&log_id=#{object.id}&file=#{file}&name=#{disk_name || file}")
    end

    def time_taken
      if object.start_time && object.end_time
        distance_of_time_in_words(object.start_time, object.end_time)
      else
        ""
      end
    end

    def download_links
      {
        "skus_not_found_in_feed" => serve_s3_link_for(ProductUpdateStatus::FILE_UNMATCHED),
        "skus_not_found_in_store" => serve_s3_link_for(ProductUpdateStatus::FILE_FAILED_PRODUCT, "not_in_store.csv"),
        "update_logs" => serve_s3_link_for(ProductUpdateStatus::FILE_UPDATED_INFO, "change_logs.csv"),
        "out_of_stock" => serve_s3_link_for(ProductUpdateStatus::FILE_OUTOFSTOCK, "low_stock.csv"),
        "back_in_stock" => serve_s3_link_for(ProductUpdateStatus::FILE_BACKINSTOCK, "back_in_stock.csv"),
        "product_status" => serve_s3_link_for(ProductUpdateStatus::FILE_PRODUCT_STATUS, "product_status.csv"),
        "error_list" => serve_s3_link_for(ProductUpdateStatus::FILE_ERROR_LIST),
        "imported_logs" => serve_s3_link_for(ProductImportStatus::FILE_IMPORTED, "change_logs.csv"),
        "remove_logs" => serve_s3_link_for(ProductRemoveStatus::FILE_DELETED)
      }
    end

    def parsed_remark_values
      return object.remark_values unless object.remark_values.is_a?(String)

      begin
        JSON.parse(object.remark_values)
      rescue
        object.remark_values
      end
    end

    def remaining_skus_to_process
      remark_values = parsed_remark_values
      return 0 if object.remark.blank? || remark_values.blank? || remark_values.is_a?(String)
      return remark_values["remaining"] if remark_values.has_key?("remaining") # import
      return remark_values["fail_count"] if remark_values.has_key?("fail_count") # import
      return remark_values["limit_skus"] if remark_values.has_key?("limit_skus") # update

      0
      # return remark_values
    end

    def is_reverting_import
      return false if object.undo_at.present?
      return false unless object.user_profile.is_import_type?
      return false if object.created_at < 14.days.ago
      Delayed::Job.exists?(product_log_id: object.id)
    end
  end
end
