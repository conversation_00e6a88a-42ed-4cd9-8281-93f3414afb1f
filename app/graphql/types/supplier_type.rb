module Types
  class SupplierType < BaseObject
    description "Supplier"

    field :id, ID, null: false
    field :name, String, null: true
    field :email, String, null: true
    field :url, String, null: true
    field :notes, String, null: true
    field :redirect_to, String, null: true
    field :popular, Bo<PERSON>an, null: false
    field :description, String, null: true
    field :supplier_type, String, null: true
    field :country_code, String, null: true
    field :categories, [String], null: true
    field :image_url, String, null: true
  end
end
