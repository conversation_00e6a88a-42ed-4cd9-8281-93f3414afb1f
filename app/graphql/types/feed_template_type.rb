module Types
  class FeedTemplateType < BaseObject
    description "Feed Template"

    field :export_templates, [SupplierType], null: true
    field :import_templates, [SupplierType], null: true
  end

  def export_templates
    object.where("user_profiles.io_mode = ?", "out").order("name asc")
  end

  def import_templates
    suppliers.where("user_profiles.io_mode = ?", "in").order("name asc")
  end
end
