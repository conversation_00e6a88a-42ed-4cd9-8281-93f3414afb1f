module Types
  class BaseObject < GraphQL::Schema::Object
    edge_type_class(Types::BaseEdge)
    connection_type_class(Types::BaseConnection)
    field_class Types::BaseField

    def current_shop
      context[:current_shop]
    end

    def is_impersonating_admin?
      context[:is_impersonating_admin].present?
    end

    def authenticate!
      raise GraphQL::ExecutionError.new("unauthorized", options: {status: :unauthorized, code: 401}) unless context[:current_shop].present?
    end

    def find_feed(feed_id)
      feed = current_shop.user_profiles.with_deleted.find(feed_id)
      if feed
        yield feed if block_given?
      else
        raise GraphQL::ExecutionError.new("feed_not_found")
      end
    end
  end
end
