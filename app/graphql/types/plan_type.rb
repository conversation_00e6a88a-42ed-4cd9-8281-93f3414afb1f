module Types
  class PlanType < BaseObject
    description "Pricing Plan"

    field :id, ID, null: false
    field :key, String, null: false
    field :price, Float, null: true
    field :limit, Integer, null: true
    field :source_limit, Integer, null: true
    field :import_limit, Integer, null: true
    field :import_source_limit, Integer, null: true
    field :schedule, String, null: true
    field :min_hour, Integer, null: true
    field :popular, <PERSON><PERSON><PERSON>, null: false
    field :monthly_credit_plan, <PERSON><PERSON><PERSON>, null: false
    field :version, String, null: true
    field :monthly_credit_load, Integer, null: false
    field :hidden, <PERSON><PERSON><PERSON>, null: false
    field :is_custom_plan, <PERSON><PERSON><PERSON>, null: false
    field :schedule_per_day, Integer, null: false

    def schedule_per_day
      object.schedule_per_day
    end

    def hidden
      ["Trial"].include?(object.key)
    end
  end
end
