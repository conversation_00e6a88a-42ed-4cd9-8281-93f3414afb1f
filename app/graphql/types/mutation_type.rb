module Types
  class MutationType < Types::BaseObject
    # ? users: [UserType]
    field :update_user, mutation: Mutations::UpdateUser
    field :contact_supplier, mutation: Mutations::ContactSupplier
    field :create_ticket, mutation: Mutations::CreateTicket
    field :update_woocommerce_credentials, mutation: Mutations::UpdateWoocommerceCredentials
    field :pre_check, mutation: Mutations::PreCheck
    field :uninstall_woocommerce_user, mutation: Mutations::UninstallWoocommerceUser
    field :revoke_access, mutation: Mutations::RevokeAccess

    # ? feeds: [UserProfileType]
    field :create_new_feed, mutation: Mutations::CreateNewFeed
    field :update_feed_name, mutation: Mutations::UpdateFeedName
    field :duplicate_feed, mutation: Mutations::DuplicateFeed
    field :update_schedule, mutation: Mutations::UpdateSchedule
    field :toggle_scheduler, mutation: Mutations::ToggleScheduler
    field :run_now, mutation: Mutations::RunNow
    field :upload_file, mutation: Mutations::UploadFile
    field :clear_cache, mutation: Mutations::ClearCache
    field :cancel_process, mutation: Mutations::CancelProcess
    field :delete_feed, mutation: Mutations::DeleteFeed
    field :cache_product_log, mutation: Mutations::CacheProductLog
    field :toggle_pin, mutation: Mutations::TogglePin
    field :run_now_sample, mutation: Mutations::RunNowSample
    field :reset_feed, mutation: Mutations::ResetFeed
    field :reset_to_template, mutation: Mutations::ResetToTemplate
    field :undelete_feed, mutation: Mutations::UndeleteFeed
    field :create_remove_feed, mutation: Mutations::CreateRemoveFeed
    field :update_preview_mode, mutation: Mutations::UpdatePreviewMode

    # ? feed_settings: [FeedSettingsType]
    field :update_feed_source, mutation: Mutations::UpdateFeedSource
    field :update_feed_settings, mutation: Mutations::UpdateFeedSettings
    field :test_connection, mutation: Mutations::TestConnection
    field :get_sample_data, mutation: Mutations::GetSampleData
    field :copy_feed_by_public_token, mutation: Mutations::CopyFeedByPublicToken

    # ? product_logs: [ProductLogType]
    field :revert_process, mutation: Mutations::RevertProcess
    field :update_product_log, mutation: Mutations::UpdateProductLog
    field :transform_product_log, mutation: Mutations::TransformProductLog

    # billing
    field :create_flexi_checkout, mutation: Mutations::CreateFlexiCheckout
    field :create_shopify_checkout, mutation: Mutations::CreateShopifyCheckout
    field :create_custom_shopify_checkout, mutation: Mutations::CreateCustomShopifyCheckout
    field :create_wix_checkout, mutation: Mutations::CreateWixCheckout
    field :create_stripe_checkout, mutation: Mutations::CreateStripeCheckout
    field :create_stripe_link_for_credits, mutation: Mutations::CreateStripeLinkForCredits
    field :create_custom_stripe_checkout_link, mutation: Mutations::CreateCustomStripeCheckoutLink

    # Admin-related mutations
    field :admin_process_feed, mutation: Mutations::Admin::ProcessFeed
    field :admin_process_via_webhook, mutation: Mutations::Admin::ProcessViaWebhook
    field :admin_copy_profile_to_store, mutation: Mutations::Admin::CopyProfileToStore
    field :admin_update_profile, mutation: Mutations::Admin::UpdateProfile
    field :admin_clear_profile_cache, mutation: Mutations::Admin::ClearProfileCache
    field :admin_check_process_cache_key, mutation: Mutations::Admin::CheckProcessCacheKey
    field :admin_create_template, mutation: Mutations::Admin::CreateTemplate
  end
end
