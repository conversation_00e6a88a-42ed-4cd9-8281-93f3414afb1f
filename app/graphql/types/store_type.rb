module Types
  class StoreType < BaseObject
    description "Store"
    # ? check for bigint, text, datetime field
    # Types::UserType::ISO8601DateTime

    field :reach_email, String, null: true
    field :shopify_domain, String, null: false
    field :installed_date, GraphQL::Types::ISO8601DateTime, null: true
    field :status, String, null: false
    field :limit_skus, Integer, null: false
    field :package, String, null: false
    field :import_package, String, null: true
    field :profile_limit, Integer, null: true
    field :import_limit_skus, Integer, null: true
    field :import_profile_limit, Integer, null: false
    field :remove_profile_limit, Integer, null: false
    field :timezone, String, null: false
    field :total_skus_count, Integer, null: true
    field :full_plan_name, String, null: true
    field :min_hour, Integer, null: false
    field :import_min_hour, Integer, null: false
    field :notification_email, String, null: true
    field :configs, String, null: true
    field :id, GraphQL::Types::BigInt, null: false
    field :public_token, String, null: true
    field :auto_get_higher_qty, Boolean, null: false
    field :locale, String, null: false
    field :close_feedback, Boolean, null: false
    field :welcome_toast_hash, String, null: true
    field :email_subscriptions, [String], null: false
    field :customer_email, String, null: true
    field :shopify_staff, Boolean, null: true
    field :is_expired, Boolean, null: true
    field :charge_period, String, null: true
    field :seen_first_usage_modal, Boolean, null: false
    field :primary_location_id, GraphQL::Types::BigInt, null: true
    field :user_using_whitelisting_ip_address, String, null: true
    field :affiliate, String, null: true
    field :feeds_count_by_type, ::GraphQL::Types::JSON, null: true
    field :custom_free_trial_days, Boolean, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :charge_date, GraphQL::Types::ISO8601DateTime, null: true
    field :provider, String, null: false
    field :stripe_api, String, null: true
    field :monthly_credit, GraphQL::Types::BigInt, null: true
    field :monthly_credit_plan, Boolean, null: false
    field :total_monthly_credits, String, null: true
    field :bigcommerce_domain, String, null: true
    field :stripe_customer_id, String, null: true
    field :wix_code, String, null: true
    field :valid_woocommerce_store, Boolean, null: true
    field :allow_import_feed, Boolean, null: true
    field :has_paid_plan, Boolean, null: true
    field :warning_zero_qty_update, Integer, null: false
    field :warning_remove_products, Integer, null: false
    # field :fast_track_feed, UserProfileType, null: true
    field :stripe_past_due, String, null: true
    field :total_process, Integer, null: true
    field :enable_translation_feature, Boolean, null: false

    field :user_profiles, [UserProfileType], null: false
    field :profiles_count, Integer, null: false
    field :feed_constants, GraphQL::Types::JSON, null: false
    field :locations, GraphQL::Types::JSON, null: true
    field :sync_field_constants, GraphQL::Types::JSON, null: false
    field :trial_expired, Boolean, null: false
    field :plan_version, Integer, null: false
    field :pre_plan_version, Integer, null: true
    field :current_plan, PlanType, null: false
    field :next_billing_date, GraphQL::Types::ISO8601Date, null: true
    field :is_impersonating_admin, Boolean, null: false
    field :multi_location_enabled, Boolean, null: true
    field :bigcommerce_store_hash, String, null: true
    field :schedule_per_day, Integer, null: false
    field :latest_package_charge, Float, null: false
    field :quickbooks_realm_id, String, null: true
    field :prestashop_api_key, String, null: true
    field :is_embedded, Boolean, null: false
    field :square_location_id, String, null: true
    field :has_subscription, Boolean, null: false
    field :is_show_quick_guide, Boolean, null: false

    def user_profiles
      sorting = "pinned desc, created_at desc"

      is_impersonating_admin ?
      object.user_profiles.includes([:sync_fields])
        .includes([latest_product_log: :user_profile])
        .includes([template: :supplier])
        .with_deleted.order("deleted_at desc NULLS FIRST")
        .order(sorting) : object.user_profiles.includes([:sync_fields])
          .includes([latest_product_log: :user_profile])
          .includes([template: :supplier])
          .order(sorting)
      # return object.user_profiles.order(sorting)
    end

    def profiles_count
      object.feeds_count_by_type.values.sum
    end

    def feed_constants
      constant = {}
      Settings.ALL_FEED_SETTINGS.each do |setting|
        setting = setting.to_sym
        options = sync_field_options_for(setting)
        if setting == :rules_operators_with_keys_required
          # This loop change the key from :" <" to :< and :" >" to :>
          # Reason: Gem Config ver 5 cause an error when using "<" and ">" in settings.yml
          #         Therefore, add a space before the symbol and remove it during graphql
          options.each_with_index do |opt, idx|
            if opt[:key].to_s.start_with?(" ")
              new_key = opt[:key].to_s.strip.to_sym
              options[idx] = {key: new_key, value: opt[:value]}
            end
          end
        end
        constant[setting] = options
      end
      constant
    end

    def sync_field_constants
      SyncField.extra_attributes_with_defaut_values
    end

    def trial_expired
      object.trial_expired?
    end

    def locale
      return object.locale if ["en", "ja", "zh_cn", "german", "french", "polish", "dutch", "spanish", "tr"].include?(object.locale)
      "en"
    end

    def plan_version
      object.humanized_plan_version
    end

    def pre_plan_version
      object.humanized_pre_plan_version
    end

    def current_plan
      # ? object.plan does not work for some store eg: 55767 & 18048, it return different plan version but correct plan name
      object.plan
    end

    def profile_limit
      (object.profile_limit != current_plan.source_limit) ? object.profile_limit : current_plan.source_limit
    end

    def limit_skus
      (object.limit_skus != current_plan.limit) ? object.limit_skus : current_plan.limit
    end

    def is_impersonating_admin
      context[:is_impersonating_admin].present?
    end

    def schedule_per_day
      24 / object.min_hour
    rescue
      0
    end

    def latest_package_charge
      charge = 0
      if object.status == "active"
        charge = object.plan.price || 0
      end
      charge
    end

    def enable_translation_feature
      translation_flag = object.extra_options&.dig("enable_translation") || false
      translation_flag || (object.installed_date < DateTime.new(2024, 12, 16) || (object.package.to_s.index("Custom Plan") || object.package.to_s == "Business"))
    end

    private

    def sync_field_options_for(settings_name)
      if object.provider != "shopify"
        settings_name = :"#{object.provider}_product_key" if settings_name == :shopify_product_key
      end

      Settings[settings_name].map do |key, value|
        {
          key: key,
          value: value.instance_of?(String) ? value : JSON.parse(value.to_json)
        }
      end
    end
  end
end
