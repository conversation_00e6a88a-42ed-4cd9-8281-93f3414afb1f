module Arguments
  class ScheduleInput < ::Types::BaseInputObject
    description "Parameters for schedule update"

    argument :job_type, String, required: true
    argument :job_time, String, required: false
    argument :job_interval, String, required: false
    argument :scheduler_enabled, Boolean, required: true
    argument :use_time_range, Boolean, required: false
    argument :hourly_start_time, String, required: false
    argument :hourly_end_time, String, required: false
  end
end
