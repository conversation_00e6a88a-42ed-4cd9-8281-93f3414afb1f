ActiveAdmin.register_page "Profile Load Source V2" do
  belongs_to :user_profile

  action_item :force_refresh, only: :index do
    link_to "Force Refresh", admin_user_profile_profile_load_source_v2_path(is_force_refresh: "true"), class: "button"
  end

  content do
    div do
      data = assigns[:data]
      is_success = data[:is_success]

      if !is_success
        message = data[:message]
        div do
          message
        end
      else
        div class: "grid grid-cols-2 gap-4" do
          div do
            panel "Details" do
              attributes_table_for data do
                row :cached_at do
                  text_node(time_ago_in_words(data[:datetime]))
                  br
                  i(data[:datetime])
                end
                row :row_count do
                  data[:row_count]
                end
                row :s3_download_link do
                  link_to("Download CSV", data[:url], target: "_blank")
                end
              end
            end
            panel "First 3 rows" do
              headers = data[:headers]
              first_3_rows = data[:first_3_rows]
              div class: "overflow-x-auto w-full" do
                table class: "data-table" do
                  thead do
                    tr do
                      th("#")
                      th("Column")
                      th("1st Row")
                      th("2nd Row")
                      th("3rd Row")
                    end
                  end

                  tbody do
                    headers.each_with_index do |r, idx|
                      tr do
                        td(idx + 1)
                        td(b(r))
                        td(first_3_rows[0]&.[](idx))
                        td(first_3_rows[1]&.[](idx))
                        td(first_3_rows[2]&.[](idx))
                      end
                    end
                  end
                end
              end
            end
          end

          div do
            panel "Search Data" do
              form method: :get, action: admin_user_profile_profile_load_source_v2_path do
                attributes_table_for data do
                  row :search_column do
                    input(
                      type: "text",
                      name: "search_column",
                      id: "search_column",
                      value: params[:search_column] || assigns[:user_profile].cache_sync_fields.find { |sf| sf.field_name == "product_id" }&.field_mapping
                    )
                  end
                  row :search_value do
                    input(
                      type: "text",
                      name: "search_value",
                      id: "search_value",
                      value: params[:search_value]
                    )
                  end
                  row :"" do
                    button("Search", type: "submit", class: "button")
                  end
                end
              end
            end

            if assigns[:is_search_mode]
              h5("Search Result")
              is_search_success = assigns[:is_search_success]
              err_msg = assigns[:search_error_message]
              search_headers = assigns[:search_filtered_headers]
              search_data = assigns[:search_filtered_data]

              first_pricing_sync_field = user_profile.first_pricing_sync_field

              if !is_search_success
                div("Error : #{err_msg}")
              else
                div do
                  table class: "data-table" do
                    thead do
                      tr do
                        th("Actions")
                        th("No")
                        search_headers.each do |r|
                          th(r)
                        end
                      end
                    end

                    tbody do
                      search_data.each_with_index do |r, idx|
                        tr do
                          td style: "vertical-align: top" do
                            if first_pricing_sync_field
                              form method: :get, action: "#{admin_user_profile_profile_load_source_v2_path}#debug-panel-#{idx}" do
                                input type: :hidden, name: :search_column, value: params[:search_column]
                                input type: :hidden, name: :search_value, value: params[:search_value]
                                input type: :hidden, name: :debug_row, value: idx
                                input type: :hidden, name: :row_data, value: r.to_json
                                input type: :hidden, name: :sync_field_id, value: first_pricing_sync_field.id

                                button b("DEBUG PRICING"), type: :submit, class: "button"
                              end
                            end
                          end
                          td(idx + 1, style: "vertical-align: top")
                          r.each do |c|
                            td(c, style: "vertical-align: top")
                          end
                        end

                        if params[:debug_row].to_i == idx && assigns[:debug_data].present?
                          tr id: "debug-panel-#{idx}", class: "debug-panel" do
                            td colspan: search_headers.length + 2 do
                              div style: "max-width: 800px; margin-left: 10px;" do
                                panel "Pricing Debugger" do
                                  div style: "margin: 10px;" do
                                    form method: :get, class: "debug-pricing-form" do
                                      input type: :hidden, name: :search_column, value: params[:search_column]
                                      input type: :hidden, name: :search_value, value: params[:search_value]
                                      input type: :hidden, name: :debug_row, value: params[:debug_row]
                                      input type: :hidden, name: :row_data, value: params[:row_data]
                                      input type: :hidden, name: :previous_sync_field_id, value: params[:sync_field_id]

                                      div style: "margin-bottom: 10px; display: flex; align-items: center;" do
                                        label "Price Header: ", style: "margin-right: 10px; min-width: 120px;"
                                        span assigns[:debug_data]&.dig("data", "price_header")&.capitalize
                                      end

                                      div style: "margin-bottom: 10px;" do
                                        div style: "display: flex; align-items: center;" do
                                          label "Price Field: ", style: "margin-right: 10px; min-width: 120px;"
                                          sync_fields = user_profile.cache_sync_fields
                                            .select { |sf| sf.extra_attributes&.keys&.any? { |key| key.include?("pricing_conditions") } }

                                          selected_field_id = assigns[:debug_data]["data"]["sync_field_id"].to_s

                                          select name: :sync_field_id,
                                            style: "width: 100px;",
                                            value: selected_field_id do
                                            sync_fields.each do |sf|
                                              option value: sf.id,
                                                selected: (sf.id.to_s == selected_field_id) ? "selected" : nil do
                                                sf.field_name
                                              end
                                            end
                                          end
                                        end

                                        div style: "margin-left: 130px; margin-top: 5px;" do
                                          if assigns[:debug_data]&.dig("data", "sync_field_id").present?
                                            link_to "Edit Pricing Rules",
                                              edit_admin_user_profile_sync_field_path(
                                                user_profile_id: user_profile.id,
                                                id: assigns[:debug_data]&.dig("data", "sync_field_id")
                                              ),
                                              class: "button",
                                              style: "white-space: nowrap;",
                                              target: "_blank"
                                          end
                                        end
                                      end

                                      div style: "margin-bottom: 10px; display: flex; align-items: center;" do
                                        label "Price Value: ", style: "margin-right: 10px; min-width: 120px;"
                                        input type: :text,
                                          name: :price_value,
                                          value:  assigns[:debug_data]&.dig("data", "price_value"),
                                          style: "width: 100px;"
                                      end

                                      button b("UPDATE DEBUG"), type: :submit, class: "button", style: "font-size: 16px;"
                                    end
                                  end
                                  table_for assigns[:debug_data]&.dig("data", "conditions") do
                                    column "condition"
                                    column "formula"
                                    column "calculated_price"
                                    column "changed"
                                  end
                                end
                              end
                            end
                          end
                        end
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end

  controller do
    def index
      id = params["user_profile_id"]

      @user_profile = UserProfile.find(id)

      is_force_refresh = params["is_force_refresh"] == "true"
      cached_data = Rails.cache.read("active_admin/load_source_v2_#{id}")

      if !is_force_refresh && cached_data.present?
        @data = cached_data
      else
        job = JobApi.new(user_profile_id: id)
        response = job.load_source_v2

        if response.is_a?(Hash) && response[:status] == false
          @data = {
            is_success: false,
            message: response[:message]
          }
          return
        end

        # Explanation:
        # JobApi will call job repo SourceManager.validate_source which will perform save to cache
        # In local dev, this is unable to test unless both repo change to same source

        @data = Rails.cache.read("active_admin/load_source_v2_#{id}")
      end

      if @data.blank?
        @data = {
          is_success: false,
          message: "No data"
        }
        return
      end

      is_success = @data[:is_success]
      s3_url = @data[:url]
      file_path = @data[:file_path]
      headers = @data[:headers]

      search_column = params["search_column"].strip if params["search_column"].present?
      search_value = params["search_value"].strip if params["search_value"].present?

      begin
        if is_success && s3_url.present? && search_column.present? && search_value.present?
          @is_search_mode = true

          csv_str = S3Select.query_from_load_source_v2_log(file_path, search_value, search_column)
          csv_str = csv_str.encode("UTF-8", invalid: :replace, undef: :replace, replace: "")

          @search_filtered_headers = headers
          @search_filtered_data = CSV.new(csv_str, col_sep: "|", quote_char: "\"", row_sep: "\r\n", liberal_parsing: true).to_a
          @is_search_success = true

          if params[:debug_row].present? && params[:row_data].present? && params[:sync_field_id].present?
            row_data = JSON.parse(params[:row_data])
            debug_params = {
              sync_field_id: params[:sync_field_id],
              row_data: row_data,
              headers: headers
            }

            debug_params[:price_value] = params[:price_value] if params[:sync_field_id] == params[:previous_sync_field_id]

            @debug_data = JobApi.new(debug_params).debug_pricing
          end
        else
          @is_search_mode = false
        end
      rescue => e
        @search_error_message = e.message
        @is_search_success = false
      end
    end
  end
end
