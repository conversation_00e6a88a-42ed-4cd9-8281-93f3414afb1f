ActiveAdmin.register_page "Dashboard" do
  menu priority: 1, label: proc { I18n.t("active_admin.dashboard") }

  content title: proc { I18n.t("active_admin.dashboard") } do
    if current_account != true_account
      panel "User Impersonation" do
        params[:current_account] = current_account
        render "impersonation"
      end
    end

    panel "Running Profiles #{UserProfile.where(status: "processing").count}" do
      table_for UserProfile.where(status: "processing").order("start_time asc") do
        column :name do |profile|
          "#{profile.user.shopify_domain} #{profile.id}" if profile.user
        end
        column :pid do |profile|
          job = Delayed::Job.where(user_profile_id: profile.id).where.not(locked_at: nil).first
          if job
            job.locked_by.to_s
          elsif profile.fargate_task_id.present? && !profile.fargate_task_id.to_s.index("BulkOperation").present? && !profile.fargate_task_id.to_s.index("run local").present?
            out = "#{profile.fargate_task_definition.present? ? profile.fargate_task_definition : "Default FG"} : #{link_to(profile.fargate_task_id, profile.fargate_task_id_console_url, target: "_blank")}"
            out += " | #{link_to "Check", fargate_health_checker_admin_user_profile_url(profile)}"
            out.html_safe
          elsif profile.fargate_task_id.to_s.index("BulkOperation").present?
            "BulkOp : #{link_to(profile.fargate_task_id, query_bulk_op_admin_user_profile_url(profile), target: "_blank")}".html_safe
          else
            profile.fargate_task_id.to_s
          end
        end
        column :queue do |profile|
          profile.job_queue
        end
        column :feed_type
        column :source_type do |profile|
          "#{profile.source_type} #{profile.file_format}"
        end
        column "Total" do |profile|
          "#{profile.total_sku} / #{profile.total_source_row}"
        end
        column :processing_no do |profile|
          "#{profile.processing_no}  - #{profile.progress}% #{profile.start_time ? time_ago_in_words(profile.start_time) : ""} "
        end
        column "Last Taken" do |profile|
          if profile.latest_product_log_id.present?
            product_log = profile.latest_product_log
            if product_log.present? && product_log.start_time.present? && product_log.end_time.present? && product_log.status
              product_log_time_distance = distance_of_time_in_words(product_log.start_time, product_log.end_time).to_s

              product_log_duration = product_log.end_time - product_log.start_time

              profile_time_since_start = Time.now - profile.start_time if profile.start_time.present?

              out = product_log_time_distance

              if profile_time_since_start && profile_time_since_start > (product_log_duration + 5.minutes.to_i)
                out += " ⚠️"
              end

              out.html_safe
            else
              "-"
            end
          else
            "N/A"
          end
        end
        column "More" do |profile|
          link_to("More", admin_user_profile_path(profile), target: "_blank")
        end
      end
    end

    panel "Running Jobs" do
      label "Total Jobs: #{Delayed::Job.where.not(queue: "events").count} / Total Fargate: #{Delayed::Job.where(queue: "main_jobs").where(locked_at: nil).where("run_at < ?", Time.now).count} / Total Local: #{Delayed::Job.where(queue: "local_main_jobs").where(locked_at: nil).where("run_at < ?", Time.now).count}  "
      table_for Delayed::Job.where.not(queue: "events").where("run_at < ?", Time.now + 15.minutes).order("priority asc").order("run_at asc").limit(30) do
        column :queue
        column :priority do |job|
          case job.priority
          when 0
            "Highest"
          when 1
            "High"
          when 2
            "Normal"
          when 3
            "Low"
          else
            "Unknown"
          end
        end
        column :locked_by
        column :run_at do |job|
          # "#{job.run_at} #{time_ago_in_words(job.run_at)}"
          if job.locked_by.present?
            "Running since #{time_ago_in_words(job.run_at)} | #{job.run_at}"
          elsif job.run_at > Time.now
            "Running in #{time_ago_in_words(job.run_at)} | #{job.run_at}"
          else
            "DELAYED #{time_ago_in_words(job.run_at)} | #{job.run_at}"
          end
        end
        column :user_profile do |job|
          if ["main_jobs", "local_main_jobs"].include? job.queue
            if job.user_profile_id.present?
              link_to(job.user_profile_id, admin_user_profile_path(job.user_profile_id), target: "_blank")
            end
          end
        end
        column "Locked / Attempt" do |job|
          "#{job.locked_at ? time_ago_in_words(job.locked_at).to_s : "(#{job.attempts}) #{job.last_error.to_s.truncate(150)}"} "
        end
      end
    end

    panel "Locking" do
      render "locking_queries"
    end
  end
end
