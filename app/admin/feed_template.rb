ActiveAdmin.register FeedTemplate do
  includes :supplier
  actions :all, except: [:new, :create]
  permit_params :profile_name, :profile_sub_name, :supplier_id, :source_url, :file_format, :acc_name, :call_params, :parent_node, :supported_providers, :source_type, :exclude_tags, :include_tags, :woocommerce_consumer_key, :woocommerce_consumer_secret, :col_sep, :row_sep, :header_params, :body_raw, :start_page, :max_page, :http_method, :filter_params, :page_limit, :delete_mode, :partial_match, :feed_type, :auth_type, :sheet_name, :update_duplicate_product_key, :published_apply_matching_products, :unpublished_apply_matching_products, :source_key_1, :shopify_product_key, :variant_node, :connection_settings, :csv_class, :has_header, :use_quote, :auto_file_settings, :file_encoding, :path_to_file
  json_editor

  filter :profile_name
  filter :source_type
  filter :supplier_supplier_type, as: :string, label: "Supplier Type"
  filter :feed_type
  filter :job_type, label: "Update Type", as: :check_boxes, collection: Settings.job_type.map { |k, v| [v, k] }

  scope "Shopify" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_shopify: true}.to_json)
  end
  scope "BigCommerce" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_bigcommerce: true}.to_json)
  end
  scope "WooCommerce" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_woocommerce: true}.to_json)
  end
  scope "Wix" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_wix: true}.to_json)
  end
  scope "Ekm" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_ekm: true}.to_json)
  end
  scope "Squarespace" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_squarespace: true}.to_json)
  end
  scope "Quickbooks" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_quickbooks: true}.to_json)
  end
  scope "Prestashop" do |templates|
    templates.where("user_profiles.supported_providers @> ?", {on_prestashop: true}.to_json)
  end

  index do
    id_column
    column :profile_name
    column :feed_type
    column :supplier
    column "SHOP" do |t|
      t.on_shopify ? "✅" : "⭕"
    end
    column "BIG" do |t|
      t.on_bigcommerce ? "✅" : "⭕"
    end
    column "WOO" do |t|
      t.on_woocommerce ? "✅" : "⭕"
    end
    column "WIX" do |t|
      t.on_wix ? "✅" : "⭕"
    end
    column "EKM" do |t|
      t.on_ekm ? "✅" : "⭕"
    end
    column "SQUARESPACE" do |t|
      t.on_squarespace ? "✅" : "⭕"
    end
    column "QUICKBOOKS" do |t|
      t.on_quickbooks ? "✅" : "⭕"
    end
    column "PRESTASHOP" do |t|
      t.on_prestashop ? "✅" : "⭕"
    end

    column :actions do |template|
      div class: "relative" do
        button id: "dropdownMenuIconButton_#{template.id}", type: "button", data: {dropdown: {toggle: "dropdownDots_#{template.id}"}}, class: "inline-flex items-center p-2 text-sm font-medium text-center text-gray-900 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-600" do
          svg class: "w-5 h-5", aria: {hidden: "true"}, xmlns: "http://www.w3.org/2000/svg", fill: "currentColor", viewBox: "0 0 16 3" do
            text_node "<path d=\"M2 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm6.041 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM14 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Z\"/>".html_safe
          end
        end

        div id: "dropdownDots_#{template.id}", class: "z-10 hidden absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600" do
          ul class: "py-2 text-sm text-gray-700 dark:text-gray-200", aria: {labelledby: "dropdownMenuIconButton"} do
            li do
              a href: admin_feed_template_sync_fields_url(template.id), class: "block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do
                "Sync Fields"
              end
            end
            li do
              a href: edit_admin_feed_template_url(template.id), class: "block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do
                "Edit Feed Template"
              end
            end
          end
        end
      end
    end
  end

  show title: proc { |p|
                "
    #{p.profile_name} [#{p.feed_type.titleize} Feed]
  SHOP: #{p.on_shopify ? "✅" : "⭕"}
  BIG: #{p.on_bigcommerce ? "✅" : "⭕"}
  WOO: #{p.on_woocommerce ? "✅" : "⭕"}
  Wix: #{p.on_wix ? "✅" : "⭕"}
  Ekm: #{p.on_ekm ? "✅" : "⭕"}
  Squarespace: #{p.on_squarespace ? "✅" : "⭕"}
  Quickbooks: #{p.on_quickbooks ? "✅" : "⭕"}
  "
              } do |profile|
    attributes_table do
      columns_to_exclude = ["delete_mode", "password_salt", "acc_password"]
      (FeedTemplate.column_names - columns_to_exclude).each do |c|
        row c.to_sym
      end
      if profile.feed_type == "remove"
        row :delete_mode do |profile|
          "#{(profile.delete_mode == 1) ? "Set to archive" : "Set to delete"}
          <br>(Set nil to delete, 1 to archive)".html_safe
        end
        row :partial_match do |profile|
          (profile.partial_match && profile.delete_mode == 1) ? "Set to archive on partial match" : "Disabled"
        end
      end
    end

    panel "Copy Template to Store ID" do
      form_tag copy_to_store_admin_feed_template_url(feed_template.id) do |f|
        text_field_tag("user_id", "", size: 20) << submit_tag("Update")
      end
    end

    panel "Sync Fields #{link_to "Add New", new_admin_feed_template_sync_field_url(feed_template)}".html_safe do
      table_for feed_template.sync_fields.order("id asc") do
        column :field_name do |d|
          if d.field_name == "product_id"
            options = [profile.variant_key_1, profile.variant_key_2, profile.variant_key_3]
            if options.any?
              joined_options = options.compact.reject(&:empty?).join(" + ")
              if joined_options.present?
                "Product Identifier : #{profile.shopify_product_key} + #{joined_options}"
              else
                "Product Identifier : #{profile.shopify_product_key}"
              end
            end
          else
            d.field_name
          end
        end

        column :field_mapping do |d|
          if d.field_name == "product_id"
            source = [profile.source_key_1, profile.source_key_2, profile.source_key_3]
            if source.any?
              joined_source = source.compact.reject(&:empty?).join(" , ")
              if joined_source.present?
                "#{d.field_mapping} , #{joined_source}"
              else
                d.field_mapping
              end
            end
          else
            d.field_mapping
          end
        end

        column :actions do |d|
          link_to "Edit", edit_admin_feed_template_sync_field_url(feed_template, d)
        end

        column :actions do |d|
          link_to "Delete", admin_feed_template_sync_field_url(feed_template, d), data: {confirm: "Are you sure"}, method: :delete
        end
      end
    end
  end

  action_item :clear_templates_cache, only: :index do
    link_to "Clear templates cache", clear_templates_cache_admin_feed_templates_path, data: {confirm: "Are you sure?"}
  end

  action_item :add_template, only: :show do
    link_to "Copy Template", duplicate_feed_template_admin_feed_template_url(resource.id)
  end

  collection_action :clear_templates_cache do
    redirect_to action: :clear_templates_cache
  end

  controller do
    def clear_templates_cache
      all_feed_types = ["update", "import", "remove"]
      ["shopify", "bigcommerce", "woocommerce"].each do |provider|
        all_feed_types.each do |feed_type|
          Rails.cache.delete("cached_#{provider}_suppliers_for_#{feed_type}")
        end
      end
      flash[:notice] = "Successfully clear templates cache"
      redirect_back fallback_location: admin_feed_templates_path
    end
  end

  member_action :duplicate_feed_template do
    template = FeedTemplate.find(params[:id])
    duplicate = template.deep_dup
    duplicate.save
    template.sync_fields.each do |field|
      arg = field.attributes.except("id", "user_profile_id", "created_at", "updated_at")
      duplicate.sync_fields.create(arg)
    end
    flash[:notice] = "Successfully copied as template ##{duplicate.id}"
    redirect_to admin_feed_templates_url
  end

  member_action :copy_to_store, method: :post do
    feed_template = FeedTemplate.find(params[:id])
    non_update_feeds = ["import", "remove"]

    begin
      shop = User.find(params[:user_id])

      all_profiles_of_type = non_update_feeds.include?(feed_template.feed_type) ? shop.user_profiles.where(fast_track: false, feed_type: non_update_feeds) : shop.user_profiles.where(fast_track: false, feed_type: "update")
      profile_number = all_profiles_of_type.count + 1

      webhook_mode = (shop.provider == "shopify")
      feed = feed_template.create_profile_from_template(user: shop)
      feed.update(profile_name: [feed.profile_name, profile_number].join(" "), webhook_mode: webhook_mode)

      redirect_to admin_user_profile_url(feed), notice: "Template copied to store ID #{shop.id}"
    rescue => e
      redirect_to admin_feed_template_url(feed_template), notice: e.message
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)
    f.inputs "Edit Feed Template" do
      input :profile_name
      input :profile_sub_name
      input :feed_type, collection: ["import", "update", "remove"], include_blank: false
      input :source_url
      input :path_to_file
      input :sheet_name
      input :file_format, as: :select, collection: Settings.file_format.map { |k, v| [v, k] }, include_blank: false
      input :supplier, collection: Supplier.order(name: :asc).collect { |t| [t.name.to_s, t.id] }
      input :source_type, as: :select, collection: Settings.admin_source_types.map { |k, v| [v, k] }.sort, include_blank: false
      input :parent_node
      input :variant_node
      input :shopify_product_key
      input :source_key_1
      input :supported_providers, as: :jsonb
      input :connection_settings, as: :jsonb

      f.inputs "CSV Config" do
        input :file_encoding, as: :select, collection: Settings.file_encoding.map { |k, v| [v, k] }, include_blank: false
        input :has_header
        input :csv_class, as: :select, collection: [["Default", ""], ["FastCSV", "FastCSV"], ["Csv Reader (no col separator)", "raw"], ["Raw Reader", "raw_file"], ["Raw Reader v2", "raw_filev2"], ["Read using foreach", "raw_filev3"]], include_blank: false
        input :col_sep, as: :select, collection: Settings.column_separators.map { |k, v| [v, k] }, include_blank: false
        input :row_sep
        input :use_quote, label: "Disable auto remove double quotes when file can't read for the first time"
        input :auto_file_settings
      end

      f.inputs "Update Settings" do
        input :delete_mode, as: :select, collection: Settings.delete_mode.map { |k, v| [v, k] }.sort, include_blank: false
        input :partial_match
        input :update_duplicate_product_key
      end

      f.inputs "HTTP Params" do
        input :http_method, as: :select, collection: [["get", "get"], ["post", "post"]], include_blank: false
        input :auth_type, as: :select, collection: {"basic_auth" => "Basic Auth", "bearer_token" => "Bearer Token", "oauth_2" => "OAuth2", "oauth_2_with_password" => "OAuth2 with Password", "oauth_2_for_odoo" => "OAuth2 for Odoo", "none" => "None"}.map { |k, v| [v, k] }, include_blank: false
        input :call_params
        input :header_params
        input :start_page
        input :max_page
        input :page_limit
      end

      f.inputs "Update Filters" do
        input :filter_params
        input :include_tags
      end

      f.inputs "Update Credentials" do
        input :acc_name
        input :acc_password, input_html: {autocomplete: "new-password"}
        input :quickbooks_access_token
        input :quickbooks_refresh_token
        input :quickbooks_realm_id
        input :s3_access_key_id
        input :s3_secret_access_key
        input :s3_bucket_name
        input :custom_login_field
        input :custom_login_password_field
        input :unleashed_api_id
        input :unleashed_api_key
        input :vend_access_token
        input :vend_refresh_token
        input :vend_domain_prefix
        input :box_access_token
        input :box_refresh_token
        input :google_auth_client
        input :google_refresh_token
        input :woocommerce_consumer_key
        input :woocommerce_consumer_secret
        input :aliexpress_app_id
        input :aliexpress_app_secret
        input :aliexpress_access_token
        input :xero_client_id
        input :xero_client_secret
        input :xero_access_token
        input :xero_refresh_token
        input :walmart_client_secret
        input :walmart_access_token
        input :body_raw
      end
    end

    f.inputs "Advanced" do
      input :extra_options, as: :jsonb
    end

    f.actions
  end
end
