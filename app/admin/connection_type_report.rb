ActiveAdmin.register ConnectionTypeReport do
  menu parent: "reports_or_logs"
  actions :all, except: [:destroy, :update, :new]
  config.sort_order = "active_count_desc"

  filter :platform
  filter :connection_name
  filter :timestamp

  scope "Today" do |ctreport|
    ctreport.where("timestamp >= ?", Time.now.to_date)
  end
  scope "Yesterday" do |ctreport|
    ctreport.where("timestamp >= ? and timestamp < ?", 0.days.ago.to_date, Time.now.to_date)
  end
  scope "7 days ago" do |ctreport|
    ctreport.where("timestamp >= ?", 6.days.ago.to_date)
  end

  index do
    id_column
    column :timestamp do |d|
      d.timestamp.strftime("%d-%m-%Y").to_s
    end
    column :connection_name
    column :platform
    column :active_count
    column :inactive_count
  end

  show do
    attributes_table do
      row :platform
      row :timestamp do |d|
        d.timestamp.strftime("%d-%m-%Y").to_s
      end
      row :connection_name
      row :active_count
      row :inactive_count
    end
  end
end
