ActiveAdmin.register Supplier do
  permit_params :name,
    :publish_templates,
    :email,
    :url,
    :notes,
    :redirect_to,
    :popular,
    :description,
    :supplier_type,
    :country_code,
    :image_url,
    categories: []

  filter :name
  filter :publish_templates
  filter :email
  filter :url
  filter :notes
  filter :popular
  filter :description
  filter :categories_includes, as: :string, label: "Categories"
  filter :country_code
  filter :supplier_type
  filter :image_url
  filter :popular_count

  scope "Popular" do |suppliers|
    suppliers.where(popular: true)
  end

  controller do
    def update
      params["supplier"]["categories"] = params["supplier"]["categories"].reject(&:blank?)
      super
    end

    def create
      params["supplier"]["categories"] = params["supplier"]["categories"].reject(&:blank?)
      super
    end
  end

  index do
    id_column
    column :name
    column :description
    column :supplier_type
    column :country_code
    column :popular_count
    column :publish_templates
    actions
  end

  show do
    attributes_table(*Supplier.column_names.map(&:to_sym))

    panel "Templates" do
      table_for supplier.feed_templates do
        column :id
        column :feed_type
        column :profile_name
        column "SHOP" do |t|
          t.on_shopify ? "✅" : "⭕"
        end
        column "BIG" do |t|
          t.on_bigcommerce ? "✅" : "⭕"
        end
        column "WOO" do |t|
          t.on_woocommerce ? "✅" : "⭕"
        end
        column "WIX" do |t|
          t.on_wix ? "✅" : "⭕"
        end
        column "EKM" do |t|
          t.on_ekm ? "✅" : "⭕"
        end
        column "SQUARESPACE" do |t|
          t.on_squarespace ? "✅" : "⭕"
        end
        column "QUICKBOOKS" do |t|
          t.on_quickbooks ? "✅" : "⭕"
        end
        column :actions do |d|
          link_to "View", admin_feed_template_url(d)
        end
      end
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)

    f.inputs "Edit Supplier" do
      input :name
      input :description
      input :publish_templates
      input :email
      input :url
      input :notes
      input :redirect_to, as: :select, collection: Settings.supplier_redirect_to.map { |k, v| [v, k] }, include_blank: false
      input :supplier_type, as: :select, collection: Settings.supplier_types.map { |k, v| [v, k] }, include_blank: false
      input :country_code, as: :select, collection: Settings.countries.map { |k, v| [v, k] }
      input :popular
      input :image_url
      input :popular_count
    end

    f.inputs "Categories" do
      supp_categories = Settings.supplier_categories.map { |k, v| [v, k, {checked: f.object.categories.include?(k.to_s)}] }
      f.input :categories, as: :check_boxes, collection: supp_categories
    end

    f.actions
  end
end
