ActiveAdmin.register NotInFeed do
  menu parent: "reports_or_logs"
  actions :all, except: [:destroy, :update, :new]

  filter :filter_product_log, as: :numeric, label: "Product Log", filters: [:equals]
  filter :user_profile_id
  filter :shopify_variant_id
  filter :key

  controller do
    def scoped_collection
      if params[:q].nil?
        end_of_association_chain.none
      else
        super
      end
    end
  end

  show do
    attributes_table do
      row :id
      row :user_profile_id
      row :product_log_id
      row :shopify_variant_id
      row :key
      row :title
      row :created_at
      row :updated_at
    end
  end
end
