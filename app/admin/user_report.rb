ActiveAdmin.register_page "User Report" do
  menu parent: "reports_or_logs"

  content do
    panel "User Report" do
      render "/admin/report/user_report"
    end
  end

  controller do
    def index
      data = LoginActivity.user.success.last_30_days.order("DATE(created_at)").group("DATE(created_at)").count
      @graph = {}

      30.downto(1) do |day_count|
        date = day_count.day.ago.to_date
        @graph[date] = data[date] || 0
      end
    end
  end
end
