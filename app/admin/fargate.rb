ActiveAdmin.register_page "Fargate" do
  menu parent: "reports_or_logs"

  content do
    panel "Running Tasks" do
      render "/admin/fargate/tasks"
    end
    panel "Filter Fargate Task Log Chart" do
      form do |f|
        form_tag admin_fargate_path, method: :get, style: "display:inline" do |f|
          select_tag("time_range", options_for_select([["1", 1], ["3", 3], ["6", 6], ["12", 12], ["24", 24], ["48", 48], ["72", 72]], "no filter")) << " hours  " << submit_tag("Filter")
        end
      end
    end
    panel "Processing Tasks (every 10 min, Asia/Kuala_Lumpur +8)" do
      since = params[:time_range].present? ? params[:time_range].to_i.hours.ago
                                                  : 24.hours.ago
      tz = ActiveSupport::TimeZone["Asia/Kuala_Lumpur"]
      logs = TaskLog
        .where("created_at >= ?", since)
        .select(:created_at,
          :fargate,
          :local_task,
          :pending_fargate_delayed_jobs,
          :pending_local_delayed_jobs)

      buckets = Hash.new { |h, k|
        h[k] = {fargate: 0, local: 0,
                                 pending_fargate: 0, pending_local: 0}
      }

      logs.each do |log|
        ts = tz.at(log.created_at)
        key = ts.change(sec: 0) - (ts.min % 10).minutes

        bucket = buckets[key]
        bucket[:fargate] += log.fargate
        bucket[:local] += log.local_task
        bucket[:pending_fargate] += log.pending_fargate_delayed_jobs
        bucket[:pending_local] += log.pending_local_delayed_jobs
      end

      points = buckets.sort_by { |t, _| t }

      categories = points.map { |t, _| t.strftime("%H:%M %d/%m") }
      chart_data = [
        {name: "Fargate", data: points.map { |_, v| v[:fargate] }},
        {name: "Local Task", data: points.map { |_, v| v[:local] }},
        {name: "Pending Fargate", data: points.map { |_, v| v[:pending_fargate] }},
        {name: "Pending Local", data: points.map { |_, v| v[:pending_local] }}
      ]

      render "/admin/fargate/chart", chart_data: chart_data, categories: categories
    end
  end

  controller do
    def index
      @fargate_user_profiles = CancelFeed::Runner.get_running_fargates

      @fargate_tasks_count = @fargate_user_profiles.count

      @processing_user_profiles = UserProfile.where(status: "processing").where.not(fargate_task_id: nil).map(&:id).sort

      @fargate_not_processing = @fargate_user_profiles - @processing_user_profiles
      @processing_not_fargate = @processing_user_profiles - @fargate_user_profiles
    end
  end
end
