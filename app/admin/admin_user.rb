ActiveAdmin.register AdminUser do
  menu parent: "setting"
  permit_params :role, :email

  index download_links: false do
    id_column
    column :email
    column :current_sign_in_at
    column :last_sign_in_at
    column :current_sign_in_ip
    column :last_sign_in_ip
    column :created_at
    column :updated_at
    actions
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)
    f.inputs "Edit Admin User" do
      input :email
      input :role
    end
    f.actions
  end
end
