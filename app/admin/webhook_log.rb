ActiveAdmin.register WebhookLog do
  menu parent: "reports_or_logs"

  actions :all, except: [:destroy, :update, :new]

  show do
    attributes_table do
      row :id
      row "Activity Log" do |web|
        if web.product_log_id.present?
          link_to "ProductLog #{web.product_log_id}", admin_product_log_url(id: web.product_log_id)
        else
          "No Log"
        end
      end
      row :external_id
      row :metadata do |web|
        "<p style=\"word-break: break-word;\">#{web.metadata}</p>".html_safe
      end
      row :max_stage
      row :current_stage
      row "Files queue (files_stage)" do |web|
        text = ""
        (web.files_stage || "").split(",").each do |file|
          text = "#{text}#{file}<br>"
        end
        text.html_safe
      end
      row :created_at
      row :updated_at
      row "Current Detail" do |web|
        feed = UserProfile.with_deleted.find_by_id(web.user_profile_id)
        if feed.present?
          text = "<p style=\"word-break: break-word;\">#{link_to "Profile #{web.user_profile_id}", admin_user_profile_url(id: web.user_profile_id)}<br><br>"
          user = feed.user
          if user.provider == "shopify" && web.external_id.present?
            text = "#{text}#{ShopifyAPI::LightGraphQL.poll_bulk(user.shopify_domain, user.shopify_token, web.external_id.split("debug::").last)}</p>"
          else
            text += "No data yet</p>"
          end
          text.html_safe
        else
          "No feed attached to log:<br><br>#{web.to_json}".html_safe
        end
      end
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)

    f.inputs "Edit Webhook Log" do
      # input :installed_date
      input :external_id
      input :metadata
      input :max_stage
      input :current_stage
      f.actions
    end
  end
end
