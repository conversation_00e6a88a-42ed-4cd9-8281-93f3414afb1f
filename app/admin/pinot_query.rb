ActiveAdmin.register_page "Pinot Query" do
  menu parent: "reports_or_logs"

  # Define a page action for AJAX requests to run the query.
  page_action :run_query, method: :post do
    sql = params[:sql]
    timeout = params[:timeout]
    result = PinotBrokerQuery.query_sql(sql, timeout)
    if result.is_a?(Hash) && result[:error].present?
      render json: {success: false, error: result[:error]}
    else
      render json: {success: true, data: result[:data]}
    end
  end

  content title: "Query Pinot using SQL" do
    panel "Enter SQL Query" do
      div do
        text_area_tag "sql", "SELECT * FROM master_product_log ORDER BY created_at DESC LIMIT 10", id: "sql-input", rows: 5, cols: 100
      end
      div do
        text_area_tag "timeout", "10000", id: "timeout-input", rows: 5, cols: 1
      end
      br
      div do
        button "Run Query", id: "run-query-button", class: "bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      end
    end

    panel "Query Results" do
      div id: "pinot-results"
    end

    script do
      raw <<-JS
        // =================================================
        // 1) Function to render JSON as a human-readable table
        // =================================================
        function renderJsonAsTable(value) {
          if (value === null || typeof value !== 'object') {
            return String(value);
          }

          // Handle { old, new } objects as a comparison table
          if (!Array.isArray(value)) {
            const keys = Object.keys(value);
            if (keys.length === 2 && keys.includes('old') && keys.includes('new')) {
              return `
                <table class="border border-gray-300 w-full text-xs my-1">
                  <thead class="bg-gray-100">
                    <tr>
                      <th class="border px-2 py-1">Old</th>
                      <th class="border px-2 py-1">New</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-b">
                      <td class="border px-2 py-1">${renderJsonAsTable(value.old)}</td>
                      <td class="border px-2 py-1">${renderJsonAsTable(value.new)}</td>
                    </tr>
                  </tbody>
                </table>
              `;
            }
          }

          // Handle arrays
          if (Array.isArray(value)) {
            let html = "<table class='border border-gray-300 w-full text-xs my-1'><thead class='bg-gray-100'><tr><th class='border px-2 py-1'>Index</th><th class='border px-2 py-1'>Value</th></tr></thead><tbody>";
            value.forEach((item, i) => {
              html += "<tr class='border-b'><td class='border px-2 py-1 font-semibold'>" + i + "</td><td class='border px-2 py-1'>" + renderJsonAsTable(item) + "</td></tr>";
            });
            html += "</tbody></table>";
            return html;
          } else {
            // General object table
            let html = "<table class='border border-gray-300 w-full text-xs my-1'><thead class='bg-gray-100'><tr><th class='border px-2 py-1'>Key</th><th class='border px-2 py-1'>Value</th></tr></thead><tbody>";
            for (const key in value) {
              if (Object.hasOwnProperty.call(value, key)) {
                html += "<tr class='border-b'><td class='border px-2 py-1 font-semibold'>" + key + "</td><td class='border px-2 py-1'>" + renderJsonAsTable(value[key]) + "</td></tr>";
              }
            }
            html += "</tbody></table>";
            return html;
          }
        }

        // =================================================
        // 2) Toggle preview
        // =================================================
        function togglePreview(event) {
          const button = event.target;
          const container = button.nextElementSibling;
          if (!container) return;

          if (container.style.display === 'none' || container.style.display === '') {
            container.style.display = 'block';
            button.textContent = 'Hide';
          } else {
            container.style.display = 'none';
            button.textContent = 'Preview';
          }
        }

        document.addEventListener("DOMContentLoaded", function(){
          var btn = document.getElementById("run-query-button");
          var sqlInput = document.getElementById("sql-input");
          var timeoutInput = document.getElementById("timeout-input");
          var resultsDiv = document.getElementById("pinot-results");

          btn.addEventListener("click", function(){
            var sql = sqlInput.value.trim();
            var timeout = timeoutInput.value.trim();
            if (!sql) {
              resultsDiv.innerHTML = "<p class='text-red-500'>Please enter SQL</p>";
              return;
            }
            resultsDiv.innerHTML = "<p>Running query...</p>";

            fetch("/admin/pinot_query/run_query", {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
              },
              body: JSON.stringify({ sql: sql, timeout: timeout })
            })
            .then(response => response.json())
            .then(function(json){
              if (!json.success) {
                resultsDiv.innerHTML = "<p class='text-red-500'>Error: " + json.error + "</p>";
                return;
              }
              var rows = json.data;
              if (rows.length === 0) {
                resultsDiv.innerHTML = "<p>No results found.</p>";
                return;
              }

              var columns = Object.keys(rows[0]);
              var table = `
                <div class="overflow-x-auto relative shadow-md sm:rounded-lg">
                  <table class="w-full text-sm text-left text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                      <tr>
              `;
              columns.forEach(function(col){
                table += `
                  <th scope="col" class="py-3 px-6">
                    ${col}
                  </th>
                `;
              });
              table += `
                      </tr>
                    </thead>
                    <tbody>
              `;

              rows.forEach(function(row, rowIndex){
                var rowBg = (rowIndex % 2 === 0) ? "bg-white" : "bg-gray-50";
                table += `<tr class="${rowBg} border-b">`;

                columns.forEach(function(col){
                  let cellValue = row[col] == null ? "" : row[col];

                  // If it's the 'message' column, show raw JSON + a Preview button
                  if (col === "message" && typeof cellValue === "object") {
                    let subTableHtml = renderJsonAsTable(cellValue);

                    cellValue = `
                      <button class="bg-gray-200 hover:bg-gray-300 text-xs px-2 py-1 rounded mt-1"
                              onclick="togglePreview(event)">
                        Preview
                      </button>
                      <div style="display: none;" class="mt-2 border border-gray-300 p-2 text-xs">
                        ${subTableHtml}
                      </div>
                    `;
                  }

                  table += `
                    <td class="py-4 px-6 align-top">
                      ${cellValue}
                    </td>
                  `;
                });

                table += "</tr>";
              });

              table += `
                    </tbody>
                  </table>
                </div>
              `;
              resultsDiv.innerHTML = table;
            })
            .catch(function(err){
              resultsDiv.innerHTML = "<p class='text-red-500'>Fetch error: " + err + "</p>";
            });
          });
        });
      JS
    end
  end
end
