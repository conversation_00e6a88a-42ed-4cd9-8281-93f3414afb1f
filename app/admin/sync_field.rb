ActiveAdmin.register Sync<PERSON>ield do
  menu false
  permit_params :field_name, :field_mapping, :user_profile_id, :extra_attributes, :is_locked
  remove_filter :audits

  collection_action :default_extra_attributes, method: :get do
    respond_to do |format|
      format.json { render json: SyncField.extra_attributes_with_defaut_values }
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)

    f.inputs "Sync Field" do
      input :is_locked, label: "Lock field mapping on UI"
      input :field_name, as: :select, collection: SyncField.all_attributes.keys + (1..80).collect { |a| "metafield_#{a}" } + (1..80).collect { |a| "custom_field_#{a}" } + (1..80).collect { |a| "product_attribute_#{a}" }, include_blank: false
      input :field_mapping
      div do
        input :extra_attributes
        a href: "#", id: "reset-extra-attributes-default" do
          "reset attributes"
        end
      end
    end

    f.actions
  end

  controller do
    def destroy
      destroy! do |format|
        format.html { redirect_to redirection_path(resource) } if resource.valid?
        # format.html { redirect_to :back } if resource.valid?
      end
    end

    def create
      create! do |format|
        format.html { redirect_to redirection_path(resource) } if resource.valid?
      end
    end

    def update
      update! do |format|
        format.html { redirect_to redirection_path(resource) } if resource.valid?
      end
    end

    private

    def redirection_path(resource)
      profile = begin
        UserProfile.find(resource.user_profile_id)
      rescue
        nil
      end
      # template = begin
      #   FeedTemplate.find(resource.user_profile_id)
      # rescue
      #   nil
      # end

      if profile.present?
        admin_user_profile_url(resource.user_profile_id)
      else
        admin_feed_template_url(resource.user_profile_id)
      end
    end

    belongs_to :feed_template, :user_profile, polymorphic: true
  end
end
