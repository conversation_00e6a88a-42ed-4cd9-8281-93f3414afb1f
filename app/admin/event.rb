ActiveAdmin.register Schedulable::Event do
  menu parent: "setting"

  index do
    id_column
    column :product_log_id do |d|
      if d.product_log_id.present?
        link_to d.product_log_id, admin_product_log_url(d.product_log_id)
      else
        "-"
      end
    end
    column :trigger_by
    column :view_logs do |event|
      link = event.fargate_log_url
      if link
        link_to(event.fargate_task_id, link, target: :_blank)
      else
        event.remark
      end
    end
    column :view_log_insights do |event|
      link = event.fargate_log_insight_url
      if link
        link_to("View", link, target: :_blank)
      end
    end
    column :created_at do |d|
      @user_profile ||= UserProfile.with_deleted.find(d.schedulable_id)
      d.created_at.blank? ? "" : "#{d.created_at.in_time_zone(@user_profile.timezone)} (#{time_ago_in_words(d.created_at)})"
    end
    column :updated_at do |d|
      @user_profile ||= UserProfile.with_deleted.find(d.schedulable_id)
      d.updated_at.blank? ? "" : "#{d.updated_at.in_time_zone(@user_profile.timezone)} (#{time_ago_in_words(d.updated_at)})"
    end
    column :scheduled_at do |d|
      @user_profile ||= UserProfile.with_deleted.find(d.schedulable_id)
      d.scheduled_at.blank? ? "" : "#{d.scheduled_at.in_time_zone(@user_profile.timezone)} (#{time_ago_in_words(d.scheduled_at)})"
    end
    column :run_at do |d|
      @user_profile ||= UserProfile.with_deleted.find(d.schedulable_id)
      d.run_at.blank? ? "" : "#{d.run_at.in_time_zone(@user_profile.timezone)} (#{time_ago_in_words(d.run_at)})"
    end
    column :finished_at do |d|
      @user_profile ||= UserProfile.with_deleted.find(d.schedulable_id)
      d.finished_at.blank? ? "" : "#{d.finished_at.in_time_zone(@user_profile.timezone)} (#{time_ago_in_words(d.finished_at)})"
    end
  end
end
