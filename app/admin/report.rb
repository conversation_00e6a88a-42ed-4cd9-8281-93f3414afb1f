ActiveAdmin.register_page "Report" do
  menu parent: "reports_or_logs"

  content do
    panel "Processing Time per Product for 1 Week" do
      render "load_store_time"
    end

    panel "Last 20 low query report in web server (> 500ms)" do
      render "web_slow_query"
    end

    panel "Last 20 low query report in job server (> 500ms)" do
      render "job_slow_query"
    end
  end

  controller do
    def index
      @data = {}
      @data[:load_source_time] = []
      @data[:processing_time] = []
      @data[:post_update_time] = []
      @data[:date_range] = []

      6.downto(0) do |day|
        @data[:date_range].push((Date.today - day).to_s)

        load_source_time_logs = UserProfile.select("user_profiles.id, SUM(product_logs.number_product_updated) AS updated_product, SUM(DATE_PART('epoch', product_logs.data_processed_at - product_logs.start_time)) AS diff").joins(:product_logs).group("user_profiles.id").where("Date(product_logs.start_time) = ?", Date.today - day).where(feed_type: "update", product_logs: {status: true}).where.not(product_logs: {start_time: nil, end_time: nil})

        load_source_time_per_product = load_source_time_logs.map { |a| a.diff }.sum / (load_source_time_logs.map { |a| a.updated_product }.sum.nonzero? || 1)
        @data[:load_source_time].push(load_source_time_per_product.round(4))

        processing_time_logs = UserProfile.select("user_profiles.id, SUM(product_logs.number_product_updated) AS updated_product, SUM(DATE_PART('epoch', product_logs.process_done_at - product_logs.data_processed_at)) AS diff").joins(:product_logs).group("user_profiles.id").where("Date(product_logs.start_time) = ?", Date.today - day).where(feed_type: "update", product_logs: {status: true}).where.not(product_logs: {start_time: nil, end_time: nil})

        processing_time_per_product = processing_time_logs.map { |a| a.diff }.sum / (processing_time_logs.map { |a| a.updated_product }.sum.nonzero? || 1)
        @data[:processing_time].push(processing_time_per_product.round(4))

        post_update_time_logs = UserProfile.select("user_profiles.id, SUM(product_logs.number_product_updated) AS updated_product, SUM(DATE_PART('epoch', product_logs.end_time - product_logs.process_done_at)) AS diff").joins(:product_logs).group("user_profiles.id").where("Date(product_logs.start_time) = ?", Date.today - day).where(feed_type: "update", product_logs: {status: true}).where.not(product_logs: {start_time: nil, end_time: nil})

        post_update_time_per_product = post_update_time_logs.map { |a| a.diff }.sum / (post_update_time_logs.map { |a| a.updated_product }.sum.nonzero? || 1)
        @data[:post_update_time].push(post_update_time_per_product.round(4))
      end

      begin
        web_slow_log = File.open(Rails.root.join("log", "web_slow_query.log"), "r")
        @data[:web_slow_query] = web_slow_log.to_a.last(20)
        web_slow_log.close
      rescue => e
        @data[:web_slow_query] = e
      end

      begin
        job_slow_log = File.open(Rails.root.join("log", "job_slow_query.log"), "r")
        @data[:job_slow_query] = job_slow_log.to_a.last(20)
        job_slow_log.close
      rescue => e
        @data[:job_slow_query] = e
      end
    end
  end
end
