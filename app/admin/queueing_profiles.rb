ActiveAdmin.register_page "Queuing Profiles" do
  menu parent: "reports_or_logs"

  page_action :cancel_all, method: :post do
    success = 0
    failed = 0
    UserProfile.where("fargate_task_id IS NOT NULL").where(status: "start").find_each do |profile|
      res = profile.cancel_process("rake task")
      res ? success += 1 : failed += 1
    end
    redirect_to admin_queuing_profiles_path, notice: "Done All (#{success}), #{failed} failed"
  end

  page_action :cancel, method: :post do
    profile = UserProfile.find(params[:id])
    res = profile.cancel_process
    redirect_to admin_queuing_profiles_path, notice: "#{res ? "Success" : "Failed"} #{params[:id]}"
  end

  page_action :cancel_all_force, method: :post do
    success = 0
    failed = 0
    UserProfile.where("fargate_task_id IS NOT NULL").where(status: "start").find_each do |profile|
      res = profile.update(status: "start", fargate_task_id: nil)
      res ? success += 1 : failed += 1
    end
    redirect_to admin_queuing_profiles_path, notice: "Done All (#{success}), #{failed} failed"
  end

  page_action :cancel_force, method: :post do
    profile = UserProfile.find(params[:id])
    res = profile.update(status: "start", fargate_task_id: nil)
    redirect_to admin_queuing_profiles_path, notice: "#{res ? "Success" : "Failed"} #{params[:id]}"
  end

  page_action :queue_all, method: :post do
    success = 0
    failed = 0
    UserProfile.where("fargate_task_id LIKE ?", "bulk%").where.not(status: "start").find_each do |profile|
      result = {}
      webhook = nil
      begin
        # raise StandardError.new("profile currently not queuing (#{profile.status})") if !profile.queuing?
        user = profile.user
        target = profile.fargate_task_id.split("::").last
        result = ShopifyAPI::LightGraphQL.poll_bulk(user.shopify_domain, user.shopify_token, target)
        webhook = profile.webhook_logs.find_by_external_id(target)
        if webhook.blank?
          failed += 1
          next
        end
        if result.dig("data", "node", "status") == "COMPLETED" && (result.dig("data", "node", "completedAt") < (Time.now - 3.minutes))
          trigger_by = webhook.try(:trigger_by) || "user"
          run_on_local = (webhook.metadata.to_s =~ /^\["local",/) == 0
          options = {trigger_by: trigger_by, webhook_log_id: webhook.id, run_on_local: run_on_local}

          if !(result.dig("data", "node", "type") == "QUERY")
            options[:chain_mutation] = webhook.current_stage
          end
          feed_process_job = ProcessFeedJob.new(profile.id, **options)
          job = Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue, priority: 1, signature: "manual_queue")
          job ? success += 1 : failed += 1
        end
      rescue => e
        Airbrake.notify(e, details: result, webhook_log_id: webhook.try(:id), user_profile_id: profile.try(:id))
      end
    end
    redirect_to admin_queuing_profiles_path, notice: "Queued All (#{success}), #{failed} failed"
  end

  page_action :queue, method: :post do
    res = false
    begin
      profile = UserProfile.find(params[:id])
      # raise StandardError.new("profile currently not queuing (#{profile.status})") if !profile.queuing?
      user = profile.user
      target = profile.fargate_task_id.split("::").last
      result = ShopifyAPI::LightGraphQL.poll_bulk(user.shopify_domain, user.shopify_token, target)
      webhook = profile.webhook_logs.find_by_external_id(target)
      if webhook.blank?
        redirect_to admin_queuing_profiles_path, error: "Queue Failed Webhook Blank #{params[:id]}"
      end
      if result.dig("data", "node", "status") == "COMPLETED" && (result.dig("data", "node", "completedAt") < (Time.now - 3.minutes))
        trigger_by = webhook.try(:trigger_by) || "user"
        run_on_local = (webhook.metadata.to_s =~ /^\["local",/) == 0
        options = {trigger_by: trigger_by, webhook_log_id: webhook.id, run_on_local: run_on_local}

        if !(result.dig("data", "node", "type") == "QUERY")
          options[:chain_mutation] = webhook.current_stage
        end
        feed_process_job = ProcessFeedJob.new(profile.id, **options)

        job = Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue, priority: 1, signature: "manual_queue")
        res = true if job
      end
    rescue => e
      Airbrake.notify(e, details: result, webhook_log_id: webhook.try(:id), user_profile_id: profile.try(:id))
    end
    redirect_to admin_queuing_profiles_path, notice: "Queue #{res ? "Success" : "Failed"} #{params[:id]}"
  end

  content do
    div class: "columns" do
      div class: "column" do
        panel "Start profiles with task id" do
          render "/admin/report/queuing_profiles_start"
        end
      end

      div class: "column" do
        panel "Queued profiles with bulk id" do
          render "/admin/report/queuing_profiles_bulk"
        end
      end
    end
  end

  controller do
    def index
      @bulk_profiles = []
      @completed_at = {}
      @start_profiles = UserProfile.where("fargate_task_id IS NOT NULL").where(status: "start")
      @op_type = {}
      # .find_each do |profile|
      #   profile.cancel_process("rake task")
      # end
      UserProfile.where("fargate_task_id LIKE ?", "bulk%").where.not(status: "start").find_each do |profile|
        result = {}
        webhook = nil
        begin
          # raise StandardError.new("profile currently not queuing (#{profile.status})") if !profile.queuing?
          user = profile.user
          target = profile.fargate_task_id.split("::").last
          result = ShopifyAPI::LightGraphQL.poll_bulk(user.shopify_domain, user.shopify_token, target)
          webhook = profile.webhook_logs.find_by_external_id(target)
          if webhook.blank?
            next
          end
          if result.dig("data", "node", "status") == "COMPLETED" && (result.dig("data", "node", "completedAt") < (Time.now - 3.minutes))
            @bulk_profiles << profile
            @completed_at[profile.id] = result.dig("data", "node", "completedAt")
            @op_type[profile.id] = result.dig("data", "node", "type")
            # feed_process_job = ProcessFeedJob.new(profile.id, trigger_by: (webhook.try(:trigger_by) || 'user'), webhook_log_id: webhook.id, run_on_local: ((webhook.metadata.to_s =~ /^\["local",/) == 0) )
            # job = Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue, priority: 1)
            # puts profile.id
          end
        rescue => e
          Airbrake.notify(e, details: result, webhook_log_id: webhook.try(:id), user_profile_id: profile.try(:id))
        end
      end
    end
  end
end
