ActiveAdmin.register_page "User Plan Report" do
  menu parent: "reports_or_logs"

  page_action :download_csv, method: :get

  action_item :download_csv do
    link_to "Download CSV",
      admin_user_plan_report_download_csv_path(sort_by_ttl_user: params[:sort_by_ttl_user]),
      method: :get
  end

  sidebar :filter, partial: "filter"

  content do
    panel "User Plan (status active only)" do
      render "user_plan_list"
    end
  end

  controller do
    def index
      @user_plan_data = generate_data
    end

    def download_csv
      data = generate_data
      csv = ""
      csv << data.columns.join(",")
      csv << "\n"
      data.rows.each do |r|
        csv << r.join(",")
        csv << "\n"
      end
      respond_to do |format|
        format.html { send_data csv, filename: "user_plan_report.csv" }
      end
    end

    private

    def generate_data
      sub_sql_sort_by = "order by A.plan_version, A.package"
      if (sort = params[:sort_by_ttl_user])
        sub_sql_sort_by = "order by ttl_user #{sort}"
      end
      sql = "select
              A.plan_version,
              A.package as plan_package,
              p.price as plan_price,
              count(*) as ttl_user,
              ROUND(sum(A.ttl_feed) / count(*),
              2) as avg_feed_count,
              sum(A.import_limit_skus) as ttl_credit,
              ROUND(sum(A.import_limit_skus) / count(*)) as avg_credit
            from
              (
              select
                u.id,
                case
                  when (u.pre_plan_version is null
                  or u.pre_plan_version = '') then u.plan_version
                  else u.pre_plan_version
                end as plan_version,
                case
                  when u.package ilike 'Custom%' then 'Custom Plan'
                  else u.package
                end as package,
                count(*) as ttl_feed,
                import_limit_skus
              from
                users u
              left join user_profiles up on
                up.user_id = u.id
                and up.deleted_at is null
              where
                u.status = 'active'
              group by
                u.id) A
            left join plans p on
              p.version = A.plan_version
              and p.key = A.package
            group by
              A.plan_version,
              A.package,
              p.price
             #{sub_sql_sort_by};"
      ActiveRecord::Base.connection.exec_query(sql)
    end
  end
end
