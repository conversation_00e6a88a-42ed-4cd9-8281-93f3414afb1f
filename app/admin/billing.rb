ActiveAdmin.register Billing do
  menu parent: "reports_or_logs"

  actions :all, except: [:destroy, :update, :new]

  action_item :check_pending_subscription, only: :show do
    if resource.user.provider === "shopify" && resource.status != "active"
      link_to "Check Billing Status", check_pending_subscription_admin_billing_path(resource)
    end
  end

  member_action :check_pending_subscription, method: :get do
    billing = Billing.find(params[:id])
    status = billing.check_subscription_status
    redirect_to admin_billing_path(billing), notice: status ? "Billing has been activated" : "No change has been detected"
  end

  filter :status
  filter :shop_name
  filter :plan_name

  scope "Pending" do |billings|
    billings.where(status: "pending")
  end

  scope "Upgrade" do |billings|
    billings.where(charge_type: Billing::UPGRADE)
  end

  scope "Downgrade" do |billings|
    billings.where(charge_type: Billing::DOWNGRADE)
  end

  scope "Upgrade - BIG" do |billings|
    billings.where(charge_type: Billing::UPGRADE, platform: "bigcommerce")
  end

  scope "Upgrade - Woo" do |billings|
    billings.where(charge_type: Billing::UPGRADE, platform: "woocommerce")
  end

  index download_links: false do
    id_column
    column :shop_name
    column :plan_name do |billing|
      "#{billing.plan_name} #{billing.remark}"
    end
    column :status
    column :charge_type
    column :total_charge
    column :created_at do |billing|
      "#{billing.created_at} #{time_ago_in_words(billing.created_at)}"
    end
    column :refunded
    actions
  end
end
