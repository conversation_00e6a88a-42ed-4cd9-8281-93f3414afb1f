ActiveAdmin.register ProductLog do
  controller do
    helper ProductLogHelper
  end
  menu parent: "reports_or_logs"

  actions :all, except: [:destroy, :update, :new]

  filter :total_store_skus
  filter :number_product_updated
  filter :number_product_update_failed
  filter :created_at
  filter :user_id
  filter :user_profile_id

  action_item :revert_process, only: :show do
    revert_link(resource)
  end

  scope :all, default: true
  scope "1 month ago" do |logs|
    logs.where(created_at: [1.month.ago..Time.now])
  end
  scope "2 month ago" do |logs|
    logs.where(created_at: [2.months.ago..1.month.ago])
  end

  member_action :revert_process do
    product_log = ProductLog.find(params[:id])
    job = product_log.create_revert_job
    flash[:notice] = "Running revert process in background." if job.id
    redirect_to admin_user_profile_url(product_log.user_profile_id)
  end

  member_action :s3_links do
    log = ProductLog.find(params[:id])
    disk_name = params[:file]
    s3 = AwsServices.s3_resource_client
    file = s3.bucket(Settings.s3.bucket_name).object("product_logs/user_profiles/#{log.user_profile_id}_#{log.download_id}/#{disk_name}")
    if file.exists?
      url = file.presigned_url(:get, expires_in: 7200, response_content_disposition: "inline; filename=\"#{params[:name]}\"") # 2 hours expiry
      redirect_to url, allow_other_host: true
    else
      redirect_to "#{Settings.hostname}/system/user_profiles/#{log.user_profile_id}_#{log.download_id}/#{disk_name}", allow_other_host: true
    end
  end

  member_action :not_in_feeds_file do
    # TODO make this secured
    product_log = ProductLog.find(params[:id])

    csv = CSV.generate do |csv|
      csv << ["ID", "Product Log ID", "Key", "Title", "Variant ID", "Created At", "Updated At"]
      NotInFeed.where(user_profile_id: product_log.user_profile_id, product_log_id: product_log.id).each do |log|
        csv << [log.id, log.product_log_id, log.key, log.title, log.shopify_variant_id, log.created_at, log.updated_at]
      end
    end
    send_data csv, type: "text/csv; header=present", disposition: "attachment; filename=not_in_feed.csv"
  end

  index download_links: false do
    id_column
    column :user_profile do |log|
      log.user_profile ? link_to(log.user_profile.profile_name, admin_user_profile_url(log.user_profile)) : "N/A"
    end
    column :status
    column :init_credits
    column :total_store_skus do |log|
      "#{log.total_store_skus} / #{log.number_product_updated} / #{log.number_product_update_failed}"
    end
    column :file_format do |log|
      log.user_profile ? log.user_profile.file_format : "N/A"
    end
    column :remark
    column :resumed
    column :created_at do |log|
      @timezone ||= log.user_profile.user.timezone
      "#{log.created_at.in_time_zone(@timezone)} #{time_ago_in_words(log.created_at.in_time_zone(@timezone))}"
    end
    column "Taken & Q" do |log|
      taken = if log.start_time && log.end_time
        distance_of_time_in_words(log.start_time, log.end_time)
      else
        "-"
      end
      webhook_query_time = if log.webhook_query_created_at.present? && log.webhook_query_completed_at.present?
        " (#{distance_of_time_in_words(log.webhook_query_created_at, log.webhook_query_completed_at)})"
      else
        ""
      end
      "Taken:#{taken} / Q:#{(log.queuing_at && log.start_time) ? distance_of_time_in_words(log.queuing_at, log.start_time) : "N/A"}#{webhook_query_time}".html_safe
    end
    column :undo do |log|
      revert_link(log)
    end
    actions
  end

  show do |log_object|
    attributes_table do
      row :id
      if log_object.number_of_product_hidden > 0 || log_object.number_of_product_published > 0
        row "Product Status" do |log|
          link_to "Download", s3_links_admin_product_log_url(log, file: ProductUpdateStatus::FILE_PRODUCT_STATUS, name: "product_status.csv")
        end
      end
      row "Activity logs" do |log|
        link_to "Download", s3_links_admin_product_log_url(log, file: log.user_profile.activity_logs_file_s3)
      end
      row "Back in stock" do |log|
        link_to "Download", s3_links_admin_product_log_url(log, file: ProductUpdateStatus::FILE_BACKINSTOCK, name: "back_in_stock.csv")
      end
      row "Out of stock" do |log|
        link_to "Download", s3_links_admin_product_log_url(log, file: ProductUpdateStatus::FILE_OUTOFSTOCK, name: "out_of_stock.csv")
      end
      row "Not In Store" do |log|
        link_to "Download", s3_links_admin_product_log_url(log, file: ProductUpdateStatus::FILE_FAILED_PRODUCT, name: "not_in_store.csv")
      end
      row "Not in Feed" do |log|
        "#{link_to "Download", not_in_feeds_file_admin_product_log_url(log), target: "_blank"} | #{link_to "Search", admin_not_in_feeds_path(q: {filter_product_log_equals: log.id}), target: "_blank"} | #{link_to "S3 Archive", s3_links_admin_product_log_url(log, file: ProductUpdateStatus::FILE_UNMATCHED, name: "not_in_feed.csv")}".html_safe
      end
      row "Error List" do |log|
        (log.error_list.to_s == "download_file_for_details") ?
        link_to("Download", s3_links_admin_product_log_url(log, file: ProductUpdateStatus::FILE_ERROR_LIST, name: "error_list.csv")) :
        "<p style=\"word-break: break-word;\">#{log.error_list}</p>".html_safe
      end

      row :undo_at
      row :revert_process do |log|
        revert_link(log)
      end
      row :total_feed_rows
      row :total_feed_filtered_rows
      row :number_product_updated
      row :number_of_product_published
      row :number_of_product_hidden
      row :fail_update_id
      row :status
      row :resumed
      row :remark
      row :remark_values
      row :user_profile_id
      row :created_at
      row :updated_at
      row :number_product_update_failed
      row :store_fail_ids
      row :total_store_skus
      row :out_of_stock_ids
      row :queuing_at
      row :start_time
      row :end_time
      row :actual_product_updated
      row :data_processed_at
      row :store_products_loaded_at
      row :process_done_at
      row :published_checked_at
      row :actual_product_retry_updated
      row :update_period
      row :trigger_by
      row :deploy_version
      row :number_out_of_stocks
      row :total_left_over_ids
      row :number_of_product_published
      row :number_of_product_hidden
      row :cache
      row :file_name
      row :init_credits
      row :webhook_query_created_at
      row :webhook_query_completed_at
      row :webhook_log do |log|
        wh = WebhookLog.where(product_log_id: log.id, user_profile_id: log.user_profile_id).limit(1)
        if wh.present?
          link_to "View Webhook log", admin_webhook_log_url(wh.first.id)
        end
      end
    end
  end
end
