ActiveAdmin.register_page "Profile Audited Log" do
  belongs_to :user_profile

  content do
    def format_text(t)
      if t.nil?
        "nil"
      else
        t.to_s
      end
    end

    def format_order(idx)
      page = params[:page]
      if page.nil? || page == "1"
        idx + 1
      else
        idx + 1 + ((page.to_i - 1) * 50)
      end
    end

    extra_attr_ignore_list = ["auto_delimiter"]

    div do
      text_node("Note :")
      b(" extra_attributes")
      text_node(" ignore keys (#{extra_attr_ignore_list.join(",")}),")
      text_node(" and only show updated key")
    end

    panel "Audited Logs" do
      paginated_collection(assigns[:logs], download_links: false) do
        table class: "data-table" do
          thead do
            tr do
              th "No"
              th "Date Time"
              th "Source"
              th "Source ID"
              th "Username"
              th "Action"
              th "Changes", style: "width: 50%"
            end
          end

          tbody do
            pre_created_at = nil
            assigns[:logs].each_with_index do |log, idx|
              tr do
                td(format_order(idx))
                td do
                  if log.created_at.to_i == pre_created_at.to_i
                    "-"
                  else
                    pre_created_at = log.created_at
                    tz = assigns[:user_timezone]
                    dt = "#{log.created_at.in_time_zone("Asia/Singapore")} #{time_ago_in_words(log.created_at.in_time_zone("Asia/Singapore"))}<br>"
                    dt += "#{log.created_at.in_time_zone(tz)} (#{tz})"
                    dt.html_safe
                  end
                end
                td(log.auditable_type)
                td(log.auditable_id)
                td(log.username)
                td(log.action)
                td do
                  if log.action == "update"
                    log.audited_changes.to_a.each do |x|
                      div do
                        field_name = x[0].to_s
                        changed_values = x[1]
                        if changed_values.is_a?(Array)
                          old_v = changed_values[0]
                          new_v = changed_values[1]
                        else
                          # I have no idea how audited suddenly from 2 array with old and new values become only 1 value
                          # should be create action but is update action I guess
                          old_v = nil
                          new_v = changed_values
                        end

                        if field_name == "extra_attributes"
                          filter_ea = {}

                          keys = (old_v.keys + new_v.keys).uniq
                          keys.each do |key|
                            next if extra_attr_ignore_list.include?(key)
                            if old_v[key] != new_v[key]
                              filter_ea[key] = {old: old_v[key], new: new_v[key]}
                            end
                          end

                          b(field_name)
                          filter_ea.each do |k, v|
                            br
                            text_node("&nbsp;&nbsp;&nbsp;&nbsp;-".html_safe)
                            b(k)
                            text_node(": #{format_text(v[:old])}")
                            b("==>>")
                            text_node(format_text(v[:new]))
                          end
                        else
                          b(field_name)
                          text_node(": #{format_text(old_v)}")
                          b("==>>")
                          text_node(format_text(new_v))
                        end
                      end
                    end
                  elsif log.action == "create"
                    log.audited_changes.to_a.each do |x|
                      div do
                        b(x[0].to_s)
                        text_node(": #{format_text(x[1])}")
                      end
                    end
                  else
                    log.audited_changes.to_s
                  end
                end
              end
            end
          end
        end
      end
    end
  end

  controller do
    def index
      id = params["user_profile_id"]
      profile = UserProfile.with_deleted.find(id)
      @user_timezone = profile.user.timezone
      @logs = profile.own_and_associated_audits.page(params[:page]).per(50)
    end
  end
end
