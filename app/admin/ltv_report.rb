ActiveAdmin.register_page "LTV Report" do
  menu parent: "reports_or_logs"

  content do
    panel "LTV Report" do
      render "/admin/report/ltv_report"
    end
  end

  controller do
    def index
      @data = CohortAnalysis.select("shopify_domain, source_app, sum(amount) as amount, max(charged_at) as last_charged, min(installed_at) as init_installed").group("shopify_domain, source_app").order("source_app, amount desc")
    end
  end
end
