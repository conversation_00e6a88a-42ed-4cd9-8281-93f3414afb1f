ActiveAdmin.register_page "Shopify SKU Range" do
  menu parent: "reports_or_logs"

  content do
    div class: "grid grid-cols-2" do
      div do
        panel "100 Range" do
          render "number_of_stalls_having_variant_range_every_100"
        end
      end
      div do
        panel "1000 Range" do
          render "number_of_stalls_having_variant_range_every_1000"
        end
      end
    end
  end

  controller do
    def index
      @total_sku_100 = UserProfile
        .joins(:user)
        .select("((user_profiles.total_sku/100)*100+1) as min_range,
                 ((user_profiles.total_sku/100)*100+100) as max_range,
                 count(*) as total_user")
        .where("user_profiles.updated_at = (SELECT MAX(user_profiles.updated_at)
                                            FROM user_profiles
                                            WHERE user_profiles.user_id = users.id)
                                            AND users.charge_id IS NOT NULL")
        .group("user_profiles.total_sku/100")
        .order(1)

      @total_sku_1000 = UserProfile
        .joins(:user)
        .select("((user_profiles.total_sku/1000)*1000+1) as min_range,
                 ((user_profiles.total_sku/1000)*1000+1000) as max_range,
                 count(*) as total_user")
        .where("user_profiles.updated_at = (SELECT MAX(user_profiles.updated_at)
                                            FROM user_profiles
                                            WHERE user_profiles.user_id = users.id)
                                            AND users.charge_id IS NOT NULL")
        .group("user_profiles.total_sku/1000")
        .order(1)
    end
  end
end
