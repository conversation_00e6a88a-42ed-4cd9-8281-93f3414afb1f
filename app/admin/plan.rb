ActiveAdmin.register Plan do
  config.sort_order = "id_asc"

  permit_params :key, :price, :limit, :source_limit, :import_limit, :import_source_limit, :schedule, :min_hour, :group, :popular, :published, :monthly_credit_load, :monthly_credit_plan, :version
  remove_filter :audits
  actions :all, except: [:destroy]

  scope "V1" do |plans|
    plans.where(version: "v1")
  end

  scope "V2" do |plans|
    plans.where(version: "v2")
  end

  scope "V3" do |plans|
    plans.where(version: "v3")
  end

  scope "V4" do |plans|
    plans.where(version: "v4")
  end

  scope "V5" do |plans|
    plans.where(version: "v5")
  end

  scope "Custom Plan" do |plans|
    plans.where(is_custom_plan: true)
  end

  index download_links: false do
    id_column
    column :version
    column :key
    column :price
    column "Variant limits" do |log|
      number_to_human(log.limit)
    end
    column "Max Update feeds" do |log|
      log.source_limit
    end
    column :min_hour
    column "Monthly Credit Reload" do |log|
      "#{log.monthly_credit_plan} #{log.monthly_credit_load}"
    end
    column :published
    column :popular
    actions
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)
    f.inputs "Edit Plan" do
      # input :key, input_html: { disabled: !f.object.new_record? }
      input :key
      input :price, input_html: {disabled: !f.object.new_record?}
      input :limit, input_html: {disabled: !f.object.new_record?}
      input :source_limit, input_html: {disabled: !f.object.new_record?}
      input :import_limit, input_html: {disabled: !f.object.new_record?}
      input :import_source_limit, input_html: {disabled: !f.object.new_record?}
      input :schedule, input_html: {disabled: !f.object.new_record?}
      input :min_hour, input_html: {disabled: !f.object.new_record?}
      input :monthly_credit_plan
      input :monthly_credit_load
      input :group
      input :version
      input :popular
      input :published
    end
    f.actions
  end
end
