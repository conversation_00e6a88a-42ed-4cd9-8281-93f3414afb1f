ActiveAdmin.register_page "Stores Running Multiple Profiles" do
  menu parent: "reports_or_logs"

  content title: "Stores Running Multiple Profiles" do
    user_ids_with_multiple_profiles = UserProfile.where(status: ["queuing", "processing"])
      .group(:user_id)
      .having("COUNT(*) > 1")
      .pluck(:user_id)

    profiles = UserProfile.where(user_id: user_ids_with_multiple_profiles, status: ["queuing", "processing"])
      .select(:id, :user_id, :status, :source_type, :init_start_at, :start_time, :queuing_at, :fargate_task_id, :fargate_task_definition, :file_format)

    profiles_by_user_id = profiles.group_by(&:user_id)

    div class: "w-full bg-gray-800 py-6 px-6" do # Full width and padding for aesthetics
      profiles_by_user_id.each do |user_id, profiles|
        div class: "mb-6" do
          h4 class: "text-lg font-bold text-white mb-4" do
            "User ID: #{user_id} - #{link_to "View User", admin_store_path(user_id), target: "_blank", class: "text-blue-400 hover:underline"}".html_safe
          end

          table class: "w-full table-auto border-collapse bg-gray-900 text-gray-300 rounded-lg shadow-lg" do
            thead class: "bg-gray-700" do
              tr do
                %w[Profile\ ID Status Source\ Type Start Queueing Processing Task\ ID Task\ Def File\ Format].each do |header|
                  th class: "py-3 px-6 text-left font-semibold" do
                    header
                  end
                end
              end
            end

            tbody do
              profiles.each do |profile|
                tr class: "border-b border-gray-700 hover:bg-gray-600" do
                  td class: "py-3 px-6" do
                    link_to(profile.id, admin_user_profile_path(profile.id), target: "_blank", class: "text-blue-400 hover:underline")
                  end
                  td class: "py-3 px-6" do
                    profile.status
                  end
                  td class: "py-3 px-6" do
                    profile.source_type.to_s.titleize
                  end
                  td class: "py-3 px-6" do
                    profile.init_start_at ? "#{time_ago_in_words(profile.init_start_at)} ago" : "-"
                  end
                  td class: "py-3 px-6" do
                    profile.queuing_at ? "#{time_ago_in_words(profile.queuing_at)} ago" : "-"
                  end
                  td class: "py-3 px-6" do
                    profile.start_time ? "#{time_ago_in_words(profile.start_time)} ago" : "-"
                  end
                  td class: "py-3 px-6" do
                    profile.fargate_task_id || "-"
                  end
                  td class: "py-3 px-6" do
                    profile.fargate_task_definition || "-"
                  end
                  td class: "py-3 px-6" do
                    profile.file_format || "-"
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
