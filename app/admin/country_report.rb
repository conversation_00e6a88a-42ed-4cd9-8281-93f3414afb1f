ActiveAdmin.register_page "Country Report" do
  menu parent: "reports_or_logs"

  content do
    panel "Country Report" do
      render "/admin/report/country_report"
    end
  end

  controller do
    def index
      @data = CohortAnalysis.select("source_app, country, sum(amount) as amount").where.not(country: "REDACTED").where.not(source_app: "").group("source_app, country").order("source_app, amount desc")
    end
  end
end
