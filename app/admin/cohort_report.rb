ActiveAdmin.register_page "Cohort Report" do
  menu parent: "reports_or_logs"

  content do
    panel "Cohort Report" do
      render "/admin/report/cohort"
    end
  end

  controller do
    def index
      current_month = Time.now.beginning_of_month
      @months = []
      @users = []
      while current_month >= 12.months.ago
        @months << "#{current_month.year}/#{current_month.month}"
        @users << User.where("created_at < ?", current_month.end_of_month).count
        current_month = (current_month - 1.month).beginning_of_month
      end

      @percentages = []
      @months.reverse_each do |month|
        current_month = Time.now.beginning_of_month
        paid_users = []
        while current_month >= Time.parse(month)

          paid_users << CohortAnalysis.where(source_app: "Stock Sync").where("charged_at > ? and charged_at < ? ", current_month.beginning_of_month, current_month.end_of_month).count

          current_month = (current_month - 1.month).beginning_of_month
        end
        @percentages << paid_users
      end
    end
  end
end
