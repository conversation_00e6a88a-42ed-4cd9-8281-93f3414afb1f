ActiveAdmin.register RawEmailLog do
  menu parent: "reports_or_logs"

  index download_links: false do
    id_column
    column :recipient
    column "Raw Message" do |log|
      log.raw_message.to_s.truncate(50)
    end
    column :created_at do |log|
      "#{log.created_at} #{time_ago_in_words(log.created_at)}"
    end
    actions
  end

  show title: "Raw Email Log" do
    attributes_table do
      row :id
      row :recipient
      row :created_at
      row :raw_message do |log|
        begin
          html = debug JSON.parse(log.raw_message)
        rescue JSON::ParserError
          html = debug log.raw_message
        end
        html.sub!(
          '<pre class="debug_dump"',
          '<pre class="debug_dump" style="white-space:pre-wrap;overflow-wrap:anywhere;word-break:break-all;"'
        ).html_safe
      end
    end
  end
end
