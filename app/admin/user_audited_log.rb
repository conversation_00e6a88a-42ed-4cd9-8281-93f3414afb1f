ActiveAdmin.register_page "User Audited Log" do
  belongs_to :user

  content do
    def format_text(t)
      if t.nil?
        "nil"
      else
        t.to_s
      end
    end

    def format_order(idx)
      page = params[:page]
      if page.nil? || page == "1"
        idx + 1
      else
        idx + 1 + ((page.to_i - 1) * 50)
      end
    end

    panel "Audited Logs" do
      paginated_collection(assigns[:logs], download_links: false) do
        table class: "data-table" do
          thead do
            tr do
              th "No"
              th "Date Time"
              th "Source"
              th "Username"
              th "Action"
              th "Changes", style: "width: 50%"
            end
          end

          tbody do
            pre_created_at = nil
            assigns[:logs].each_with_index do |log, idx|
              tr do
                td(idx + 1)
                td do
                  if log.created_at.to_i == pre_created_at.to_i
                    "-"
                  else
                    pre_created_at = log.created_at
                    "#{log.created_at.in_time_zone("Asia/Singapore")} #{time_ago_in_words(log.created_at.in_time_zone("Asia/Singapore"))}"
                  end
                end
                td(log.auditable_type)
                td(log.username)
                td(log.action)
                td do
                  if log.action == "update"
                    log.audited_changes.to_a.each do |x|
                      div do
                        b x[0].to_s
                        text_node ": #{format_text(x[1][0])}"
                        b "==>>"
                        text_node format_text(x[1][1])
                      end
                    end
                  elsif log.action == "create"
                    log.audited_changes.to_a.each do |x|
                      div do
                        b x[0].to_s
                        text_node ": #{format_text(x[1])}"
                      end
                    end
                  else
                    log.audited_changes.to_s
                  end
                end
              end
            end
          end
        end
      end
    end
  end

  controller do
    def index
      id = params["store_id"]
      @logs = User.find(id).audits.reorder("version DESC").page(params[:page]).per(50)
    end
  end
end
