ActiveAdmin.register EmailLog do
  menu parent: "reports_or_logs"

  actions :all, except: [:destroy, :update, :new]

  filter :recipient
  filter :raw_msg
  filter :created_at

  index download_links: false do
    id_column
    column :recipient
    column "Raw Message" do |log|
      log.raw_msg.to_s.truncate(50)
    end
    column "Attachment" do |log|
      attachment_link = "#{Settings.hostname}#{log.source_file.url}".gsub("/system", "/drive/")
      link_to "Download", attachment_link
    end
    column :created_at
    actions
  end

  show do
    attributes_table do
      row :recipient
      row :raw_msg
      row "HTML" do |log|
        log.raw_msg ? log.raw_msg.html_safe : ""
      end
      row "Attachment" do |log|
        attachment_link = "#{Settings.hostname}#{log.source_file.url}".gsub("/system", "/drive/")
        if attachment_link.to_s.index("missing.png").present?
          "No Attachment"
        else
          link_to "Download", attachment_link
        end
      end
      row "Email Link" do |log|
        email_link = nil

        if log.user_profile.source_type == "email_link"
          if !log.user_profile.path_to_file.present? || !log.raw_msg.present?
            email_link = "Please Check Profile Path To File / Check Raw Msg Empty or Not"
          else
            log.raw_msg.present?
            urls = URI.extract(log.raw_msg, ["http", "https"])
            path_to_file = log.user_profile.path_to_file.to_s
            hyperlink_urls = urls.map do |url|
              if url.to_s.index(path_to_file)
                "<a href='#{url}' target='_blank' style='color: purple;'>#{url}</a>"
              else
                "<a href='#{url}' target='_blank'>#{url}</a>"
              end
            end.join(", ")
            if hyperlink_urls.present?
              email_link = raw(hyperlink_urls)
            end
          end
        end

        email_link
      end
    end
  end
end
