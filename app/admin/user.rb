ActiveAdmin.register User, as: "Store" do
  before_action :update_shopify_store_id, only: [:show]

  menu priority: 2
  actions :all, except: [:new, :destroy]

  filter :all_domain, as: :string, label: "All Domain/Email", filters: [:contains, :equals]
  filter :shopify_domain
  filter :bigcommerce_domain
  filter :woocommerce_domain
  filter :square_domain
  filter :ekm_domain
  filter :squarespace_domain
  filter :wix_domain
  filter :wix_code, label: "Wix Instance ID"
  filter :square_merchant_id
  filter :email
  filter :store_public_token, as: :string, label: "Public Token", filters: [:contains, :equals]
  filter :installed_date, as: :date_range
  filter :status, as: :select
  filter :plan_name
  filter :plan_version
  filter :package, as: :select, collection: Settings.packages.map { |package| [package[:key], package[:key]] }
  filter :pre_plan_version
  filter :limit_skus, as: :numeric
  filter :uninstalled_after_90_days_equals, as: :select, collection: [["Yes", true], ["No", false]], label: "Uninstalled after 90 days"
  filter :api_status, as: :select

  action_item :impersonate, only: :show do
    if current_account
      if resource != true_account.shop && resource != current_account.shop
        link_to "Impersonate", impersonate_admin_stores_url(id: resource.id), class: "action-item-button"
      elsif resource != true_account.shop && resource == current_account.shop
        link_to "Stop impersonate", stop_impersonate_admin_stores_path(id: resource.id), class: "action-item-button"
      end
    end
  end

  action_item :add_profile, only: :show do
    link_to "Add Profile", add_profile_admin_store_url(resource), class: "action-item-button"
  end

  action_item :uninstall_store, only: :show do
    if resource.uninstalled_at.nil?
      link_to "Uninstall", uninstall_store_admin_store_url(resource), data: {confirm: "Are you really really sure to uninstall this store ?"}, class: "action-item-button"
    end
  end

  action_item :cancel_all_feeds, only: :show do
    link_to "Cancel Feeds", cancel_all_running_admin_store_url(resource), data: {confirm: "This will cancel multiple queuing / processing feeds. Confirm?"}, class: "action-item-button"
  end

  # action_item :change_to_trial, only: :show do
  #   link_to 'Extend Trial', change_to_trial_admin_store_url(resource), data: { confirm: 'Are you sure?' }
  # end

  # action_item :clear_products, only: :show do
  #   link_to 'Clear Products', clear_products_admin_store_url(resource), data: { confirm: 'Are you sure?' }
  # end

  action_item :show_locations, only: :show do
    link_to "Show locations", load_locations_admin_store_url(resource), target: :_blank, class: "action-item-button"
  end

  action_item :migrate_user_stocks, only: :show do
    if resource.migrated_user_stocks
      "Migrated user stocks"
    elsif resource.stocks_group.to_i <= 0
      "No stocks group"
    else
      link_to "Migrate User Stocks to Partition ##{resource.stocks_group}", migrate_user_stocks_admin_store_url(resource), class: "action-item-button"
    end
  end

  action_item :import_profile_json, only: :show do
    link_to "Import Profile JSON", import_profile_json_admin_store_url(resource), method: :get, class: "action-item-button"
  end

  member_action :migrate_user_stocks, method: :get do
    user = User.find(params[:id])
    user.migrate_user_stocks
    redirect_to admin_store_url(user), notice: "User stocks migrated"
  end

  member_action :add_profile, method: :get do
    user = User.find(params[:id])
    user.user_profiles.create(webhook_mode: user.provider == "shopify")
    redirect_to admin_store_url(user), notice: "New profile created"
  end

  member_action :clear_products, method: :get do
    user = User.find(params[:id])
    user.user_stocks.delete_all
    redirect_to admin_store_url(user), notice: "Products cleared"
  end

  member_action :load_locations do
    user = User.find(params[:id])

    @locations = begin
      user.active_session.get_locations
    rescue => e
      e.message
    end

    if user.provider == "shopify"
      @all_locations = begin
        ShopifyAPI::LightGraphQL.get_locations(user.shopify_domain, user.shopify_token).dig("data", "locations", "nodes")
      rescue => e
        e.message
      end
      render "load_locations"
    else
      render "load_misc_locations"
    end
  end

  member_action :uninstall_store, method: :get do
    user = User.find(params[:id])
    user.uninstalled_at = Time.now
    user.status = "inactive"
    user.api_status = "inactive"
    user.deactive_profiles(true, "uninstall admin")
    user.email = "<EMAIL>"
    user.notification_email = "<EMAIL>"
    user.save
    redirect_to admin_store_url(user), notice: "Store uninstalled"
  end

  action_item :current_shop, only: :index do
    # TODO: improve logic - sessin["impersonate"] is shop id
    if session["impersonate"] && current_account && current_account.shop.id != session["impersonate"]
      link_to "Go Current Shop", admin_store_url(id: session["impersonate"])
    end
  end

  member_action :shopify_graphiql do
    @page_libraries = {ace: true}
    @store = params[:id]
  end

  member_action :execute_shopify_graphiql, method: :post do
    user = User.find_by(id: params[:id])
    result = if user.present?
      begin
        ::ShopifyAPI::LightGraphQL.query({
          "query" => ::ShopifyAPI::LightGraphQL.clean_query(<<~GRAPHQL
            #{params[:query]}
          GRAPHQL
                                                           ),
          "variables" => (params[:variables] || {}).to_unsafe_h
        },
          user.shopify_domain, user.shopify_token, :json, false, (params[:headers] || {}).to_unsafe_h)
      rescue => e
        {"error" => "#{e.message}\n#{e.backtrace}"}
      end
    else
      {"error" => "user not found"}
    end
    Rails.logger.info(result)
    respond_to do |format|
      format.json { render json: result }
    end
  end

  member_action :search_logs do
    @store = params[:id]
    @profile = UserProfile.where(user_id: User.where(id: params[:id]).pluck(:id).first).limit(1).pluck(:id).first
  end

  member_action :search_change_logs do
    args = {user_id: params[:user_id]}
    if params[:spid].present?
      args[:spid] = params[:spid]
    end
    if params[:svid].present?
      args[:svid] = params[:svid]
    end
    if params.has_key?(:sku)
      args[:sku] = params[:sku].blank? ? nil : params[:sku]
    end

    fields = params[:fields].present? ? params[:fields] : "*"
    limit = params[:size].present? ? params[:size].to_i : 30
    page = params[:page].present? ? params[:page].to_i : 1
    page = if page <= 1
      0
    else
      (page - 1) * limit
    end

    render json: {"success" => true, "type" => params[:type], "data" => ChangeLog.select(fields).where(**args).order(created_at: :desc).limit(limit).offset(page).as_json}
  end

  member_action :search_pinot_logs do
    @store = params[:id]
    @profile = User.find(@store).user_profiles.last
  end

  member_action :schedule_preview do
    user = User.find(params[:id])
    profiles = user.enabled_profiles

    logs_data = ProductLog
      .where("created_at >= ?", 1.day.ago)
      .where(user_profile_id: profiles, user_id: user.id)
      .select(:user_profile_id, :start_time, :end_time,
        :queuing_at, :webhook_query_created_at,
        :webhook_query_completed_at)

    queue_data = []
    process_data = []
    webhook_data = []

    logs_data.each do |log|
      label = "Profile #{log.user_profile_id}"

      if log.queuing_at && log.start_time
        queue_data << {
          x: label,
          y: [log.queuing_at.to_i * 1000, log.start_time.to_i * 1000]
        }
      end

      if log.start_time && log.end_time
        process_data << {
          x: label,
          y: [log.start_time.to_i * 1000, log.end_time.to_i * 1000]
        }
      end

      if log.webhook_query_created_at && log.webhook_query_completed_at
        webhook_data << {
          x: label,
          y: [log.webhook_query_created_at.to_i * 1000,
            log.webhook_query_completed_at.to_i * 1000]
        }
      end
    end

    @series = [
      {name: "Queueing", data: queue_data},
      {name: "Processing", data: process_data},
      {name: "Webhook Query", data: webhook_data}
    ]

    render "schedule_preview", series: @series
  end

  member_action :search_pinot_change_logs, method: :get do
    args = {shop: params[:shop]}
    args[:spid] = params[:spid] if params[:spid].present?
    args[:svid] = params[:svid] if params[:svid].present?
    args[:sku] = params[:sku].presence
    args[:ignore_null_sku] = params[:ignore_null_sku].presence

    limit = params[:size].present? ? params[:size].to_i : 30
    offset = params[:page].present? ? (params[:page].to_i - 1) * limit : 0
    order = params[:order].present? ? params[:order] : "created_at ASC"
    from_date = params[:from_date].present? ? params[:from_date] : nil
    to_date = params[:to_date].present? ? params[:to_date] : nil
    timeout = params[:timeout].present? ? params[:timeout] : "10000"

    sql_query = PinotBrokerQuery.construct_sql_query(args.merge({limit: limit, offset: offset, order: order, from_date: from_date, to_date: to_date}))
    result = PinotBrokerQuery.query_sql(sql_query, timeout)

    if result[:data].present?
      result[:data].each do |val|
        val["time_ago"] = view_context.time_ago_in_words(val["created_at"]).to_s
      end
    end

    render json: {success: true, data: result[:data], error: result[:error]}
  end

  # member_action :change_to_trial do
  #   user = User.find(params[:id])
  #   package = Plan.find_by_key("Trial")
  #   package.activate(user, nil, true)
  #   user.update(installed_date: Time.now)
  #   redirect_to admin_store_url(user), :notice => "Package changed to trial"
  # end

  member_action :extend_trial, method: :post do
    # TODO implement trial extension for different country in future
    user = User.find(params[:id])
    current_plan = user.package
    shopify_plan = user.plan_name
    if current_plan == "Snappy" || current_plan == "Trial" || current_plan == "Free" || (shopify_plan == "partner_test" || shopify_plan == "Developer Preview") || (shopify_plan == "plus_partner_sandbox" || shopify_plan == "Shopify Plus Partner Sandbox")
      extended_days = ((Time.now + params[:days].to_i.days).to_date - user.installed_date.to_date).to_i
      user.update(trial_days: extended_days)
      package = Plan.find_by_key("Trial")
      package.activate(user, nil, true)
      msg = "Trial extended for " + params[:days] + " days"
      data = {
        plan_name: "Trial",
        charge_type: "extend trial",
        remark: "Extend period: #{params[:days]} days. Admin: #{current_admin_user.email}. Expire date: #{user.expiry_date}"
      }
      user.billings.create(data)
    else
      msg = "Not Allowed (Current plan is either not Free, Trial, or Snappy. Shopify plan is not Partner Test or Plus Partner Sandbox)"
    end
    redirect_to admin_store_url(user), notice: msg
  end

  member_action :regenerate_public_token, method: :post do
    user = User.find(params[:id])
    user.old_public_token = user.public_token
    user.regenerate_public_token
    user.save
    redirect_to admin_store_url(user), notice: "Public token successfully regenerated"
  rescue
    redirect_to admin_store_url(user), notice: "Failed to regenerate public token."
  end

  member_action :copy_all_user_profiles, method: :post do
    user = User.find(params[:id])
    transfer_to = User.find(params[:user_id])
    user.user_profiles.where(fast_track: false).each do |profile|
      profile.duplicate_user_profile(user: transfer_to, profile_name: profile.profile_name)
    end

    respond_to do |format|
      format.json { render json: {id: transfer_to.id, message: "Successfully copied to #{transfer_to.shopify_domain}", success: true} }
    end
  end

  member_action :create_store_products_cache, method: :post do
    user = User.find(params[:id])
    result = user.start_products_to_cache
    notice = if result[:feed_id].present?
      "Cache process started (#{result[:feed_id]})"
    else
      "Failed to start cache process (#{result[:message]})"
    end

    redirect_to admin_store_url(user), notice: notice
  end

  member_action :delete_store_products_cache, method: :post do
    user = User.find(params[:id])
    notice = if user.destroy_products_from_cache
      "Cache process deleted"
    else
      "Failed to delete cache process"
    end

    redirect_to admin_store_url(user), notice: notice
  end

  member_action :import_profile_json, method: [:get, :post] do
    if request.get?
      render :import_profile_json
      return
    end

    json_str = params[:json]
    user_id = params[:id]
    profile_hash = JSON.parse(json_str)
    profile_hash["user_id"] = user_id
    if profile_hash.dig("decrypted_password").present?
      password = profile_hash.dig("decrypted_password")
      profile_hash.delete("decrypted_password")
    end
    sync_fields_hash = profile_hash.delete("sync_fields")

    profile = UserProfile.create(profile_hash)
    sync_fields_hash.each do |sfh|
      profile.sync_fields.create(sfh)
    end

    if password.present?
      pm = PasswordManager.encrypt(password)

      profile.acc_password = pm[:password]
      profile.password_salt = pm[:salt]
      profile.save
    end

    flash[:notice] = "Successfully imported as profile #{profile.id}"
    redirect_to admin_user_profile_url(profile)
  rescue JSON::ParserError
    flash[:error] = "Invalid JSON"
    redirect_to import_profile_json_admin_store_path
  rescue => e
    flash[:error] = e.message
    redirect_to import_profile_json_admin_store_path
  end

  collection_action :impersonate, method: :get do
    shop_id = params[:id]
    account = Account.find_by(shop_id: shop_id)

    user = User.find(shop_id)
    if !user.uninstalled_at.present? && user.valid_api_check?

      if account_signed_in?
        impersonate_account(account)
        session["impersonate"] = shop_id
        url = "#{Settings.hostname}/?force_standalone=true"
      else
        url = new_account_session_url
      end
      redirect_to url, allow_other_host: true
    else
      flash[:error] = "Not a valid user to impersonate."
      redirect_back fallback_location: admin_root_path
    end
  end

  collection_action :stop_impersonate, method: :get do
    shop_id = params[:id]
    account = Account.find_by(shop_id: shop_id)
    session["impersonate"] = nil
    cookies.delete :impersonating_session
    stop_impersonating_account
    flash[:notice] = "Successfully stopped impersonating #{account.email}"
    redirect_to admin_store_url(shop_id)
  end

  member_action :cancel_all_running, method: :get do
    shop_id = params[:id]
    user = User.find(shop_id)
    user.user_profiles.where(status: ["queuing", "processing"]).each do |profile|
      profile.cancel_process("admin cancel all running process")
    end
    flash[:notice] = "Successfully stop all processing / queuing profiles"
    redirect_to admin_store_url(shop_id)
  end

  member_action :reset_shopify_token, method: :post do
    user = User.find(params[:id])
    user.shopify_token = ""
    user.save(validate: false)
    Rails.cache.delete(CacheKey.is_valid_shopify_shop(user.id))
    flash[:notice] = "Successfully reset Shopify token for user #{user.shopify_domain} #{user.id}"
    redirect_to admin_store_url(params[:id])
  end

  member_action :reset_shopify_cache, method: :post do
    user = User.find(params[:id])
    Rails.cache.delete(CacheKey.is_valid_shopify_shop(user.id))
    Rails.cache.delete(CacheKey.shopify_user_id(user.shopify_domain))
    Rails.cache.delete(CacheKey.shopify_account_id(user.shopify_domain))
    flash[:notice] = "Successfully reset Shopify cache for user #{user.shopify_domain} #{user.id}"
    redirect_to admin_store_url(params[:id])
  end

  member_action :reset_valid_api_check_cache, method: :post do
    Rails.cache.delete(CacheKey.valid_api_check(params[:id]))
    flash[:notice] = "Successfully reset valid API Status cache for user ID #{params[:id]}"
    redirect_to admin_store_url(params[:id])
  end

  member_action :toggle_unlimited_import, method: :post do
    user = User.find(params[:id])
    if params[:enabled] == "true"
      (user.extra_options || {}).delete("shopify_unlimited_import")
    else
      user.extra_options ||= {}
      user.extra_options["shopify_unlimited_import"] = true
    end
    user.save(validate: false)
    redirect_to admin_store_url(user), notice: "Updated"
  end

  member_action :enable_translation, method: :post do
    user = User.find(params[:id])
    enable = !(params[:enable] == "true")
    user.extra_options ||= {}
    user.extra_options["enable_translation"] = enable
    user.save(validate: false)
    redirect_to admin_store_url(user), notice: "Updated"
  end

  member_action :check_valid_api do
    data = "inactive"
    v = User.find(params[:id]).valid_api_check?
    data = "active" if v
    render plain: data, layout: false
  end

  member_action :adjust_credits, method: :post do
    user = User.find(params[:id])
    amount = params[:amount].to_i
    old_amount = user.import_limit_skus.to_i
    adjustment = old_amount + amount
    adjustment = 0 if adjustment < 0
    user.update(import_limit_skus: adjustment)
    data = {
      plan_name: "Manual Credit Adjustment",
      charge_type: "manual credit adjustment",
      remark: "Admin: #{current_admin_user.email}. Previous credits: #{old_amount}. \
       Current credits: #{user.import_limit_skus}. \
       Amount changed: #{amount}
      "
    }
    user.billings.create(data)
    redirect_to admin_store_url(user), notice: "Updated"
  end

  action_item only: :index do
    link_to "Marketing Email", download_csv_admin_stores_path(url: request.original_url), data: {confirm: "Will take a long time. Are you sure?"}, class: "action-item-button"
  end

  collection_action :download_csv do
    redirect_to action: :download_csv
  end

  controller do
    content_security_policy do |policy|
      policy.worker_src :self, :blob
    end

    def update_shopify_store_id
      user = User.find(params[:id])
      if user.provider == "shopify" && user.platform_store_id.nil?
        begin
          info = user.get_user_info
          raise StandardError.new("Missing info") if info.blank?
          user.update(platform_store_id: info["id"].split("/").last)
        rescue => e
          Rails.logger.error(e)
        end
      end
    end

    def download_csv
      url = params[:url]
      uri = URI.parse(url.strip)
      uri_params = uri.query ? CGI.parse(uri.query).except("utf8", "commit", "order") : nil
      uri_scope = uri_params ? uri_params["scope"] : nil
      scope = uri_scope ? uri_scope[0] : nil
      filter_params = uri_params&.except("scope")

      respond_to do |format|
        format.html { send_data User.to_csv(scope: scope, filter: filter_params), filename: "users-#{Date.today}.csv" }
      end
    end

    def update
      if params[:user][:password].present?
        if params[:user][:password] == params[:user][:password_confirmation]
          resource.account.take.update(password: params[:user][:password])
          flash[:notice] = "Password updated successfully"
        else
          flash[:error] = "Password confirmation does not match"
          return render :edit
        end
      end

      params[:user].delete(:password)
      params[:user].delete(:password_confirmation)
      super
    end

    after_save do
      user = User.find(params[:id])
      user.clear_cache_enable_profiles
    end
  end

  permit_params :name, :email, :installed_date, :charge_id, :charge_date,
    :profile_limit, :status, :limit_skus, :auto_get_higher_qty, :coupon_code,
    :stocks_group, :import_package, :import_limit_skus, :import_profile_limit, :remove_profile_limit,
    :min_hour, :import_min_hour, :remark, :pricing_group, :billing_partner_id,
    :package, :plan_version, :pre_plan_version, :woocommerce_protocol,
    :private_app_key, :private_app_secret, :woocommerce_domain,
    :woocommerce_key, :woocommerce_secret, :warning_zero_qty_update, :square_domain,
    :file_size_limit, :api_query_limit, :customer_email,
    :notification_email, :auto_get_lower_qty, :priority, :warning_remove_products, :password, :password_confirmation

  scope :all, default: true
  scope "Shopify" do |users|
    users.where(provider: "shopify")
  end
  scope "Bigcommerce" do |users|
    users.where(provider: "bigcommerce")
  end
  scope "Woocommerce" do |users|
    users.where(provider: "woocommerce")
  end
  scope "Wix" do |users|
    users.where(provider: "wix")
  end
  scope "Square" do |users|
    users.where(provider: "square")
  end
  scope "Ekm" do |users|
    users.where(provider: "ekm")
  end
  scope "Squarespace" do |users|
    users.where(provider: "squarespace")
  end
  scope "Quickbooks" do |users|
    users.where(provider: "quickbooks")
  end
  scope "Prestashop" do |users|
    users.where(provider: "prestashop")
  end
  scope "Paid" do |users|
    users.where("charge_id IS NOT NULL")
  end
  scope "Full Access" do |users|
    users.installed_witin(0, Settings.free_trial_days)
  end
  scope "Expired in 3 days" do |users|
    users.installed_witin(Settings.free_trial_days.to_i - 3, Settings.free_trial_days)
  end
  scope "Uninstall within 1 day" do |users|
    users.where("uninstalled_at between created_at AND created_at + INTERVAL '24 HOURS'")
  end
  scope "Uninstall after 90 days" do |users|
    users.where("uninstalled_at - installed_date > INTERVAL '90 days'")
  end

  csv do
    column :shopify_domain
    column :bigcommerce_domain
    column :woocommerce_domain
    column :wix_domain
    column :square_domain
    column :email
  end

  index do
    id_column
    column :view do |user|
      link_to "View", view_user_admin_user_profiles_url(user_id: user.id), data: {confirm: "Are you sure?"}
    end
    column :provider
    column :domain do |user|
      if user.shopify_domain.present?
        link_to "#{user.shopify_domain}<br>#{user.email}".html_safe, "http://#{user.shopify_domain}", "target" => "_blank"
      elsif user.bigcommerce_domain.present?
        link_to "#{user.bigcommerce_domain}<br>#{user.email}".html_safe, "http://#{user.bigcommerce_domain}", "target" => "_blank"
      elsif user.woocommerce_domain.present?
        link_to "#{user.woocommerce_domain}<br>#{user.email}".html_safe, "http://#{user.woocommerce_domain}", "target" => "_blank"
      elsif user.wix_domain.present?
        link_to "#{user.wix_domain}<br>#{user.email}".html_safe, "http://#{user.wix_domain}", "target" => "_blank"
      elsif user.square_domain.present?
        link_to "#{user.square_domain}<br>#{user.email}".html_safe, "http://#{user.square_domain}", "target" => "_blank"
      else
        "Unknown domain"
      end
    end
    column :installed_date
    column :status
    column :charge_id
    column :credits do |user|
      user.import_limit_skus
    end
    column :monthly_credit
    column :package do |user|
      (user.package == "Snappy") ? "Free plan" : user.package
    end
    column :priority

    column :actions do |user|
      div class: "relative" do
        button id: "dropdownMenuIconButton_#{user.id}", type: "button", data: {dropdown: {toggle: "dropdownDots_#{user.id}"}}, class: "inline-flex items-center p-2 text-sm font-medium text-center text-gray-900 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-600" do
          svg class: "w-5 h-5", aria: {hidden: "true"}, xmlns: "http://www.w3.org/2000/svg", fill: "currentColor", viewBox: "0 0 16 3" do
            text_node "<path d=\"M2 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm6.041 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM14 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Z\"/>".html_safe
          end
        end

        div id: "dropdownDots_#{user.id}", class: "z-10 hidden absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600", style: "min-width:140px;" do
          ul class: "py-2 text-sm text-gray-700 dark:text-gray-200", aria: {labelledby: "dropdownMenuIconButton"} do
            if current_account.nil? || current_account.shop != user
              li do
                a href: impersonate_admin_stores_url(id: user.id), class: "block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do
                  "Impersonate"
                end
              end
            end
            if current_account.nil? || current_account.shop == user && current_account != true_account
              li do
                a href: stop_impersonate_admin_stores_url(id: user.id), class: "block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do
                  "Stop Impersonate"
                end
              end
            end
            if current_account&.shop == user
              li do
                span "Current User"
              end
            end
          end
          div class: "py-2 border-t border-gray-200 dark:border-gray-600" do
            div class: "px-4 py-2" do
              "Copy all user profiles to.."
            end
            div class: "px-3" do
              input type: "text", id: "user_id_#{user.id}", class: "block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500", placeholder: "Store ID", required: true
            end
            a href: "#", data: {target: user.id.to_s, method: "copy_all_user_profiles", resource: "stores"}, class: "copy_all_profiles_to block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white no-underline" do
              "Copy"
            end
          end
        end
      end
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)

    f.inputs "Edit User" do
      input :api_query_limit
      input :remark
      input :min_hour
      input :import_min_hour
      input :profile_limit
      input :limit_skus
      input :priority
      input :import_profile_limit
      input :remove_profile_limit
      input :auto_get_higher_qty
      input :auto_get_lower_qty
      input :coupon_code
      input :billing_partner_id
      input :warning_zero_qty_update
      input :warning_remove_products
      if ["Trial", "Snappy"].include? f.object.package
        input :package, as: :select, include_blank: false, collection: {"Full access" => "Trial", "Free plan" => "Snappy"}
      end

      if ["shopify", "woocommerce", "bigcommerce", "wix", "prestashop"].include?(object.provider)
        input :plan_version, as: :select, include_blank: false, collection: {"v1" => "v1", "v2" => "v2", "v3" => "v3", "v4" => "v4", "v5 latest " => "v5"}

        if current_admin_user.role == "admin"
          input :pre_plan_version, as: :select, collection: {"v1" => "v1", "v2" => "v2", "v3" => "v3", "v4" => "v4", "v5 latest " => "v5"}
        end

      else
        input :plan_version, as: :select, include_blank: false, collection: {"v1" => "v1", "v2" => "v2", "v3" => "v3", "v4 latest " => "v4"}
        if current_admin_user.role == "admin"
          input :pre_plan_version, as: :select, collection: {"v1" => "v1", "v2" => "v2", "v3" => "v3", "v4 latest " => "v4"}
        end
      end
      input :private_app_key
      input :private_app_secret
      input :file_size_limit

      if object.provider == "woocommerce"
        input :woocommerce_domain
        input :woocommerce_key
        input :woocommerce_secret
        input :woocommerce_protocol, as: :select, include_blank: false, collection: {"http" => "http", "https" => "https"}
      elsif object.provider == "bigcommerce"
        input :multi_location_enabled
      end
    end

    f.inputs "Reset Password" do
      f.input :password, as: :password, input_html: {style: "width: 200px"}
      f.input :password_confirmation, as: :password, input_html: {style: "width: 200px"}
    end

    f.inputs "Update Email" do
      input :email
      input :notification_email
      input :customer_email
    end
    f.actions
  end

  show title: proc { |u|
                domain = if u.provider == "wix"
                  u.wix_domain
                else
                  u.shopify_domain
                end
                "#{domain.blank? ? u.shopify_domain : domain} - #{u.provider.capitalize} (#{u.status})"
              } do |user|
    div class: "grid grid-cols-2" do
      if user.remark.present?
        div do
          panel "Remark" do
            user.remark.to_s.html_safe
          end
        end
      end
      running_feeds = user.user_profiles.where(status: ["processing", "queuing"]).pluck(:id)
      if running_feeds.count.positive?
        div do
          panel "Processing / Queuing Profiles" do
            running_feeds.map { |profile_id| link_to profile_id, admin_user_profile_path(profile_id) }.join(" ").html_safe
          end
        end
      end
    end

    panel "User Profiles" do
      colors = {"update" => "green  ", "import" => "blue", "remove" => "red", "export" => "purple"}
      enabled_profiles = user.enabled_profiles
      table_for user.user_profiles.includes(:latest_product_log).with_deleted.order("deleted_at desc", "id desc") do
        column :id do |d|
          link_to d.id, admin_user_profile_url(d), target: "_blank"
        end

        column :feed_type do |d|
          text = d.humanize_feed_type.titleize
          text += " (#{(d.delete_mode == 1) ? "Archive" : "Delete"})" if d.feed_type == "remove"
          "<span style='color: #{colors[d.humanize_feed_type]}'>#{text}</span>".html_safe
        end

        column :profile_name do |d|
          v = if d.deleted?
            "(&#10008;) <del>#{d.profile_name}</del>"
          else
            d.profile_name
          end
          v += "<br>(Disabled)" if !enabled_profiles.include?(d.id) && !d.fast_track?
          v.html_safe
        end

        column :misc do |d|
          auto_published = if d.published_apply_to_all
            "All"
          elsif d.published_apply_matching_products
            "Matching"
          else
            "None"
          end

          auto_unpublished = if d.unpublished_apply_to_all
            "All"
          elsif d.unpublished_apply_matching_products
            "Matching"
          else
            "None"
          end

          misc = "Out of Stock: #{(d.auto_reset_quantity || d.hide_unmatch_products) ? "&#10003;" : "&#10008;"} <br>"
          misc << "Auto published: #{auto_published} <br>"
          misc << "Auto unpublished: #{auto_unpublished} <br>"
          misc.html_safe
        end

        column :info do |d|
          msg = if d.scheduler_enabled
            "&#10003; #{d.job_type} #{(d.job_type == "periodically") ? d.job_interval : d.job_time}"
          else
            "&#10008; No schedule"
          end

          html = "#{d.status}/#{d.fargate_task_id}<br>"
          html << msg
          html << "<br>#{d.source_type} (#{d.processing_no})"
          html << "<br>".html_safe
          html << link_to("Check Locations",
            "#",
            class: "get-profile-locations",
            data: {target: "user-profile-check-loc-#{d.id}",
                   profile: d.id})
          html << %(<br><div id="user-profile-check-loc-#{d.id}" style="display:inline;"></div>)
          html.html_safe
        end

        column :lastest_log do |d|
          log = d.latest_product_log
          if log
            a = log.status ? d.stats(log) : "Fail: #{log.remark}"
            a << "<br> run #{log.created_at} #{time_ago_in_words(log.created_at)}"
            taken = if log.start_time && log.end_time
              distance_of_time_in_words(log.start_time, log.end_time)
            else
              "-"
            end
            a << "<br> last taken : #{taken}"
            a.html_safe
          end
        end

        column :filter do |d|
          "Shopify: #{UserProfile.shopify_filter(d)} <br> Feed:#{UserProfile.feed_filter(d)}".html_safe
        end
      end
    end

    div class: "grid grid-cols-2 gap-6" do
      attributes_table_for user do
        row :id do |user|
          body = "<span id='user-shopify-domain' style='margin-right: 7px'>#{user.shopify_domain}</span>"
          if user.shopify_domain.present?
            case user.platform
            when "shopify"
              body << link_to('<img style="vertical-align:middle" src="https://img.icons8.com/material-outlined/20/000000/copy.png"/>'.html_safe, "#", class: "copy-button", data: {target: "user-shopify-domain"})
            when "wix"
              body << link_to("Wix History", "https://dev.wix.com/dc3/my-apps/24443325-8a29-46aa-b21a-9fccb18022c9/payouts/history")
            end
            body.html_safe
          end
        end

        row "Extend Trial" do
          div class: "flex items-center gap-4" do
            out = (user.remaining_trial_days > 0) ? "#{user.remaining_trial_days} days left" : "Expired"
            out << if (user.plan_name == "partner_test" || user.plan_name == "Developer Preview") || (user.plan_name == "plus_partner_sandbox" || user.plan_name == "Shopify Plus Partner Sandbox")
              form_tag extend_trial_admin_store_url(user), class: "flex items-center gap-4", method: :post do |f|
                select_tag("days", options_for_select([["90 days", 90], ["180 days", 180], ["365 days", 365]], "no filter")) << " " << submit_tag("Extend", class: "filters-form-submit", data: {confirm: "Are you sure to extend the trial ?"})
              end
            else
              form_tag extend_trial_admin_store_url(user), class: "flex items-center gap-4", method: :post do |f|
                select_tag("days", options_for_select([["14 days", 14], ["30 days", 30], ["60 days", 60], ["90 days", 90]], "no filter")) << " " << submit_tag("Extend", class: "filters-form-submit", data: {confirm: "Are you sure to extend the trial ?"})
              end
            end
            out.html_safe
          end
        end

        row :timestamp do |user|
          "#{user.timezone} - <b>Install:</b> #{user.installed_date} <b>Created:</b> #{user.created_at} <b>Updated:</b> #{user.updated_at}".html_safe
        end
        row :uninstalled_at

        row "User Check API" do |user|
          out = "#{link_to "Check API Status", "#", class: "is-valid-api-check", data: {target: "user-check-api-#{user.id}"}, target: "_blank"}
            <div id=\"user-check-api-#{user.id}\" style=\"display:inline;\"></div> "
          out << if user.present?
            form_tag reset_valid_api_check_cache_admin_store_url(user), method: :post, style: "display:inline-block;" do |f|
              submit_tag("Reset API Status", class: "filters-form-submit", data: {confirm: "Are you sure want to reset this store API Status cache ?"})
            end
          end
          out.html_safe
        end
        row :failure_count
        row :charge do |user|
          "Shopify Plan: #{user.plan_name} #{user.charge_period} / Charge Info:#{user.charge_id} #{user.charge_date}"
        end
        row :auto_get_higher_qty
        row :auto_get_lower_qty
        row :email do |user|
          links = []
          if user.email.present?
            links << link_to(user.email, user.generate_mailgun_url(user.email), target: "_blank")
          end
          if user.customer_email.present?
            links << link_to(user.customer_email, user.generate_mailgun_url(user.customer_email), target: "_blank")
          end
          links.join(", ").html_safe
        end
        row :notification_email do |user|
          if user.notification_email.present?
            user.notification_email.split(",").map do |email|
              link_to(email.strip, user.generate_mailgun_url(email.strip), target: "_blank")
            end.join(", ").html_safe
          end
        end
        row :email_subscriptions do |user|
          "<ul>#{user.email_subscriptions.collect { |a| "<li>#{I18n.t(a)}</li>" }.join("")}</ul>".html_safe
        end
      end

      attributes_table_for user do
        row :download_url do |user|
          download_products_home_index_url(host: Settings.hostname, token: user.public_token, format: :csv)
        end
        row :old_public_token do |user|
          user.old_public_token
        end
        row :public_token do |user|
          out = "".html_safe
          out << user.public_token
          out << form_tag(regenerate_public_token_admin_store_url(user), method: :post) do
            submit_tag("Regenerate Token", class: "filters-form-submit", style: "margin-top:8px")
          end

          out
        end
        row :plan_name

        row :search_logs do |user|
          link_to("search logs via sku", search_logs_admin_store_url(user), target: "_blank") +
            " | " +
            link_to("search new logs via sku", search_pinot_logs_admin_store_path(user), target: "_blank")
        end

        if user.provider == "shopify"
          row "Shopify Store GraphiQL" do |profile|
            link_to("Explore", shopify_graphiql_admin_store_url(user), target: "_blank")
          end
        end

        row :products_cache do |user|
          out = ""
          cache_status = RedisManager.store_products_cache_ready?(user.id) ?
            "<div style='color: green; margin-bottom: 10px'>Products Currently Cached</div>" :
            "<div style='color: red; margin-bottom: 10px'>No Products Cached</div>"

          out << cache_status
          out << if user.present?
            form_tag create_store_products_cache_admin_store_url(user), method: :post, style: "display:inline" do |f|
              submit_tag("Cache Products", class: "filters-form-submit", style: "margin-top:8px", data: {confirm: "Are you sure to cache store products ? (this will affect import/delete feed of the entire store)"})
            end
          end
          out << "<br>"
          out << if user.present?
            form_tag delete_store_products_cache_admin_store_url(user), method: :post, style: "display:inline" do |f|
              submit_tag("Delete Cache Products", class: "filters-form-submit", style: "margin-top:8px", data: {confirm: "Are you sure to delete cache store products ?"})
            end
          end
          out.html_safe
        end

        if user.provider == "woocommerce"
          row :woocommerce_key
          row :woocommerce_secret
        end

        if user.provider == "bigcommerce"
          row :bigcommerce_store_hash
          row :bigcommerce_access_token
        end

        if user.provider == "wix"
          row :wix_code
          row :wix_domain
        end

        if user.provider == "square"
          row :square_merchant_id
          row :square_location_id
          row :square_access_token
          row :square_refresh_token
        end

        if user.provider == "ekm"
          row :ekm_access_token
          row :ekm_refresh_token
          row :ekm_domain
          row :ekm_code
        end

        if user.stripe_customer_id
          row :stripe_customer_id do
            link_to(user.stripe_customer_id, "https://dashboard.stripe.com/customers/#{user.stripe_customer_id}", target: "_blank")
          end
        end

        if user.stripe_subscription_id
          row :stripe_cusstripe_subscription_idtomer_id do
            link_to(user.stripe_subscription_id, "https://dashboard.stripe.com/subscriptions/#{user.stripe_subscription_id}", target: "_blank")
          end
        end

        row :audited_log do |user|
          link_to "View Audited Logs", admin_store_user_audited_log_path(user), target: "_blank"
        end
        if user.provider == "shopify" && current_admin_user.role == "admin"
          row :reset_shopify do |user|
            out = ""
            out << if user.present?
              form_tag reset_shopify_token_admin_store_url(user), method: :post, style: "display:inline-block; margin-right: 10px;" do |f|
                submit_tag("Shopify Token", class: "filters-form-submit", data: {confirm: "Are you sure want to reset this store Shopify token ?"})
              end
            end
            out << if user.present?
              form_tag reset_shopify_cache_admin_store_url(user), method: :post, style: "display:inline-block;" do |f|
                submit_tag("Shopify Cache", class: "filters-form-submit", data: {confirm: "Are you sure want to reset this store Shopify cache ?"})
              end
            end
            out.html_safe
          end

          row :toggle_unlimited_import do |user|
            enabled = (user.extra_options || {})["shopify_unlimited_import"] || false
            "<div>#{enabled ? "Manual unlimited import enabled" : "Manual unlimited import disabled"}<div>
            #{form_tag toggle_unlimited_import_admin_store_url(user, enabled: enabled), method: :post, style: "display:inline" do |f|
              submit_tag("#{enabled ? "Disable" : "Enable"} Unlimited Import", class: "filters-form-submit")
            end}".html_safe
          end

          row :enable_translation do |user|
            enable = (user.extra_options || {})["enable_translation"] || false
            "<div>#{enable ? "Translation enabled" : "Translation disabled"}<div>
            #{form_tag enable_translation_admin_store_url(user, enable: enable), method: :post, style: "display:inline" do |f|
              submit_tag("#{enable ? "Disable" : "Enable"} Translation", class: "filters-form-submit")
            end}".html_safe
          end
        end
        row :schedule_preview do |user|
          link_to "View Last 24 Hours Store's Run", schedule_preview_admin_store_url(user), target: "_blank"
        end
      end
    end

    div class: "grid grid-cols-2 gap-6" do
      div do
        panel "Update Details" do
          attributes_table_for user do
            row :package do |user|
              "<strong>#{user.plan_version}</strong> #{user.package} Update: #{user.is_valid?("update") ? "&#10003;".html_safe : "&#10008;"} / Import: #{user.is_valid?("import") ? "&#10003;".html_safe : "&#10008;"}".html_safe
            end
            row("Pre Plan Version") { |user| user.pre_plan_version }
            row :profile_limit
            row :limit_skus
            row("Update usage") { |user| user.total_skus_count }
            row :is_custom_plan do |user|
              user.has_custom_plan_from_admin?
            end
            row :update_min_hour do |user|
              user.min_hour
            end
          end
        end
      end

      div do
        panel "Import/Remove Details" do
          attributes_table_for user do
            row :import_package
            row :import_profile_limit
            row("Credits") do |user|
              out = "".html_safe
              out << "Adhoc: #{user.import_limit_skus} / Monthly: #{user.monthly_credit}"
              out << form_tag(adjust_credits_admin_store_url(user), method: :post) do
                number_field_tag(:amount, 0,
                  min: -10000,
                  max: 10000,
                  style: "width: 200px") <<
                  submit_tag("Adjust credits",
                    data: {disable_with: false},
                    class: "filters-form-submit")
              end
              out
            end
            row :import_min_hour do |user|
              user.import_min_hour
            end
            row :remove_profile_limit
          end
        end
      end
    end

    panel "Notification Logs #{link_to "View all", admin_notification_logs_url(order: "created_at_desc", q: {user_id_eq: user.id})}".html_safe do
      table_for user.notification_logs.order("send_at desc").limit(3) do
        column :id
        column :sync_status
        column :send_at do |log|
          log.send_at ? "#{log.send_at.in_time_zone(user.timezone).strftime("%m-%d %H:%M %z")} (#{time_ago_in_words(log.send_at)}) " : "-"
        end
      end
    end

    panel "Billing Logs" do
      table_for user.billings.order("id desc") do
        column :id
        column :charge_id do |log|
          if user.provider == "shopify"
            log.charge_id
          elsif user.charge_id.present?
            link_to(user.charge_id, "https://dashboard.stripe.com/search?query=#{user.charge_id}", target: "_blank")
          else
            ""
          end
        end
        column :created_at do |log|
          "#{log.created_at} (#{time_ago_in_words(log.created_at)})"
        end
        column :plan_name
        column :status
        column :addon_charge
        column :total_charge
        column :charge_type
        column :remark
      end
    end

    panel "Custom Plans" do
      table_for Plan.where("key like ?", "Custom Plan #{user.shopify_domain}_%").order("id desc") do
        column :id
        column :key
        column :price
        column "Variant limits" do |p|
          p.limit
        end
        column "Max Update feeds" do |p|
          p.source_limit
        end
        column :min_hour
        column "Monthly Credit Reload" do |p|
          "#{p.monthly_credit_plan} #{p.monthly_credit_load}"
        end
      end
    end

    panel "Products" do
      render partial: "search", locals: {user: user}
    end
  end
end
