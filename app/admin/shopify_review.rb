ActiveAdmin.register ShopifyReview do
  menu parent: "reports_or_logs"
  config.sort_order = "times_desc"

  actions :all, except: [:destroy, :update, :new]

  action_item :top_word_stock_sync, only: :index do
    link_to "Top words - Stock Sync", top_words_admin_shopify_reviews_url(app: "stock-sync"), target: :_blank
  end

  action_item :top_word_syncee, only: :index do
    link_to "Top words - Syncee", top_words_admin_shopify_reviews_url(app: "syncee"), target: :_blank
  end

  action_item :download_store_without_reviews, only: :index do
    link_to "Download Shopify", download_admin_shopify_reviews_url(app: "syncee"), target: :_blank
  end

  action_item :download_ss, only: :index do
    link_to "Download SS", download_all_admin_shopify_reviews_url(app: "stock-sync"), target: :_blank
  end

  action_item :download_ex, only: :index do
    link_to "Download EX", download_all_admin_shopify_reviews_url(app: "exportible"), target: :_blank
  end

  action_item :download_fs, only: :index do
    link_to "Download FS", download_all_admin_shopify_reviews_url(app: "fulfillsync"), target: :_blank
  end

  scope :all, default: true
  scope "Stock Sync" do |reviews|
    reviews.where(app: "stock-sync")
  end
  scope "Exportible" do |reviews|
    reviews.where(app: "exportible")
  end
  scope "FulfillSync" do |reviews|
    reviews.where(app: "fulfillsync")
  end

  collection_action :download_all, method: :get do
    csv = CSV.generate do |csv|
      csv << ["Messag", "Date", "Store", "Star"]
      reviews = ShopifyReview.where(app: params["app"]).all.order("star")
      reviews.each do |review|
        csv << [review.post, review.times, review.link, review.star]
      end
    end
    send_data csv, type: "text/csv; header=present", disposition: "attachment; filename=allreviews.csv"
  end

  collection_action :download, method: :get do
    csv = CSV.generate do |csv|
      csv << ["Store name", "Email", "Created_at", "Plan"]
      users = User.where(platform: "shopify").where.not(charge_id: nil).where.not(package: ["Snappy", "Trial"]).where("created_at < ?", 30.days.ago).where("shopify_domain not in (select link from shopify_reviews)").where("email_subscriptions like '%newsletter%'")
      users.each do |user|
        csv << [user.shopify_domain, user.notification_email || user.email, user.created_at, user.package]
      end
    end
    send_data csv, type: "text/csv; header=present", disposition: "attachment; filename=unreview_stores.csv"
  end

  collection_action :top_words, method: :get do
    posts = ShopifyReview.where(app: params["app"])
    @words = {}
    posts.each do |post|
      post.post.split(" ").each do |word|
        next if word.gsub(/[,.!ai]/i, "").blank?
        @words[word] = [] unless @words[word]
        @words[word] << post.id
      end
    end
    @words = @words.sort_by { |_key, value| value.count }.to_a.reverse.to_h
    render "top_words"
  end

  filter :link
  filter :star
  filter :app
  filter :times
  filter :post

  index download_links: false do
    column "Review" do |review|
      review.post.truncate(120)
    end
    column :app
    column :link
    column :star
    column :times
    actions
  end
end
