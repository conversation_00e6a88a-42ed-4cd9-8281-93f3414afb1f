ActiveAdmin.register_page "Stripe Subscriptions" do
  menu parent: "reports_or_logs"

  content do
    panel "Active Subscriptions" do
      render "/admin/report/stripe_subscriptions"
    end
  end

  controller do
    def index
      all_subscriptions = []
      starting_after = nil
      loop do
        query_params = {
          limit: 100,
          starting_after: starting_after,
          expand: ["data.customer", "data.plan"],
          status: "active" # Hardcode status to active
        }

        result = Stripe::Subscription.list(query_params)
        current_page_subscriptions = result.data

        all_subscriptions.concat(current_page_subscriptions)

        break unless result.has_more
        starting_after = result.data.last.id
      end

      @subscriptions = all_subscriptions
      @total_count = @subscriptions.length
    end
  end
end
