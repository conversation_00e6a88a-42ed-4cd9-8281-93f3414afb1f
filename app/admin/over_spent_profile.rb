ActiveAdmin.register_page "Over spent feeds" do
  menu parent: "reports_or_logs"

  content do
    panel "User Report" do
      render "/admin/report/over_spent_feed"
    end
  end

  controller do
    def index
      @data = {}
      @fargate_tasks = UserProfile.last.available_fargate_tasks
      @data["blank"] = UserProfile.where(fargate_task_definition: nil).where.not(status: "pause").limit(20).order("total_sku desc")
      @fargate_tasks.each do |task|
        list = []
        list << UserProfile.where("fargate_task_definition = ?", task).where.not(status: "pause").limit(10).order("total_sku desc")
        list << "..."
        list << UserProfile.where("fargate_task_definition = ?", task).where.not(status: "pause").limit(10).order("total_sku asc")
        @data[task] = list.flatten
      end
    end
  end
end
