ActiveAdmin.register UserProfile do
  includes :user
  includes :product_log_invalid_today
  menu priority: 3
  actions :all, except: [:destroy]
  config.sort_order = "id_asc"
  permit_params :update_only_when_zero_qty, :store_filters, :source_file_file_name, :clear_google_sheet, :remove_product_when_all_locations_nil, :bypass_cache, :source_type, :cache_expiry, :acc_name, :acc_password, :source_url, :source_auth, :file_format,
    :user_id, :source_file, :path_to_file, :parent_node, :job_type, :job_time, :job_interval, :prefix, :store_prefix, :has_header, :file_name,
    :public_file, :password_salt, :password_key, :profile_name, :profile_status, :io_mode, :email,
    :rules, :ssh_key, :eq_hide_value, :eq_show_value, :shopify_product_key, :price_formula, :status,
    :variant_key_1, :variant_key_2, :variant_key_3, :option_key_1, :option_key_2, :source_key_1, :source_key_2, :source_key_3, :published_apply_to_all, :unpublished_apply_to_all, :sheet_name, :namespace_identifier, :published_apply_matching_products, :unpublished_apply_matching_products,
    :export_email, :every_minutes, :csv_class, :google_out_insert, :uudecode, :vendor_filter, :cache_data,
    :source_key1_rules, :source_key2_rules, :source_key3_rules, :hide_unmatch_products, :price_delimiter, :find_val, :replace_val, :zero_qty,
    :ignore_dont_track_inventory, :ignore_zero_quantity, :col_sep, :row_sep, :quote_char, :ftp_rename, :add_to_init_quantity, :override_tags, :ftp_mode, :unmatched_force_hide, :auto_reset_quantity,
    :weight_unit, :weight_formula, :case_convert, :low_stock_level, :force_override_compare_at_price, :call_params, :header_params, :body_raw, :start_page, :max_page, :http_method, :scheduler_enabled, :auto_reset_qty_when_above_this_level, :update_duplicate_product_key, :process_in_nodejs, :variant_node, :feed_type,
    :password, :exclude_vendors, :ignore_update_when_qty_low_level, :shopify_track_inventory, :use_quote, :product_type_filter, :collection_filter_id, :load_products_cache,
    :custom_login_field, :custom_login_password_field, :special_cookie, :postfix, :published_filter, :shopify_inventory_management, :received_email, :variant_image_link, :assign_variants_to_first_image,
    :exclude_product_types, :include_tags, :exclude_tags, :skip_import_with_zero_qty, :filter_params, :import_tags, :product_key_separator, :checksum_check, :custom_extra_field, :custom_extra_field_value, :import_sort, :auto_visible_check_all_sku, :file_encoding, :custom_file_name, :case_sensitive, :page_limit,
    :published_apply_matching_products, :unpublished_apply_matching_products, :login_url, :job_queue, :location_id, :fargate_task_definition, :auto_pick_first_available_location, :exclude_skus, :include_skus, :exclude_barcodes, :barcode_filter, :exclude_product_titles, :product_title_filter, :match_and_delete, :ftp_whitelist, :url_date_formatting, :match_subject, :auto_remove_default_title,
    :woocommerce_consumer_key, :woocommerce_consumer_secret, :delete_mode, :partial_match, :xlsx_v2_enabled, :auto_file_settings, :auth_type, :skip_total_rows, :webhook_mode, :bypass_blank_row, :taxable_flag, :not_taxable_flag, :filename_in_zip, :active_product_log_id, :include_bpn, :exclude_bpn, :api_sleep_sec_per_page,
    :status_active_flag, :status_archived_flag, :status_draft_flag, :connection_settings, :enable_image_validation, :export_sort, :extra_options, :use_high_duplicate_qty, :use_low_duplicate_qty
  json_editor

  filter :profile_name
  filter :source_url
  filter :email
  filter :received_email, label: "Unique Email"
  filter :job_type, label: "Update Type", as: :check_boxes, collection: Settings.job_type.map { |k, v| [v, k] }
  filter :source_type

  scope :all, default: true
  scope :import do |profiles|
    profiles.where(feed_type: "import")
  end
  scope :update do |profiles|
    profiles.where(feed_type: "update", io_mode: "in")
  end
  scope :export do |profiles|
    profiles.where(feed_type: "update", io_mode: "out")
  end
  scope :remove do |profiles|
    profiles.where(feed_type: "remove")
  end

  before_save do |profile|
    if profile.filter_params_changed?
      profile.feed_filters = UserProfile.process_new_feed_filters(profile.filter_params)
    end
    if profile.vendor_filter_changed? || profile.exclude_vendors_changed? || profile.product_type_filter_changed? || profile.exclude_product_types_changed? || profile.product_title_filter_changed? || profile.exclude_product_titles_changed? || profile.collection_filter_id_changed? || profile.exclude_collections_changed? || profile.include_tags_changed? || profile.exclude_tags_changed? || profile.include_skus_changed? || profile.exclude_skus_changed? || profile.ignore_zero_quantity_changed? || profile.ignore_dont_track_inventory_changed? || profile.published_filter_changed?
      profile.store_filters = UserProfile.process_new_store_filters(profile)
    end
  end

  collection_action :kill_lock do
    begin
      ActiveRecord::Base.connection.execute("SELECT pg_cancel_backend(#{params[:pid]})")
      ActiveRecord::Base.connection.execute("SELECT pg_terminate_backend(#{params[:pid]})")
    rescue => e
      Airbrake.notify(e, message: "kill lock trigger by admin: #{e.message}")
      # Rails.logger.error "kill lock trigger by admin: #{e.message}"
    end
    redirect_to admin_root_url, notice: "Kill PID #{params[:pid]}"
  end

  collection_action :view_user do
    shop_id = params[:user_id]

    user_profile_id = params[:user_profile_id]
    account = Account.find_by(shop_id: shop_id)
    user = User.find(shop_id)
    if !user.uninstalled_at.present? && user.valid_api_check?
      if account_signed_in?
        impersonate_account(account)
        session["impersonate"] = shop_id
        url = if user_profile_id
          "#{Settings.hostname}/user_profiles/#{user_profile_id}"
        else
          "#{Settings.hostname}/"
        end
        redirect_to url, allow_other_host: true

      else
        flash[:error] = "You are not logged in, please login before impersonate."
        redirect_back fallback_location: admin_root_path
      end
    else
      flash[:error] = "Not a valid user to impersonate."
      redirect_back fallback_location: admin_root_path
    end
  end

  collection_action :stop_impersonate_profile, method: :get do
    shop_id = UserProfile.with_deleted.find(params[:id]).user.id
    account = Account.find_by(shop_id: shop_id)
    session["impersonate"] = nil
    cookies.delete :impersonating_session
    stop_impersonating_account
    flash[:notice] = "Successfully stopped impersonating #{account.email}"
    redirect_to admin_store_url(shop_id)
  end

  action_item :add_template, only: :show do
    link_to "Add Template", make_as_feed_template_admin_user_profile_url(resource.id), class: "action-item-button"
  end

  action_item :copy_feed, only: :show do
    link_to "Make a copy", copy_admin_user_profile_url(resource.id), data: {confirm: "Are you sure?"}, class: "action-item-button"
  end

  action_item :undelete, only: :show do
    if resource.deleted_at
      link_to "Undelete", undelete_admin_user_profile_url(resource.id), data: {confirm: "Are you sure?"}, class: "action-item-button"
    end
  end

  action_item :clear_all_cache, only: :show do
    link_to "Clear all cache", clear_all_cache_admin_user_profile_url(resource.id), data: {confirm: "Are you sure?"}, class: "action-item-button"
  end

  action_item :view, only: :show do
    link_to "View (User)", view_user_admin_user_profiles_url(user_id: resource.user_id, user_profile_id: resource.id), data: {confirm: "Are you sure?"}, class: "action-item-button"
  end

  action_item :generate_json, only: :show do
    link_to "Generate JSON", generate_json_admin_user_profile_path(resource), method: :get, class: "action-item-button"
  end

  collection_action :restart_job do
    begin
      system("cap job_without_key delayed_job:restart")
    rescue => e
      Airbrake.notify(e)
    end
    redirect_to admin_root_url, notice: "Restarted Job"
  end

  member_action :search_logs do
    @profile = params[:id]
  end

  member_action :search_mismatched do
    @profile = params[:id]
  end

  member_action :jmespath_tester, title: "JMESPath" do
    @page_libraries = {ace: true, liquid: true, jmespath: true}
    @profile = params[:id]
    profile = UserProfile.with_deleted.find_by(id: params[:id])
    @parent_node = profile.try(:parent_node) || ""
    @variant_node = profile.try(:variant_node) || ""
  end

  member_action :save_nodes, method: :post do
    profile = UserProfile.find_by(id: params[:id])
    if profile.present?
      profile.update!(**params.permit(:parent_node, :variant_node))
    end
    render plain: "Profile updated"
  rescue => e
    render plain: "Profile failed to update: #{e.message}"
  end

  member_action :load_jmespath_feed_data, method: :get do
    profile = UserProfile.with_deleted.find_by(id: params[:id])
    file_dir = Rails.env.production? ? "/home/<USER>/efs_storage/stock_sync/json_data/" : Rails.root.join("public", "system", "json_data/").to_s

    if profile.present?
      if File.exist?("#{file_dir}#{profile.id}/data.json")
        chunk_size = 1024
        File.open("#{file_dir}#{profile.id}/data.json").each(nil, chunk_size) do |chunk|
          response.stream.write chunk
        end
      else
        response.stream.write "{\"error\":\"feed data missing\"}"
      end
    else
      response.stream.write "{\"error\":\"profile not found\"}"
    end
  rescue
  ensure
    response.stream.close
  end

  member_action :search_not_in do
    args = {user_profile_id: params[:id]}
    if params[:sku].present?
      args[:key] = params[:sku]
    end
    limit = params[:size].present? ? params[:size].to_i : 30
    page = params[:page].present? ? params[:page].to_i : 1
    page = if page <= 1
      0
    else
      (page - 1) * limit
    end

    model = if params[:type] == "1"
      "NotInFeed"
    else
      "NotInStore"
    end

    render json: {"success" => true, "type" => params[:type], "data" => model.constantize.where(**args).order(:product_log_id).limit(limit).offset(page).as_json}
  end

  member_action :search_change_logs do
    args = {user_profile_id: params[:user_profile_id]}
    if params[:spid].present?
      args[:spid] = if params[:spid].include?(",")
        params[:spid].split(",").map { |a| a.blank? ? nil : a }
      else
        params[:spid]
      end
    end
    if params.has_key?(:sku)
      args[:sku] = if params[:sku].include?(",")
        params[:sku].split(",").map { |a| a.blank? ? nil : a }
      else
        params[:sku].blank? ? nil : params[:sku]
      end
    end
    pp args

    fields = params[:fields].present? ? params[:fields] : "*"
    limit = params[:size].present? ? params[:size].to_i : 30
    page = params[:page].present? ? params[:page].to_i : 1
    page = if page <= 1
      0
    else
      (page - 1) * limit
    end

    render json: {"success" => true, "type" => params[:type], "data" => ChangeLog.select(fields).where(**args).order(created_at: :desc).limit(limit).offset(page).as_json}
  end

  member_action :search_pinot_logs do
    @profile = UserProfile.find(params[:id])
  end

  member_action :search_pinot_change_logs, method: :get do
    args = {profile: params[:user_profile]}
    args[:spid] = params[:spid] if params[:spid].present?
    args[:svid] = params[:svid] if params[:svid].present?
    args[:sku] = params[:sku].presence
    args[:ignore_null_sku] = params[:ignore_null_sku].presence

    limit = params[:size].present? ? params[:size].to_i : 30
    offset = params[:page].present? ? (params[:page].to_i - 1) * limit : 0
    order = params[:order].present? ? params[:order] : "created_at ASC"
    from_date = params[:from_date].present? ? params[:from_date] : nil
    to_date = params[:to_date].present? ? params[:to_date] : nil
    timeout = params[:timeout].present? ? params[:timeout] : "10000"

    sql_query = PinotBrokerQuery.construct_sql_query(args.merge({limit: limit, offset: offset, order: order, from_date: from_date, to_date: to_date}))
    result = PinotBrokerQuery.query_sql(sql_query, timeout)

    if result[:data].present?
      result[:data].each do |val|
        val["time_ago"] = view_context.time_ago_in_words(val["created_at"]).to_s
      end
    end

    render json: {success: true, data: result[:data], error: result[:error]}
  end

  member_action :load_file_listing do
    @data = if Rails.env.development?
      command = "cd ../stock-sync-job && bundle exec rake job:get_ftp_file_list[#{params[:id]}]"
      begin
        # shell = IO.popen("ssh ubuntu@172.30.0.242 '/bin/bash -l -c \"cd /home/<USER>/apps/stocksync-job/current && bundle exec rake job:load_source\[#{current_admin_user.email},#{params[:id]},#{params[:filter]}\] RAILS_ENV=job\"'")
        shell = IO.popen(command)
        @resp = shell.read.delete("\n")
        shell.close
      rescue => e
        @resp = e.message
      end

      result = @resp.scan(/\{"status.*/).first
      result = result.split("D,").first if result

      if result
        begin
          data = JSON.parse(result, symbolize_names: true)
        rescue JSON::ParserError
        end
      end
      data
    else
      job = JobApi.new(user_profile_id: params[:id])
      job.get_ftp_file_list
      # "ssh <EMAIL> '/bin/bash -l -c \"cd /home/<USER>/apps/stocksync-job/current && bundle exec rake job:get_ftp_file_list\[#{params[:id]}]\] RAILS_ENV=job\"'"
    end
    @data ||= {status: false, message: "unable to load"}
    @data
  end

  member_action :check_process_cache_key do
    data = "Invalid environment, use only in production"
    if Rails.env.production?
      working_dir = "/home/<USER>/efs_storage/stock_sync/source_cache"
      job = JobApi.new(user_profile_id: params[:id])
      cache_key, expiry = job.source_cache_key[:message]
      data = "Cache key: #{cache_key} (valid? #{expiry})<br>Location: #{working_dir}/#{cache_key} (exists? #{Pathname("#{working_dir}/#{cache_key}").exist?})"
    end
    render plain: data, layout: false
  end

  member_action :get_current_location do
    data = UserProfile.with_deleted.find(params[:id]).get_current_profile_location
    render plain: data, layout: false
  end

  member_action :load_source do
    @data = {status: false, message: "Unable to get response"}
    @resp = ""
    if ![params[:force], params[:raw]].include?("true") && (data = Rails.cache.read("active_admin/load_#{params[:id]}")).present?
      @data = data
    else
      # use ssh instead of cd in production
      if Rails.env.development?   # ##change to production if want to test in local. Make sure to switch back to development before deploy.
        command = "cd ../stock-sync-job && bundle exec rake job:load_source[#{current_admin_user.email.split("@").first},#{params[:id]},#{params[:filter]},#{params[:raw]},#{params[:limit]}]"
        begin
          # shell = IO.popen("ssh ubuntu@172.30.0.242 '/bin/bash -l -c \"cd /home/<USER>/apps/stocksync-job/current && bundle exec rake job:load_source\[#{current_admin_user.email},#{params[:id]},#{params[:filter]}\] RAILS_ENV=job\"'")
          shell = IO.popen(command)
          @resp = shell.read.delete("\n")
          shell.close
        rescue => e
          @resp = e.message
        end

        @resp.scan(/\{"status":.*"\}/).each do |r|
          @data = JSON.parse(r, symbolize_names: true)
          break
        rescue JSON::ParserError => e
          next
        end

        begin
          @headers_array = JSON.parse(@data[:headers])
        rescue JSON::ParserError => e
          begin
            # @headers_array = eval(@data[:headers])
          rescue SyntaxError => e
            @headers_array = [@data[:headers]]
          end
        end
      else
        job = JobApi.new(user_profile_id: params[:id], filter_checked: params[:filter], raw_data: params[:raw], limit: params[:limit], full: params[:full])
        @data = job.load_source
        @headers_array = @data[:headers] if @data[:headers]
        @sample_array = @data[:sample_data] if @data[:sample_data]
        # "ssh <EMAIL> '/bin/bash -l -c \"cd /home/<USER>/apps/stocksync-job/current && bundle exec rake job:get_ftp_file_list\[#{params[:id]}]\] RAILS_ENV=job\"'"
      end
      Rails.cache.write("active_admin/load_#{params[:id]}", @data, expires_in: 2.days)
    end

    created_at = Rails.cache.send(:read_entry, "active_admin/load_#{params[:id]}").instance_variable_get(:@created_at)
    @data[:timestamp] = created_at if created_at
    @data
  end

  member_action :s3_links do
    profile = UserProfile.with_deleted.find(params[:id])
    file_path = params[:file_path]
    file_name = params[:file_name]
    final_s3_path = "#{file_path}#{file_name}"
    s3 = AwsServices.s3_resource_client
    file = s3.bucket(Settings.s3.bucket_name).object(final_s3_path)
    if file.exists?
      url = file.presigned_url(:get, expires_in: 7200, response_content_disposition: "inline; filename=\"#{file_name}\"") # 2 hours expiry
      redirect_to url, allow_other_host: true
    else
      flash[:error] = "S3 Link not exist. Please try again."
      redirect_to admin_user_profile_url(profile)
    end
  end

  member_action :clear_test_connection_attempt do
    profile = UserProfile.with_deleted.find(params[:id])
    if Rails.cache.read("attempt_test_conn_#{profile.id}").present?
      Rails.cache.delete("attempt_test_conn_#{profile.id}")
      flash[:notice] = "Test connection attempt cleared"
    else
      flash[:error] = "No cache found"
    end
    redirect_back fallback_location: admin_root_path
  end

  member_action :predict_field_mapping do
    job = JobApi.new(user_profile_id: params[:id])
    @data = job.predict_field_mapping

    @data
  end

  member_action :store_field_mapping do
    job = JobApi.new(user_profile_id: params[:id])
    @data = job.store_field_mapping_suggestion
  end

  member_action :metafield_mapping do
    job = JobApi.new(user_profile_id: params[:id], owner_type: params[:type])
    @data = job.metafield_mapping_suggestion
  end

  member_action :upload_file, method: :post do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.update(source_file: params[:source_file], checksum: nil)
    # feed_process_job = ProcessFeedJob.new(profile.id, trigger_by: 'support')
    # job = Delayed::Job.enqueue(feed_process_job, queue: 'main_jobs')
    # if job.id
    #   profile.queuing
    # end
    redirect_to admin_user_profile_url(profile), notice: "Upload completed processing running"
  end

  member_action :run_on_local do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.update(checksum: nil, checksum_file_size: nil)

    feed_process_job = ProcessFeedJob.new(profile.id, trigger_by: "support", run_on_local: true, process_limit: params[:process_limit])
    _job = Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue)
    profile.queuing
    profile.mark_mapping
    redirect_to admin_user_profile_url(profile)
  end

  member_action :run_on_fargate do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.update(checksum: nil, checksum_file_size: nil)
    feed_process_job = ProcessFeedJob.new(profile.id, trigger_by: "support", run_on_local: false, process_limit: params[:process_limit])
    _job = Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue, priority: 0)
    profile.queuing
    profile.mark_mapping
    redirect_to admin_user_profile_url(profile)
  end

  member_action :cancel_job do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.update(checksum: nil, checksum_file_size: nil)

    admin = current_admin_user.present? ? current_admin_user.email : "admin"
    begin
      if profile.cancel_process(admin)
        redirect_to admin_user_profile_url(profile), notice: "Job cancelled successfully"
      else
        redirect_to admin_user_profile_url(profile), notice: "Job fail to cancel. Please try again"
      end
    rescue Aws::ECS::Errors::InvalidParameterException
      Delayed::Job.where(user_profile_id: profile.id).delete_all
      profile.update(fargate_task_id: nil)
      profile.start
      redirect_to admin_user_profile_url(profile), notice: "Fargate task not found"
    end
  end

  member_action :search_sku, method: :get do
    @product = nil
    if params[:search_sku].present?
      profile = UserProfile.with_deleted.find(params[:id])
      @product = profile.user.active_session.get_product_by_sku(params[:search_sku])
      render :search_sku
    end
  end

  member_action :search_bigcommerce, method: :get do
    @product = nil

    if params[:search].present?
      @profile = UserProfile.with_deleted.find(params[:id])
      client = @profile.user.active_session
      @product, @is_variant = client.get_product_by_sku(params[:search], params[:type])
      @locations = client.get_locations_inventory(params[:search])
    end
  end

  member_action :search_wix, method: :get do
    @product = nil
    if params[:search].present?
      profile = UserProfile.with_deleted.find(params[:id])
      client = profile.user.active_session
      @product = if params[:type] == "sku"
        client.get_product_by_sku(params[:search])
      else
        client.get_product_by_title(params[:search])
      end
      render :search_sku
    end
  end

  member_action :search_square, method: :get do
    @product = nil
    if params[:search].present?
      profile = UserProfile.with_deleted.find(params[:id])
      @product = profile.user.active_session.get_product_by_sku(params[:search])
    end
  end

  member_action :search_squarespace, method: :get do
    @product = nil
    if params[:search].present?
      profile = UserProfile.with_deleted.find(params[:id])
      @product = profile.user.active_session.get_product_by_sku(params[:search])
      render :search_sku
    end
  end

  member_action :search_ekm, method: :get do
    @product = nil
    if params[:search].present?
      profile = UserProfile.with_deleted.find(params[:id])
      @product = profile.user.active_session.get_product_by_sku(params[:search])
      render :search_sku
    end
  end

  member_action :search_shopify_sku, method: :get do
    @product = nil
    @spid = ""
    profile = UserProfile.with_deleted.find(params[:id])
    @search = params[:search].to_s.strip
    if @search.length > 0
      begin
        user = profile.user
        @levels = {}
        (@data = ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["products"], :query_products, filter: (((params[:type] || "sku") == "sku") ? "(sku:'#{@search}')" : @search.to_s), media: true)).each_with_index do |product, pindex|
          if product.dig("variants", "pageInfo", "hasNextPage") && product.dig("variants", "pageInfo", "endCursor").present?
            product["variants"]["nodes"] += ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["productVariants"], :query_product_variants, product_id: product["id"].split("/").last, after: product.dig("variants", "pageInfo", "endCursor"))
          end
          if product.dig("media", "pageInfo", "hasNextPage") && product.dig("media", "pageInfo", "endCursor").present?
            product["media"]["nodes"] += ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["product", "media"], :query_media, product_id: product["id"], after: product.dig("media", "pageInfo", "endCursor"))
          end

          product["variants"]["nodes"].each do |variant|
            @levels[variant["id"]] = ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["productVariant", "inventoryItem", "inventoryLevels"], :query_inventory_levels, variant_id: variant["id"])
          end
        end

        @profile = profile
        @profile_id = profile.id

        @type = params[:type]
        @log = profile.latest_product_log
        @outofstock_raw = ""
        @updatelog_raw = ""
        @notinstock_raw = ""
        @notinfeed_raw = ""

        if @log && @type == "sku"

          if @log.number_product_updated > 0
            begin
              @updatelog_raw = ProductUpdateStatus.prettify(profile.find_logs_file(@log, @search, ProductUpdateStatus::FILE_UPDATED_INFO, "SKU"), :FILE_UPDATED_INFO)
            rescue Aws::S3::Errors::MissingHeaders
              @updatelog_raw = "File not able to process"
            end

          end
          if @log.number_out_of_stocks > 0
            begin
              @outofstock_raw = ProductUpdateStatus.prettify(profile.find_logs_file(@log, @search, ProductUpdateStatus::FILE_OUTOFSTOCK), :FILE_OUTOFSTOCK)
            rescue Aws::S3::Errors::MissingHeaders
              @outofstock_raw = "File not able to process"
            end

          end
          if @log.number_product_update_failed > 0
            begin
              @notinstock_raw = ProductUpdateStatus.prettify(profile.find_logs_file(@log, @search, ProductUpdateStatus::FILE_FAILED_PRODUCT), :FILE_FAILED_PRODUCT)
            rescue Aws::S3::Errors::MissingHeaders
              @notinstock_raw = "File not able to process"
            end

          end
          if @log.total_left_over_ids > 0
            not_in_feed = NotInFeed.select("key,shopify_variant_id,title,'-'").where(user_profile_id: profile.id, product_log_id: @log.id, key: @search).first
            if not_in_feed
              @notinfeed_raw = ProductUpdateStatus.prettify(not_in_feed.attributes.values.drop(1).join(","), :FILE_UNMATCHED)
            end
          end
        end
      rescue => e
        @error = e
      end
    else
      redirect_to admin_user_profile_url(profile), notice: "SKU is empty"
    end
  end

  member_action :update_password, method: :post do
    profile = UserProfile.with_deleted.find(params[:id])
    if params[:password].length > 0
      pm = PasswordManager.encrypt(params[:password].strip)

      profile.acc_password = pm[:password]
      profile.password_salt = pm[:salt]
      profile.save

      if profile.save
        redirect_to admin_user_profile_url(profile), notice: "New password updated"
      else
        redirect_to admin_user_profile_url(profile), error: profile.errors.first
      end
    else
      redirect_to admin_user_profile_url(profile), error: "no new password"
    end
  end

  member_action :copy_to, method: :post do
    profile = UserProfile.with_deleted.find(params[:id])
    user_id = params[:user_id]
    if user_id.length > 0
      begin
        new_profile = profile.duplicate
        new_profile.user_id = user_id
        new_profile.save

        if new_profile.save
          redirect_to admin_user_profile_url(new_profile), notice: "New feed copied"
        else
          redirect_to admin_user_profile_url(profile), notice: "Not able to copy to #{user_id}"
        end
      rescue => e
        redirect_to admin_user_profile_url(profile), error: e.message
      end
    else
      redirect_to admin_user_profile_url(profile), error: "No shop id or not found"
    end
  end

  member_action :load_single_object do
    profile = UserProfile.with_deleted.find(params[:id])
    @object = nil

    case profile.provider
    when "shopify"
      user = profile.user
      @object = begin
        if params[:type] == "product"
          ShopifyAPI::LightGraphQL.get_product(user.shopify_domain, user.shopify_token, {id: params[:object_id]})
        else
          ShopifyAPI::LightGraphQL.get_product_variant(user.shopify_domain, user.shopify_token, {id: params[:object_id]})
        end
      rescue => e
        e
      end
    else
      @object = "Unable to support woocommerce or bigcommerce yet"
    end
  end

  member_action :show_log do
    @log = begin
      UserProfile.with_deleted.find(params[:id]).product_logs.find(params[:log_id])
    rescue
      nil
    end
  end

  member_action :local_webhook_file do
    path = "/home/<USER>/efs_storage/stock_sync/bulk_data/#{params[:id]}/#{params[:file]}"
    unless File.file?(path)
      render plain: "File not found for \n\n#{path}" and return
    end

    if params[:open_in_tab]
      @json_data = []
      File.open(path, "r") do |file|
        file.each_line.with_index do |line, index|
          @json_data << {index: index + 1, data: JSON.parse(line)}
        end
      end

      render :local_webhook_file
    else
      send_file(path, type: "text/jsonl", disposition: "attachment", filename: params[:file])
    end
  end

  member_action :list_webhook_files do
    @profile_id = params[:id]
    @file_list = Dir.entries("/home/<USER>/efs_storage/stock_sync/bulk_data/#{@profile_id}")
    @file_list.reject! { |x| [".", ".."].include?(x) }
  rescue
    @file_list = []
  end

  member_action :mark_correct_mapping do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.mark_mapping
    redirect_to admin_user_profile_url(profile), notice: "Mapping marked"
  end

  member_action :toggle_hash_no_attr_xml, method: :post do
    profile = UserProfile.find(params[:id])

    if (ox_options = (profile.extra_options || {})["ox_xml_parse_flags"] || {})["mode"].present?
      ox_options.delete("mode")
      profile.extra_options.delete("ox_xml_parse_flags") if ox_options.blank?
    else
      profile.extra_options ||= {}
      ox_options = (profile.extra_options["ox_xml_parse_flags"] ||= {})
      ox_options["mode"] = "hash_no_attr"
    end

    profile.save
    redirect_to admin_user_profile_url(profile), notice: "Updated"
  end

  member_action :current_webhook_file do
    profile = UserProfile.with_deleted.find(params[:id])
    credentials = User.where(id: profile.user_id).pluck(:shopify_domain, :shopify_token).first

    result = ShopifyAPI::LightGraphQL.poll_bulk(credentials.first, credentials.last, params[:external_id])
    url = result.dig("data", "node", "url")

    if url.present?
      redirect_to url, allow_other_host: true
    else
      render plain: "No URL found \n\n#{result.as_json}"
    end
  end

  member_action :load_store do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user

    case user.provider
    when "shopify"
      (@store_products = ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["products"], :query_products, filter: ShopifyAPI::LightGraphQL.graphql_product_filters(profile).first, media: true)).each_with_index do |product, pindex|
        if product.dig("variants", "pageInfo", "hasNextPage") && product.dig("variants", "pageInfo", "endCursor").present?
          product["variants"]["nodes"] += ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["productVariants"], :query_product_variants, product_id: product["id"].split("/").last, after: product.dig("variants", "pageInfo", "endCursor"))
        end
        if product.dig("media", "pageInfo", "hasNextPage") && product.dig("media", "pageInfo", "endCursor").present?
          product["media"]["nodes"] += ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["product", "media"], :query_media, product_id: product["id"], after: product.dig("media", "pageInfo", "endCursor"))
        end
      end
    when "woocommerce"
      @store_products = user.active_session.get_products(params[:limit], profile)
      render :load_woocommerce_store
    when "bigcommerce"
      @store_products = user.active_session.get_products(params[:limit], profile)
      render :load_bigcommerce_store
    when "wix"
      @store_products = user.active_session.get_products(params[:limit], profile)
      render :load_wix_store
    when "ekm"
      @store_products = user.active_session.get_products(params[:limit], profile)
      render :load_ekm_store
    when "squarespace"
      @store_products = user.active_session.get_products(params[:limit], profile)
      render :load_squarespace_store
      # when "quickbooks"
      #   @store_products = QuickbooksClient.new(profile.user).get_products(params[:limit], profile)
      #   render :load_quickbooks_store
    end
    @params = params
  end

  member_action :load_brands do
    profile = UserProfile.with_deleted.find(params[:id])
    @brands = profile.user.active_session.get_all_brands
    nil
  end

  member_action :load_categories do
    profile = UserProfile.with_deleted.find(params[:id])
    @data = profile.user.active_session.get_all_categories.map { |cat| {key: cat["id"], value: cat["name"]} }
    render :load_woocommerce_resource
  end

  member_action :load_tags do
    profile = UserProfile.with_deleted.find(params[:id])
    @data = profile.user.active_session.get_all_tags.map { |cat| {key: cat["id"], value: cat["name"]} }
    render :load_woocommerce_resource
  end

  member_action :check_variant_count do
    profile = UserProfile.with_deleted.find(params[:id])
    count = profile.user.active_session.get_variants_count
    render plain: count, layout: false
  end

  member_action :load_vendors do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user

    @vendors = []
    @vendors = ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["productVendors"], :query_vendors) if user.provider === "shopify"
  end

  member_action :load_collection do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user

    @collections = ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["collections"], :query_collections, filter: "collection_type:#{params[:smart].present? ? "smart" : "custom"}")
    @params = params
  end

  member_action :load_metafield_definition do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user
    @metafield_definitions = if user.provider == "shopify"
      ::ShopifyAPI::LightGraphQL.metafield_definitions(user.shopify_domain, user.shopify_token, params)
    else
      {error: "Store is not shopify (#{user.id} - #{user.provider})"}
    end
    @params = params
  end

  member_action :pre_load_store do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user

    case user.provider
    when "shopify"
      count = ShopifyAPI::LightGraphQL.get_products_count(user.shopify_domain, user.shopify_token, query: ShopifyAPI::LightGraphQL.graphql_product_filters(profile).first).dig("data", "productsCount", "count")
      count = ">=10000" if count.to_i == 10_000
    else
      count = user.active_session.get_products_count
    end
    render plain: count, layout: false
  end

  member_action :load_last_log do
    @store_products = []
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user
    last_log = if params[:log_id]
      profile.product_logs.find(params[:log_id])
    else
      profile.product_logs.order("created_at desc").first
    end
    if last_log
      @store_products = profile.user.retrieve_products_from_last_log(profile, last_log)
    end
    if user.provider == "bigcommerce"
      render :load_bigcommerce_store
    elsif user.provider == "woocommerce"
      render :load_woocommerce_store
    elsif user.provider == "wix"
      render :load_wix_store
    else
      render :load_store
    end
  end

  member_action :load_metafield do
    profile = UserProfile.with_deleted.find(params[:id])
    @profile = profile
    user = profile.user
    @metafields = {}
    @metafields[:product] = (ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["product", "metafields"], :query_metafields, object_id: "gid://shopify/Product/#{params[:product_id]}", object: "product") || [])
    @metafields[:variant] = (ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["productVariant", "metafields"], :query_metafields, object_id: "gid://shopify/ProductVariant/#{params[:variant_id]}", object: "productVariant") || [])
    # @levels = ShopifyAPI::LightGraphQL.paginate(user.shopify_domain, user.shopify_token, ["productVariant", "inventoryItem", "inventoryLevels"], :query_inventory_levels, variant_id: "gid://shopify/ProductVariant/#{params[:variant_id]}", inventory_fields: true)

    @sync_fields = profile.sync_fields
    data = @sync_fields.select { |field| field["field_name"].include?("metafield_") }
    @product_metafields = data.select { |metafield| metafield["extra_attributes"]["metafield_owner"] == "product" }
    @variant_metafields = data.select { |metafield| metafield["extra_attributes"]["metafield_owner"] == "variant" }
  end

  member_action :make_as_feed_template do
    profile = UserProfile.with_deleted.find(params[:id])

    if profile.fast_track?
      flash[:error] = "This is a Snappy profile."
    else
      template = profile.make_as_template
      flash[:notice] = "Successfully copied as template ##{template.id}"
    end
    redirect_to admin_user_profiles_url
  end

  member_action :copy do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.duplicate
    flash[:notice] = "Successfully copied #{profile.id}"
    redirect_to admin_user_profile_url(profile.id)
  end

  member_action :undelete do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.restore
    flash[:notice] = "Successfully undelete #{profile.id}"
    redirect_to admin_user_profile_url(profile.id)
  end

  member_action :copy_feed_to_user, method: :post do
    profile = UserProfile.with_deleted.find(params[:id])
    user = User.find(params[:user_id])
    new_profile = profile.duplicate_user_profile(user: user, profile_name: profile.profile_name)

    respond_to do |format|
      format.json { render json: {id: new_profile.id, message: "Successfully copied to #{user.shopify_domain}", success: true} }
    end
  end

  member_action :load_product do
    profile = UserProfile.with_deleted.find(params[:id])
    @user = profile.user
    begin
      @data = case @user.provider
      when "shopify"
        ShopifyAPI::LightGraphQL.get_product(@user.shopify_domain, @user.shopify_token, {id: params[:pid]})
      else
        @user.active_session.get_product(params[:pid])
      end
    rescue => e
      @data = e.message
    end
  end

  member_action :load_variant do
    profile = UserProfile.with_deleted.find(params[:id])
    begin
      case profile.provider
      when "woocommerce"
        @data = profile.user.active_session.get_variant(params[:pid], params[:vid])
      end
    rescue => e
      @data = e.message
    end
  end

  member_action :schedule do
    @user_profile = UserProfile.with_deleted.find(params[:id])
    @product_logs = @user_profile.product_logs.order("created_at desc").limit(30)
    @events = Schedulable::Event.where(schedulable_id: params[:id]).order("created_at desc").limit(30)
    @delayed_jobs = Delayed::Job.where(user_profile_id: @user_profile.id)

    render "admin/user_profiles/schedule"
  end

  member_action :schedule_new_job do
    @user_profile = UserProfile.with_deleted.find(params[:id])
    schedulable = @user_profile.as_schedule
    Schedulable::CreateEvent.new(schedule: schedulable, trigger_by: "schedule", queue: @user_profile.job_queue).call

    flash[:notice] = "Created delayed job."
    redirect_to schedule_admin_user_profile_url(@user_profile)
  end

  member_action :clear_all_cache do
    profile_id = params[:id]
    profile = UserProfile.with_deleted.find(profile_id)

    # Remove sample data cache
    RedisManager.remove_sample_data(profile_id)
    RedisManager.remove_field_mapping_prediction(profile_id)
    Rails.cache.delete("profile#{profile.id}-#{profile.updated_at.to_i}-#{profile.sync_fields.maximum(:updated_at).to_i}", namespace: "arbre")

    # Clear source cache
    begin
      working_dir = Rails.env.production? ? "/home/<USER>/efs_storage/stock_sync/source_cache" : Rails.root.join("public", "system", "source_cache").to_s
      job = JobApi.new(user_profile_id: profile_id)
      cache_key, _expiry = job.source_cache_key[:message]
      if Pathname("#{working_dir}/#{cache_key}").exist?
        fork { exec("echo '' | sudo -S rm #{working_dir}/#{cache_key}") }
        # File.delete("#{working_dir}/#{cache_key}")
      end
    rescue
    end

    # Remove active admin load cache
    Rails.cache.delete("active_admin/load_#{profile_id}")
    Rails.cache.delete("active_admin/load_source_v2_#{profile_id}")

    # Remove FTP file list cache
    Rails.cache.delete("ftp_filelist_#{profile_id}_#{profile.source_url}}")

    # Clear sample data
    Rails.cache.delete("feed_sample_#{profile_id}_check")

    # Clear collections cache
    Rails.cache.delete("collection_#{profile.user.id}")

    # Clear vendors cache
    Rails.cache.delete("vendors_#{profile.user.id}")

    profile.invalidate_received_email_cache

    redirect_back fallback_location: admin_root_path
  end

  member_action :download_filezilla, method: :get do
    profile = UserProfile.with_deleted.find(params[:id])
    filezilla = FilezillaConnectionFile.new(profile)
    send_data filezilla.call, filename: "filezilla.xml", disposition: "attachment"
  end

  member_action :check_woocommerce_validity do
    profile = UserProfile.with_deleted.find(params[:id])

    client = Faraday.new(
      url: profile.source_url,
      headers: {"Content-Type" => "application/json"}
    ) do |conn|
      conn.request(:authorization, :basic, profile.woocommerce_consumer_key, profile.woocommerce_consumer_secret)
      conn.ssl.verify = false
    end

    response = client.get("wp-json/wc/v3/products", {page: 1, per_page: 1})
    message = response.headers["x-wp-total"].present? ? "Authorized! Total Products: #{response.headers["x-wp-total"]}" : "UNAUTHORIZED"
    render plain: message, layout: false
  end

  member_action :check_encoding do
    @encoding = ""
    profile = UserProfile.with_deleted.find(params[:id])

    if profile.source_file
      file_path = profile.source_file.path.split("/source_files").last
      file_path = "/home/<USER>/apps/stock-sync/shared/public/system/user_profiles/source_files" + file_path

      source_file_in_current_server = profile.upload_origin == ENV["ORIGIN_SERVER"]
      host = "private-web1.stock-sync.com"
      host = "private-web2.stock-sync.com" if ENV["ORIGIN_SERVER"] == "web2.stock-sync.com"
      host = "private-web3.stock-sync.com" if ENV["ORIGIN_SERVER"] == "web3.stock-sync.com"

      if source_file_in_current_server
        @encoding = `uchardet #{file_path}`
      else
        Net::SSH.start(host, "ubuntu") do |ssh|
          @encoding = ssh.exec!("uchardet #{file_path}")
        end
      end
      @encoding&.strip!
    end

    render plain: @encoding
  end

  member_action :switch_webhook do
    profile = UserProfile.with_deleted.find(params[:id])
    profile.webhook_mode = !profile.webhook_mode
    profile.save
    redirect_back fallback_location: admin_root_path
  end

  member_action :run_bulk_op do
    text = ""
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user
    # user.upsert_webhooks(Settings.shopify_eventbridge.bulk_operations.to_h)

    if (profile.start? || profile.pause?) && profile.location_id.present?
      if (status = (response = ShopifyAPI::LightGraphQL.bulk_query(user.shopify_domain, user.shopify_token, profile)).dig("data", "bulkOperationRunQuery", "bulkOperation") || {})["status"] == "CREATED"
        profile.fargate_task_id = "bulk::#{status["id"]}"
        profile.save
        profile.queuing
        profile.mark_mapping
        flash[:notice] = "#{text} Successfully started bulk op"
      else
        flash[:notice] = "#{text} Failed to start bulk op"
      end
      profile.webhook_logs.create(external_id: status["id"], metadata: "[#{params[:platform] ? "\"#{params[:platform]}\"," : ""}#{params[:process_limit] ? "\"process_limit_#{params[:process_limit]}_\"," : ""}#{response.to_json}]", trigger_by: "admin")
    else
      flash[:notice] = "#{text} Aborted, profile is queued/running or location is blank"
    end
    redirect_back fallback_location: admin_root_path
  end

  member_action :predict_price_delim do
    # job = JobApi.new(user_profile_id: params[:id], price_field: params[:price], )
    # @data = job.predict_price
    client = AwsServices.sagemaker_client
    res = client.invoke_endpoint({endpoint_name: "pricedelim-predict", body: {input: params[:price]}.to_json, content_type: "application/json", accept: "application/json"})
    @data = begin
      JSON.parse(res.body.string)
    rescue => e
      {status: false, data: e.message}
    end
  end

  member_action :predict_date_format do
    client = AwsServices.sagemaker_client

    # Prepare the payload as JSON
    payload = {
      "inputs" => params[:date]
    }.to_json

    res = client.invoke_endpoint({
      endpoint_name: "dateformat-endpoint-test",
      body: payload,
      content_type: "application/json",
      accept: "application/json"
    })

    puts res.inspect

    @data = begin
      JSON.parse(res.body.read)
    rescue => e
      {status: false, error: e.message}
    end

    # Render the result (optional)
    render json: @data
  end

  member_action :predict_col_separator do
    # job = JobApi.new(user_profile_id: params[:id], price_field: params[:price], )
    # @data = job.predict_price
    client = AwsServices.sagemaker_client
    res = client.invoke_endpoint({endpoint_name: "colseparator-predict", body: {input: params[:line].sub(/\R/, "")}.to_json, content_type: "application/json", accept: "application/json"})
    @data = begin
      JSON.parse(res.body.string)
    rescue => e
      {status: false, data: e.message}
    end
  end

  member_action :predict_processing_time do
    profile = UserProfile.with_deleted.find(params[:id])
    client = AwsServices.sagemaker_client
    res = client.invoke_endpoint(
      endpoint_name: "feedprocessing-predict",
      body: {
        source: profile.source_type,
        provider: profile.provider,
        feed: profile.feed_type,
        actual_product_updated: profile.latest_product_log&.actual_product_updated || 0,
        total_feed_filtered_rows: profile.latest_product_log&.total_feed_filtered_rows || 0,
        total_store_skus: profile.latest_product_log&.total_store_skus || 0
      }.to_json,
      content_type: "application/json",
      accept: "application/json"
    )
    prediction = JSON.parse(res.body.string)
    if prediction["prediction"].present?
      seconds = prediction["prediction"]
      minutes = (seconds / 60).round(2)
      finish_time = Time.current + seconds
      data = "Estimated time: #{minutes} minutes (#{seconds.round(2)} seconds)\n" \
      "Expected finish: #{finish_time.strftime("%H:%M:%S")}"
    else
      data = "Error: Unable to get prediction from model"
    end
    render plain: data, layout: false
  end

  member_action :get_woocommerce_domain, method: :get do
    user = UserProfile.with_deleted.find(params[:id]).user
    url = "#{user.woocommerce_protocol}://#{user.woocommerce_domain}"
    if user.woocommerce_domain.blank? || user.woocommerce_protocol.blank?
      render plain: "Please fill in the woocommerce domain and protocol", layout: false
      return
    end

    conn = Faraday.new(url) { |b|
      b.use FaradayMiddleware::FollowRedirects
      b.adapter :net_http
    }

    begin
      response = conn.get("/")
      result = response.env.url.to_s
      render plain: result, layout: false
    rescue => e
      render plain: e.message, layout: false
    end
  end

  member_action :load_fargate_events do
    profile = UserProfile.with_deleted.find(params[:id])
    @events = Schedulable::Event.where(schedulable_id: params[:id]).latest_first.limit(50)
    @timezone = profile.timezone
  end

  member_action :query_bulk_op do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user
    bulk_query_id = (profile.fargate_task_id || "").split("::").last
    @data = ShopifyAPI::LightGraphQL.poll_bulk(user.shopify_domain, user.shopify_token, bulk_query_id)
  end

  member_action :search_metafield_definition do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user
    @data = (ShopifyAPI::LightGraphQL.find_metafield_definition(user.shopify_domain, user.shopify_token, owner_type: params[:metafield_owner], key: params[:metafield_key], namespace: params[:metafield_namespace]).dig("data", "metafieldDefinitions", "nodes") || []).last
  end

  member_action :download_sftp_keyfile, method: :get do
    ssh_key = UserProfile.with_deleted.find(params[:id]).ssh_key

    send_data ssh_key, filename: "ssh_key.pem", disposition: "attachment"
  end

  member_action :generate_json, method: :get do
    profile = UserProfile.with_deleted.find(params[:id])

    ## profile data

    profile_hash = profile.as_json
    profile_hash.delete("id")
    profile_hash.delete("user_id")
    profile_hash.delete("email")
    profile_hash.delete("latest_product_log_id")
    profile_hash.delete("latest_product_log_id_24h_ago")
    profile_hash.delete("fargate_task_id")
    profile_hash.delete("fargate_task_definition")
    profile_hash.delete("scheduler_enabled")
    profile_hash["status"] = "start"
    if profile.decrypted_password.present?
      profile_hash["decrypted_password"] = profile.decrypted_password
      profile_hash.delete("acc_password")
      profile_hash.delete("password_salt")
    end

    ## sync fields data

    sync_fields = profile.sync_fields

    sync_fields_hash = sync_fields.map do |sf|
      h = sf.as_json
      h.delete("id")
      h.delete("user_profile_id")
      h
    end

    profile_hash[:sync_fields] = sync_fields_hash

    json_data = profile_hash.to_json

    render json: json_data
  end

  member_action :load_inventory_item do
    profile = UserProfile.with_deleted.find(params[:id])
    user = profile.user

    begin
      inv_id = params[:inv_id]
      inv_id = "gid://shopify/InventoryItem/#{inv_id}" unless inv_id.index("gid://shopify/InventoryItem")
      @data = ShopifyAPI::LightGraphQL.get_inventory_item(inv_id, user.shopify_domain, user.shopify_token)
    rescue => e
      @data = e.message
    end
  end

  member_action :toggle_liquid_mapping, method: :post do
    profile = UserProfile.find(params[:id])
    if profile.use_liquid_mapping?
      if profile.id >= 386500
        profile.extra_options ||= {}
        profile.extra_options["liquid_mapping"] = false
      else
        (profile.extra_options || {}).delete("liquid_mapping")
      end
    elsif profile.id >= 386500
      (profile.extra_options || {}).delete("liquid_mapping")
    else
      profile.extra_options ||= {}
      profile.extra_options["liquid_mapping"] = true
    end
    profile.save(validate: false)
    redirect_to admin_user_profile_url(profile), notice: "Updated"
  end

  member_action :fargate_health_checker, method: :get do
    profile = UserProfile.find(params[:id])
    res = profile.fargate_health_checker
    if res[:error].present?
      flash[:error] = "Error : #{res[:error]}"
    else
      flash[:notice] = "Health : #{res[:health]}, Last Status: #{res[:last]} Desired: #{res[:desired]}, Running since: #{time_ago_in_words(res[:started_at])}"
    end
    redirect_back fallback_location: admin_root_path
  rescue => e
    flash[:error] = "Error rescued : #{e.message}"
    redirect_back fallback_location: admin_root_path
  end

  controller do
    include ActionView::Helpers::DateHelper
    include ActionController::Live

    content_security_policy do |policy|
      policy.worker_src :self, :blob
    end

    def index
      index! { |format|
        format.csv {
          output = CSV.generate(headers: true) do |csv|
            collection.select(collection.column_names - ["password", "password_salt"]).each_with_index do |item, index|
              if index.zero?
                csv << item.attributes.keys
              end
              csv << item.attributes.values
            end
          end
          send_data output, type: "text/csv; header=present", disposition: "attachment; filename=user_profiles.csv"
        }
      }
    end

    def show
      show! { |format|
        format.json {
          render json: resource.as_json(except: [:password, :password_salt])
        }
      }
    end

    def scoped_collection
      end_of_association_chain.with_deleted
    end

    after_save do
      profile = UserProfile.with_deleted.find(params[:id])
      profile.strip_key_space
      # profile.update_scheduler_enabled
      profile.save
    end
  end

  index download_links: [:csv] do
    id_column
    column "Shop Name" do |profile|
      profile.user ? link_to(profile.user.shopify_domain, admin_store_url(profile.user)) : "N/A"
    end
    column :profile_name
    column "Update Type", :job_type do |profile|
      Settings.job_type[profile.job_type]
    end
    column :job_time
    column :job_interval
    column :processing_no
    # column "No. of Update Today" do |profile|
    #   profile.product_log_today.size
    # end
    # column "No. of Failed Update Today" do |profile|
    #   profile.product_log_invalid_today.size
    # end
    column "Failed Reason" do |profile|
      profile.product_log_invalid_today.map { |log| log.remark if log.remark.present? }.uniq.join("<br>")
    end
    column :cache_data
    column :total_memory do |profile|
      number_to_human_size(profile.total_allocated_memsize)
    end
    column :actions do |profile|
      div class: "relative" do
        button id: "dropdownMenuIconButton_#{profile.id}", type: "button", data: {dropdown: {toggle: "dropdownDots_#{profile.id}"}}, class: "inline-flex items-center p-2 text-sm font-medium text-center text-gray-900 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-600" do
          svg class: "w-5 h-5", aria: {hidden: "true"}, xmlns: "http://www.w3.org/2000/svg", fill: "currentColor", viewBox: "0 0 16 3" do
            text_node "<path d=\"M2 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm6.041 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM14 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Z\"/>".html_safe
          end
        end

        div id: "dropdownDots_#{profile.id}", class: "z-10 hidden absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600", style: "min-width:140px;" do
          ul class: "py-2 text-sm text-gray-700 dark:text-gray-200", aria: {labelledby: "dropdownMenuIconButton"} do
            li do
              a href: schedule_admin_user_profile_url(profile.id), class: "block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do
                "See Schedule"
              end
            end
            li do
              a href: admin_user_profile_sync_fields_url(profile.id), class: "block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do
                "Sync Fields"
              end
            end
            unless profile.fast_track?
              li do
                a href: make_as_feed_template_admin_user_profile_url(profile.id), class: "block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do
                  "Make Template"
                end
              end
            end
          end
          div class: "py-2 border-t border-gray-200 dark:border-gray-600" do
            div class: "px-4 py-2" do
              "Copy profile to.."
            end
            div class: "px-3" do
              input type: "text", id: "user_id_#{profile.id}", class: "block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500", placeholder: "Store ID", required: true
            end
            a href: "#", data: {target: profile.id.to_s, method: "copy_feed_to_user", resource: "user_profiles"}, class: "copy_all_profiles_to block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white no-underline" do
              "Copy"
            end
          end
        end
      end
    end
  end

  show title: proc { |profile| "#{profile.profile_name}<br /> <h3 style=\"margin: 0;\">#{profile.provider.titleize}</h3>".html_safe } do |profile|
    cache_arbre(self, "profile#{profile.id}-#{profile.updated_at.to_i}-#{profile.sync_fields.maximum(:updated_at).to_i}", expires_in: 15.minutes) do
      panel "Back to Store #{link_to(profile.user.shopify_domain, admin_store_url(profile.user_id))}".html_safe do
        div class: "grid grid-cols-2 gap-6" do
          div do
            panel "General" do
              attributes_table_for profile do
                row :deleted_at do |profile|
                  if profile.deleted?
                    profile.deleted_at
                  else
                    false
                  end
                end
                row :disabled_feed do |profile|
                  update_profiles = profile.user.user_profiles.where(fast_track: false, feed_type: "update").limit(profile.user.profile_limit).order("created_at asc").pluck(:id)
                  import_profiles = profile.user.user_profiles.where(fast_track: false, feed_type: "import").limit(profile.user.import_profile_limit).order("created_at asc").pluck(:id)
                  remove_profiles = profile.user.user_profiles.where(fast_track: false, feed_type: "remove").limit(profile.user.remove_profile_limit).order("created_at asc").pluck(:id)
                  profiles = update_profiles + import_profiles + remove_profiles
                  !profiles.include?(profile.id)
                end
                if profile.feed_type == "remove"
                  row :delete_mode do |profile|
                    ((profile.delete_mode == 1) ? "Set to archive" : "Set to delete").html_safe
                  end

                  row :partial_match do |profile|
                    (profile.partial_match && profile.delete_mode == 1) ? "Set to archive on partial match" : "Disabled"
                  end
                end

                row :total_sku
                row :profile_name do |profile|
                  body = "<strong>#{profile.humanize_feed_type.titleize}</strong><br />"
                  body << if profile.processing? || profile.queuing?
                    link_to("Predict Feed Processing Time", predict_processing_time_admin_user_profile_path(profile), target: "_blank") + " | " +
                      link_to("Cancel Job", cancel_job_admin_user_profile_url(profile), data: {confirm: "Are you very very sure?"})
                  else
                    "<i>#{profile.run_webhook_mode? ? "ALERT USING BULK WEBHOOK" : "Running using REST API"}</i></br>
                    #{if profile.run_webhook_mode?
                        "#{link_to("Webhook Local", run_bulk_op_admin_user_profile_url(platform: "local"), data: {confirm: "Are the mapping correct?"})}
                    - #{link_to("Webhook Fargate", run_bulk_op_admin_user_profile_url, data: {confirm: "Are the mapping correct?"})}</br>
                    #{link_to("Webhook Preview (Local)", run_bulk_op_admin_user_profile_url(platform: "preview\",\"local"), data: {confirm: "Are the mapping correct?"})}
                    - #{link_to("Webhook Preview (Fargate)", run_bulk_op_admin_user_profile_url(platform: "preview"), data: {confirm: "Are the mapping correct?"})}</br>
                    #{link_to("Webhook 3 products only (Local)", run_bulk_op_admin_user_profile_url(process_limit: 3, platform: "local"), data: {confirm: "Are the mapping correct?"})}
                    - #{link_to("Webhook 3 products only (Fargate)", run_bulk_op_admin_user_profile_url(process_limit: 3), data: {confirm: "Are the mapping correct?"})}"
                      else
                        "#{link_to("Run on local", run_on_local_admin_user_profile_url(profile), data: {confirm: "Are the mapping correct?"})} -
                    #{link_to("Run on fargate", run_on_fargate_admin_user_profile_url(profile), data: {confirm: "Are the mapping correct?"})}<br />
                    #{link_to "3 products only (Local)", run_on_local_admin_user_profile_url(profile, process_limit: 3), data: {confirm: "Are the mapping correct?"}} -
                    #{link_to "3 products only (Fargate)", run_on_fargate_admin_user_profile_url(profile, process_limit: 3), data: {confirm: "Are the mapping correct?"}}
                    "
                      end}".html_safe
                  end
                  body.html_safe
                end
                row :receiving_email do |profile|
                  out = "<ul>"

                  out << "<li>#{profile.email} | #{link_to("Mailgun", profile.generate_mailgun_url(profile.email), target: "_blank")} | #{link_to("Raw Email Logs", admin_raw_email_logs_path(q: {recipient_cont: profile.email}, order: :created_at_desc), target: "_blank")}</li>"

                  if profile.received_email.present?
                    profile.received_email.split(",").each do |email|
                      out << "<li>#{email.strip} | #{link_to("Mailgun", profile.generate_mailgun_url(email.strip), target: "_blank")} | #{link_to("Raw Email Logs", admin_raw_email_logs_path(q: {recipient_cont: email.strip}, order: :created_at_desc), target: "_blank")}</li>"
                    end
                  end
                  out << "</ul>"

                  out.html_safe
                end

                if profile.source_type == "email" || profile.source_type == "email_link"
                  row :match_subject
                end

                row :fargate_info do |profile|
                  if profile.fargate_task_id.present? && !profile.fargate_task_id.to_s.index("BulkOperation").present? && !profile.fargate_task_id.to_s.index("run local").present?
                    out = "#{profile.fargate_task_definition.present? ? profile.fargate_task_definition : "Default FG"} - #{link_to(profile.fargate_task_id, profile.fargate_task_id_console_url, target: "_blank")}"
                    out += " | #{link_to "Check Fargate Status", fargate_health_checker_admin_user_profile_url(profile)}"
                    out.html_safe
                  elsif profile.fargate_task_id.to_s.index("BulkOperation").present?
                    out = profile.fargate_task_id.to_s
                    out += " | #{link_to("Check Bulk Op", query_bulk_op_admin_user_profile_url(profile), target: "_blank")}"
                    out.html_safe
                  else
                    "#{profile.fargate_task_definition} - #{profile.fargate_task_id}"
                  end
                end

                row :processing_no do |profile|
                  "<strong>#{profile.status}</strong> #{profile.processing_no}  #{profile.progress}% #{profile.start_time ? time_ago_in_words(profile.start_time) : ""}".html_safe
                end

                if profile.processing? || profile.queuing?
                  row :clashed_run do |profile|
                    profile.user.user_profiles.where(status: ["queuing", "processing"]).pluck(:id).count > 1
                  end
                end

                row :location do |profile|
                  profile.get_current_profile_location.html_safe
                end

                if profile.woocommerce_consumer_key.present?
                  row :Woocommerce do |profile|
                    "Key: #{profile.woocommerce_consumer_key}  <br> SECRET #{profile.woocommerce_api_version}: #{profile.woocommerce_consumer_secret}".html_safe if profile.woocommerce_consumer_secret?
                  end
                end

                row :audited_log do
                  link_to "View Audited Logs", admin_user_profile_profile_audited_log_path(resource), target: "_blank"
                end
              end
            end
          end

          div do
            panel "Connection" do
              attributes_table_for profile do
                row :source_type do |profile|
                  (profile.source_type == "online_file") ? "Download Link" : profile.source_type.to_s.titleize
                end
                row :source_type do |profile|
                  out = "(#{(profile.file_format == "Auto") ? "Auto - #{profile.detected_file_format || "No detection"}" : profile.file_format})"
                  out << " XML V2" if ["Xml"].include?(profile.file_format) && profile.use_liquid_mapping?
                  out << " - Parent node: #{profile.parent_node} | Variant node: #{profile.variant_node}" if ["Xml", "Json"].include?(profile.file_format)
                  out << "<br>- #{link_to "load", load_source_admin_user_profile_url(profile), target: "_blank"} | #{link_to "Load 10 rows (force)", load_source_admin_user_profile_url(profile, force: true, limit: 10), target: "_blank"} | #{link_to "load with filter", load_source_admin_user_profile_url(profile, filter: true), target: "_blank"} | #{link_to "load raw", load_source_admin_user_profile_url(profile, raw: "true", filter: false), target: "_blank"}"
                  if Rails.cache.exist?("active_admin/load_#{profile.id}")
                    out << "<br>- #{link_to "Regenerate new", load_source_admin_user_profile_url(profile, force: true), target: "_blank"}"
                  end
                  out << "<br>#{link_to "download_s3_link", s3_links_admin_user_profile_url(profile, file_path: "active_admin/active_admin/", file_name: "load_#{profile.id}.csv")}"
                  out << " | #{link_to "load full", load_source_admin_user_profile_url(profile, full: true), target: "_blank", data: {confirm: "Please don't use this often. This will load all data and probably causing error during load. Are you really, really, really sure???"}}"
                  out.html_safe
                end

                row :source_type_v2 do
                  link_to "Load Source (always CSV)", admin_user_profile_profile_load_source_v2_path(resource), target: "_blank"
                end

                if profile.use_liquid_mapping?
                  row "XML Node Tester" do |profile|
                    link_to("Explore", jmespath_tester_admin_user_profile_url(profile), target: "_blank")
                  end
                  row "Process XML Attributes" do |profile|
                    disabled = (((profile.extra_options || {})["ox_xml_parse_flags"] || {})["mode"] == "hash_no_attr")
                    "#{disabled ? "<span class=\"status-tag\" data-status=\"no\">No</span>" : "<span class=\"status-tag\" data-status=\"yes\">Yes</span>"}
                    #{form_tag toggle_hash_no_attr_xml_admin_user_profile_url(profile), method: :post, style: "display:inline" do |f|
                      submit_tag("#{disabled ? "Enable" : "Disable"} Attributes", class: "filters-form-submit")
                    end}".html_safe
                  end
                end

                if profile.source_type == "uploaded_file"

                  row "File" do |profile|
                    "#{profile.upload_origin} - #{link_to "Download #{profile.source_file_file_name}", profile.feed_file_location}  ".html_safe if profile.source_file
                  end
                  row "Check encoding" do |profile|
                    link_to("Check encoding", check_encoding_admin_user_profile_url(profile), target: "_blank")
                  end
                elsif profile.source_type == "online_file"
                  row :source_url do |profile|
                    body = "<span style='margin-right: 7px'>#{link_to profile.source_url, profile.source_url, id: "profile-source-url", target: "_blank"}</span>"
                    if profile.source_url.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-source-url"})
                    end
                    body.html_safe
                  end
                elsif ["shopify_store", "bigcommerce_store", "wix_store"].include?(profile.source_type)
                  row :source_url do |profile|
                    body = "<span style='margin-right: 7px'>#{link_to profile.source_url, "https://admin.stock-sync.com/admin/stores?utf8=%E2%9C%93&q%5Bpublic_token_cont%5D=#{profile.source_url}", id: "profile-source-url", target: "_blank"}</span>"
                    if profile.source_url.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-source-url"})
                    end
                    body.html_safe
                  end
                elsif profile.source_type == "email" || profile.source_type == "email_link"
                  if profile.export_email
                    row :export_email do |profile|
                      link_to profile.export_email, "https://app.mailgun.com/app/sending/domains/mail.stock-sync.com/logs?skip=0&search=#{profile.export_email}", target: "_blank"
                    end
                  end
                  row :able_to_receive_email do |profile|
                    profile.user.able_to_receive_email?(profile.feed_type)
                  end
                  row :path_to_file

                elsif profile.source_type == "rest_api"
                  row :source_url do |profile|
                    body = "<span style='margin-right: 7px'>#{link_to "#{(profile.max_page > 1) ? "(Page:#{profile.max_page})" : ""} #{profile.source_url}", profile.source_url, id: "profile-source-url", target: "_blank"}</span>"
                    if profile.source_url.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-source-url"})
                    end
                    body.html_safe
                  end
                  row :http_method
                  row :auth_type

                  case profile.auth_type
                  when "bearer_token", "bearer_token_body"
                    row "Bearer Token" do |profile|
                      profile.woocommerce_consumer_key
                    end
                    row "Headers/Body" do |profile|
                      profile.woocommerce_consumer_secret
                    end
                  when "oauth_2", "oauth_2_with_password"
                    row "Token URL" do |profile|
                      profile.unleashed_api_id
                    end
                    row "Client Secret" do |profile|
                      profile.woocommerce_consumer_secret
                    end
                    row "Grant Type" do |profile|
                      profile.aliexpress_app_secret
                    end
                    row "Scope" do |profile|
                      profile.unleashed_api_key
                    end
                  when "oauth_2_for_odoo"
                    row "Token URL" do |profile|
                      profile.unleashed_api_id
                    end
                    row "Database" do |profile|
                      profile.woocommerce_consumer_secret
                    end
                  end
                  row :call_params
                  row :header_params

                else
                  row :source_url do |profile|
                    body = "<span style='margin-right: 7px'>#{link_to profile.source_url, profile.source_url, id: "profile-source-url", target: "_blank"}</span>"
                    if profile.source_url.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-source-url"})
                    end
                    body.html_safe
                  end
                end

                # for WooCommerce to check if link is valid
                if profile.source_type == "woocommerce"
                  row :check_woocommerce_validity do |profile|
                    link_to("Check products count", check_woocommerce_validity_admin_user_profile_url(profile), class: "load-woocommerce")
                  end
                end
                if profile.provider == "woocommerce"
                  row :validate_woocommerce_domain do |profile|
                    link_to("Check live domain", get_woocommerce_domain_admin_user_profile_url(profile), class: "woocom_redirect_link")
                  end
                end

                if Settings.admin_source_settings.keys.map(&:to_s).include?(profile.source_type)
                  option = Settings.admin_source_settings.send(profile.source_type.to_sym)
                  if option.has_oauth
                    row "Auth Success" do |profile|
                      profile.oauth_granted
                    end
                  end
                  option.fields.each do |field|
                    row field.label do |profile|
                      profile.send(field.key)
                    end
                  end
                elsif profile.source_type == "custom_login"
                  row("Login URL") { |profile| profile.login_url }
                  row("Email Field Name") { |profile| profile.custom_login_field }
                  row("Password Field Name") { |profile| profile.custom_login_password_field }
                  row("Email Value") do |profile|
                    body = "<span id='profile-acc-name' style='margin-right: 7px'>#{profile.acc_name || "-"}</span>"
                    if profile.acc_name.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-acc-name"})
                    end
                    body.html_safe
                  end
                  row("Password Value") do |profile|
                    body = "<span id='profile-acc-password' style='margin-right: 7px'>#{profile.decrypted_password || "-"}</span>"
                    if profile.decrypted_password.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-acc-password"})
                    end
                    body.html_safe
                  end
                else
                  row :source_auth
                  row :acc_name do |profile|
                    body = "<span id='profile-acc-name' style='margin-right: 7px'>#{profile.acc_name || "-"}</span>"
                    if profile.acc_name.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-acc-name"})
                    end
                    body.html_safe
                  end
                  row :password do |profile|
                    body = "<span id='profile-acc-password' style='margin-right: 7px'>#{profile.acc_password ? profile.decrypted_password : "-"}</span>"
                    if profile.acc_password.present?
                      body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", class: "copy-button", data: {target: "profile-acc-password"})
                    end
                    body.html_safe
                  end
                  # row :source_file_file_name
                  if ["ftp", "sftp", "ftps", "ftpsimplicit", "ftp_multiple", "ftps_multiple", "sftp_multiple"].include?(profile.source_type)
                    if profile.ssh_key.present?
                      row :ssh_key do
                        link_to("Download Key", download_sftp_keyfile_admin_user_profile_url(profile.id))
                      end
                    end

                    row :path_to_file

                    if profile.path_to_file.index("%")
                      row :parsed_file_path do |profile|
                        FileReader.parse_url(profile.path_to_file.to_s, ActiveSupport::TimeZone.new(profile.timezone).now)
                      end
                    end
                    row :ftp_rename do |profile|
                      "#{Settings.file_rename_options[profile.ftp_rename]}-  #{profile.custom_file_name}"
                    end
                    row :filezilla_connection do |profile|
                      link_to("Download File", download_filezilla_admin_user_profile_url(profile.id))
                    end
                    row :file_listing do |profile|
                      link_to("View", load_file_listing_admin_user_profile_url(profile.id), target: "_blank")
                    end
                    row :clear_test_connection_attempt do |profile|
                      link_to("Clear test connection attempt", clear_test_connection_attempt_admin_user_profile_url(profile.id), target: "_blank")
                    end
                  end
                  row :sheet_name
                end
                row :csv_class
                row :has_header
                row :cache_expiry
                row :connection_settings do |profile|
                  pre JSON.pretty_generate(profile.connection_settings)
                rescue
                  profile.connection_settings
                end
                row :chatgpt do |profile|
                  out = "<span>#{link_to "Store Field Mapping ", store_field_mapping_admin_user_profile_url(profile), target: "_blank"}</span>"
                  if profile.provider == "shopify"
                    out << "| #{link_to "Product Metafield Mapping ", metafield_mapping_admin_user_profile_url(profile, type: "product"), target: "_blank"} | #{link_to "Variant Metafield Mapping ", metafield_mapping_admin_user_profile_url(profile, type: "variant"), target: "_blank"}"
                  end
                  out.html_safe
                end
              end
            end
          end
        end

        div class: "grid grid-cols-2 gap-6" do
          div do
            panel "Search" do
              attributes_table_for profile do
                provider = profile.provider
                case provider
                when "shopify"
                  row "Shopify Store GraphiQL" do |profile|
                    link_to("Explore", shopify_graphiql_admin_store_url(profile.user), target: "_blank")
                  end
                  row "Search from Shopify" do |profile|
                    form_tag search_shopify_sku_admin_user_profile_url(profile), method: :get, target: "_blank" do |f|
                      text_field_tag("search", "", size: 20) << raw("<br>") << select_tag("type", options_for_select([["Search All", "no filter"], ["SKU only", "sku"]], "no filter")) << submit_tag("Go", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                  row "Search Product ID" do |profile|
                    form_tag load_product_admin_user_profile_url(profile), target: "_blank", method: :get do |f|
                      text_field_tag("pid", "", size: 20) << submit_tag("Search", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                  row "Search via Inventory ID" do |profile|
                    form_tag load_inventory_item_admin_user_profile_url(profile), target: "_blank", method: :get do |f|
                      text_field_tag("inv_id", "", size: 20) << submit_tag("Search", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                when "bigcommerce"
                  row "Search from BigCommerce" do |profile|
                    form_tag search_bigcommerce_admin_user_profile_url(profile), method: :get, target: "_blank" do |f|
                      text_field_tag("search", "", size: 20) << raw("<br>") << select_tag("type", options_for_select([["SKU", "sku"], ["Barcode", "upc"], ["MPN", "mpn"], ["Product Title", "name"]], "sku")) << submit_tag("Go", data: {disable_with: false})
                    end
                  end
                when "wix"
                  row "Search from Wix" do |profile|
                    form_tag search_wix_admin_user_profile_url(profile), method: :get, target: "_blank" do |f|
                      text_field_tag("search", "", size: 50) << raw("<br>") << select_tag("type", options_for_select([["SKU", "sku"], ["Title", "name"]], "sku")) << submit_tag("Go", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                  row "Search Product ID" do |profile|
                    form_tag load_product_admin_user_profile_url(profile), target: "_blank", method: :get do |f|
                      text_field_tag("pid", "", size: 20) << submit_tag("Search", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                when "square"
                  row "Search from Square" do |profile|
                    form_tag search_square_admin_user_profile_url(profile), method: :get, target: "_blank" do |f|
                      text_field_tag("search", "", size: 20) << raw("<br>") << select_tag("type", options_for_select([["SKU", "sku"]], "sku")) << submit_tag("Go", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                when "ekm"
                  row "Search from EKM" do |profile|
                    form_tag search_ekm_admin_user_profile_url(profile), method: :get, target: "_blank" do |f|
                      text_field_tag("search", "", size: 20) << raw("<br>") << select_tag("type", options_for_select([["SKU", "sku"]], "sku")) << submit_tag("Go", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                else
                  row "Search SKU from #{provider.capitalize}" do |profile|
                    form_tag search_sku_admin_user_profile_url(profile), method: :get, target: "_blank" do |f|
                      text_field_tag("search_sku", "", size: 20) << submit_tag("Go", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                    end
                  end
                end

                row :search_logs do |profile|
                  link_to("search logs via sku", search_logs_admin_store_url(profile.user), target: "_blank") +
                    " | " +
                    link_to("search new logs via sku", search_pinot_logs_admin_user_profile_path(profile), target: "_blank")
                end

                row :search_mismatched do |profile|
                  link_to("search not in store/feed", search_mismatched_admin_user_profile_url(profile), target: "_blank")
                end
              end
            end
          end

          div do
            panel "Load Store Data" do
              attributes_table_for profile do
                row "Product Listing" do |profile|
                  "#{link_to "load", load_store_admin_user_profile_url(profile), class: "load-store", target: "_blank"}
                    |
                    #{link_to "load first 250 only", load_store_admin_user_profile_url(profile, params: {limit: 240}), target: "_blank"}
                    |
                    #{link_to "load since last logs", load_last_log_admin_user_profile_url(profile), target: "_blank"}
                    ".html_safe
                end

                case profile.provider
                when "shopify"
                  row "Vendor Listing" do |profile|
                    link_to "load", load_vendors_admin_user_profile_url(profile), target: "_blank"
                  end
                  row "Collection Listing" do |profile|
                    "#{link_to "load custom", load_collection_admin_user_profile_url(profile), class: "load-collection", target: "_blank"}
                      |
                      #{link_to "load smart", load_collection_admin_user_profile_url(profile, params: {smart: true}), class: "load-collection", target: "_blank"} ".html_safe
                  end
                  row "Metafield Definition Listing" do |profile|
                    "#{link_to "For products", load_metafield_definition_admin_user_profile_url(profile, params: {type: "PRODUCT"}), class: "load-collection", target: "_blank"}
                      |
                      #{link_to "For variants", load_metafield_definition_admin_user_profile_url(profile, params: {type: "PRODUCTVARIANT"}), class: "load-collection", target: "_blank"} ".html_safe
                  end
                when "bigcommerce"
                  row "Store Brands" do |profile|
                    link_to "load brands", load_brands_admin_user_profile_url(profile), class: "load-collection", target: "_blank"
                  end
                  row "Store Categories" do |profile|
                    link_to "load categories", load_categories_admin_user_profile_url(profile), class: "load-collection", target: "_blank"
                  end
                  row "Variant Count" do |profile|
                    link_to "Check", check_variant_count_admin_user_profile_url(profile), class: "variants-check", target: "_blank"
                  end
                when "woocommerce"
                  row "Store Categories" do |profile|
                    link_to "load categories", load_categories_admin_user_profile_url(profile), class: "load-collection", target: "_blank"
                  end

                  row "Store Tags" do |profile|
                    link_to "load tags", load_tags_admin_user_profile_url(profile), class: "load-collection", target: "_blank"
                  end
                end

                row "Cache Key" do |profile|
                  "#{link_to "Process", "#", class: "check-process-cache-key", data: {target: "check-process-cache-result-#{profile.id}"}, target: "_blank"}
                    <div id=\"check-process-cache-result-#{profile.id}\" style=\"word-break: break-word;\"></div>".html_safe
                end
              end
            end
          end
        end

        div class: "grid grid-cols-2 gap-6" do
          div do
            panel "Schedule/Setup" do
              attributes_table_for profile do
                row :schedule do |profile|
                  next_job = Delayed::Job.where(user_profile_id: profile.id).where(locked_at: nil).order("run_at asc").first
                  result = []
                  result << "<strong>#{profile.scheduler_enabled ? "Enabled" : "Disabled"}</strong>  #{link_to "Schedule Detail", schedule_admin_user_profile_url(profile)}"
                  result << "From #{profile.hourly_start_time} to #{profile.hourly_end_time}" if profile.use_time_range && profile.job_type == "periodically"
                  result << "Type: #{profile.job_type.to_s.humanize} / Time: #{profile.job_time || "N/A"} / Interval: #{profile.job_interval} #{profile.every_minutes ? "Every mins" : ""}"
                  if next_job
                    result << if next_job.run_at > Time.now
                      "Running in #{time_ago_in_words(next_job.run_at)} (#{next_job.run_at.in_time_zone(profile.timezone)})"
                    else
                      "DELAYED #{time_ago_in_words(next_job.run_at)} (#{next_job.run_at.in_time_zone(profile.timezone)})"
                    end
                  end
                  result.join("<br>").html_safe
                end

                row :store_filters do |profile|
                  if profile.provider == "shopify"
                    filters = profile.store_filters
                    filters.map do |filter|
                      key = filter["key"].titleize
                      conditions = filter["conditions"].map do |condition|
                        operator = condition["operator"]
                        value = condition["value"].presence || nil
                        "<b> #{operator} - #{value} </b>"
                      end.join("--")
                      "#{key}: #{conditions}"
                    end.join("<br>").html_safe
                  else
                    UserProfile.shopify_filter(profile)
                  end
                end
                row :feed_filter do |profile|
                  UserProfile.feed_filter(profile)
                end
                if profile.is_import_type?
                  row :skip_import_with_zero_qty
                end
                row :prefix do |profile|
                  result = []

                  result << "Store Prefix: <strong>#{profile.store_prefix}</strong>" if profile.store_prefix
                  result << "Prefix: <strong>#{profile.prefix}</strong>" if profile.prefix
                  result << "Postfix: <strong>#{profile.postfix}</strong>" if profile.postfix
                  result << "Case sensative: <strong>#{profile.case_sensitive}</strong>" if profile.case_sensitive
                  result.join("<br>").html_safe
                end
                row :out_of_stock do |profile|
                  result = []
                  result << "Set out of stock products to Zero &#10003;".html_safe if profile.hide_unmatch_products
                  result << "Set out of stock products to Zero that was updated by syncX: Stock Sync before &#10003;" if profile.auto_reset_quantity
                  result.join("<br>").html_safe
                end
                row :checksum_check
                row :liquid_mapping do |profile|
                  enabled = profile.use_liquid_mapping?
                  "<div>Liquid mapping #{enabled ? "enabled" : "disabled"}<div>
                  #{form_tag toggle_liquid_mapping_admin_user_profile_url(profile), method: :post, style: "display:inline" do |f|
                    submit_tag("#{enabled ? "Disable" : "Enable"} Liquid Mapping", class: "filters-form-submit")
                  end}".html_safe
                end
                row :extra_options do |profile|
                  pre JSON.pretty_generate(profile.extra_options || {})
                rescue
                  profile.extra_options
                end
              end
            end
          end

          div do
            panel "Info/Actions" do
              attributes_table_for profile do
                if profile.is_import_type?
                  row :import_tags
                end
                row "Created/Updated At" do |profile|
                  "#{profile.created_at} / #{profile.updated_at} (#{time_ago_in_words(profile.updated_at)}) "
                end

                row "Queuing/Processing Time" do |profile|
                  "#{profile.queuing_at} / #{profile.start_time}"
                end

                row :upload_file do |profile|
                  form_tag upload_file_admin_user_profile_url(profile), multipart: true do |f|
                    file_field_tag("source_file") << submit_tag("Upload", class: "filters-form-submit")
                  end
                end
                row "Upload File Updated At" do |profile|
                  "#{profile.source_file_updated_at} --  #{time_ago_in_words(profile.source_file_updated_at) if profile.source_file_updated_at} "
                end
                row "Feed password" do |source|
                  form_tag update_password_admin_user_profile_url(source) do |f|
                    text_field_tag("password", "", size: 20) << submit_tag("Update", class: "filters-form-submit", style: "margin-top:8px")
                  end
                end
                row "Copy to Store ID" do |source|
                  form_tag copy_to_admin_user_profile_url(source) do |f|
                    text_field_tag("user_id", "", size: 20) << submit_tag("Update", class: "filters-form-submit", style: "margin-top:8px")
                  end
                end
                row "Test Price Delimiter" do |source|
                  form_tag predict_price_delim_admin_user_profile_url(source), target: "_blank", method: :get do |f|
                    text_field_tag("price", "", size: 20) << submit_tag("Test", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                  end
                end
                row "Test Col Separator" do |source|
                  form_tag predict_col_separator_admin_user_profile_url(source), target: "_blank", method: :get do |f|
                    text_field_tag("line", "", size: 200) << submit_tag("Test", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                  end
                end
                row "Test Date Format for Metafields" do |source|
                  form_tag predict_date_format_admin_user_profile_url(source), target: "_blank", method: :get do |f|
                    text_field_tag("date", "", size: 200) << submit_tag("Test", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                  end
                end
                row "Predict Field Mapping" do |source|
                  form_tag predict_field_mapping_admin_user_profile_url(profile), target: "_blank", method: :get do |f|
                    submit_tag("Predict", data: {disable_with: false}, class: "filters-form-submit", style: "margin-top:8px")
                  end
                end
              end
            end
          end
        end

        panel "Sync Fields #{link_to "Add New", new_admin_user_profile_sync_field_url(user_profile)}".html_safe do
          div class: "grid grid-cols-#{profile.template_id ? "2" : "1"} gap-6" do
            attributes_table_for profile do
              row :is_template do
                profile&.template_id ? true : false
              end
            end
            if profile.template_id
              attributes_table_for profile do
                row :template do
                  link_to "#{profile&.template_id} #{(template = FeedTemplate.find_by_id(profile&.template_id)) ? template&.profile_name : "!!Not found!!"}", "/admin/feed_templates/#{profile&.template_id}"
                end
              end
            end
          end
          table_for user_profile.sync_fields.order("id asc") do |field|
            column :field_name do |d|
              str = ""
              if d.field_name == "product_id"
                link_to "#{I18n.t(d.field_name, default: d.field_name)} : #{str}", edit_admin_user_profile_sync_field_url(user_profile, d)
                if d.user_profile.blank?
                  false
                else
                  str = [d.user_profile.shopify_product_key]
                  str << d.user_profile.variant_key_1 unless d.user_profile.variant_key_1.blank?
                  str << d.user_profile.variant_key_2 unless d.user_profile.variant_key_2.blank?
                  str << d.user_profile.variant_key_3 unless d.user_profile.variant_key_3.blank?
                  str = str.join(" + ")
                end
              end
              if d.field_name == "product_id"
                link_to "#{I18n.t(d.field_name, default: d.field_name)} : #{str}", edit_admin_user_profile_sync_field_url(user_profile, d)
              elsif d.field_name.index("metafield_")
                metafield_key = d.extra_attributes.fetch("metafield_key", nil)
                metafield_namespace = d.extra_attributes.fetch("metafield_namespace", nil)
                metafield_owner = d.extra_attributes.fetch("metafield_owner", nil)
                metafield_owner = "productvariant" if metafield_owner == "variant"
                str = "#{link_to I18n.t(d.field_name, default: d.field_name), edit_admin_user_profile_sync_field_url(user_profile, d)} <br /><br /> #{link_to "Check", search_metafield_definition_admin_user_profile_url(user_profile, metafield_owner: metafield_owner, metafield_key: metafield_key, metafield_namespace: metafield_namespace), target: "_blank"}"
                str.html_safe
              elsif d.field_name === "quantity"
                quantity_use_on_hand = d.extra_attributes.fetch("quantity_use_on_hand", nil)
                if profile.provider === "shopify"
                  if quantity_use_on_hand === true
                    link_to "#{I18n.t("quantity_on_hand", default: "quantity_on_hand")} : #{str}", edit_admin_user_profile_sync_field_url(user_profile, d)
                  else
                    link_to "#{I18n.t("quantity_available", default: "quantity_available")} : #{str}", edit_admin_user_profile_sync_field_url(user_profile, d)
                  end
                else
                  link_to "#{I18n.t(d.field_name, default: d.field_name)} : #{str}", edit_admin_user_profile_sync_field_url(user_profile, d)
                end
              else
                str = link_to "#{I18n.t(d.field_name, default: d.field_name)} #{str}", edit_admin_user_profile_sync_field_url(user_profile, d)
                str.html_safe
              end
            end
            column :field_mapping do |d|
              if d.user_profile.blank?
                d.field_mapping
              elsif d.field_name == "product_id"
                str = [d.field_mapping]
                str << d.user_profile.source_key_1 unless d.user_profile.source_key_1.blank?
                str << d.user_profile.source_key_2 unless d.user_profile.source_key_2.blank?
                str << d.user_profile.source_key_3 unless d.user_profile.source_key_3.blank?
                str = str.join(" + ")
                str
              else
                d.field_mapping
              end

              # if d.field_mapping == "SKU"
              #   if d.user_profile.blank?
              #     d.field_mapping
              #   else
              #     str = [d.field_mapping]
              #     str << d.user_profile.source_key_1 unless d.user_profile.source_key_1.blank?
              #     str << d.user_profile.source_key_2 unless d.user_profile.source_key_2.blank?
              #     str << d.user_profile.source_key_3 unless d.user_profile.source_key_3.blank?
              #     str = str.join(" + ")
              #     str
              #   end
              # else
              #   d.field_mapping
              # end
            end
            column :extra_attributes do |d|
              result = []
              if d.extra_attributes.is_a?(Hash) && (d.extra_attributes.length > 0)
                i = 0

                extra_attr_keys = d.extra_attributes.keys
                extra_attr_values = d.extra_attributes.values

                while i < extra_attr_keys.length
                  v = extra_attr_values[i]
                  if extra_attr_keys[i] == "price_round"
                    v = "#{Settings.price_round_options[extra_attr_values[i]]} (#{v})"
                  end
                  result << "#{I18n.t(extra_attr_keys[i])} : #{v}"
                  i += 1
                end
              end
              result.join("<br>").html_safe
            end
            column :action do |d|
              link_to "Delete", admin_user_profile_sync_field_url(user_profile, d.id), data: {confirm: "Are you sure?"}, method: :delete
            end
          end
          div class: "grid grid-cols-1 gap-6" do
            link_to "Mark Mapping Correct", mark_correct_mapping_admin_user_profile_url(user_profile, path: "mapping_data/#{user_profile.id}")
          end
        end

        panel "Webhook Files" do
          link_to "List Files", list_webhook_files_admin_user_profile_url(profile), target: "_blank"
        end

        panel "Activity Logs #{link_to "View all", admin_product_logs_url(order: "created_at_desc", q: {user_profile_id_eq: user_profile.id})}".html_safe do
          limits = 25
          limits = 15 if user_profile.feed_type == "import"
          if user_profile.feed_type == "import"
            profile = UserProfile.with_deleted.find(params[:id])
            total_import = Rails.cache.fetch("total_product_import_#{profile.id}", expires_in: 5.hours) do
              profile.product_logs.sum(:number_product_updated)
            end
            span "<strong>Total Product Import: #{total_import}</strong>".html_safe
          end
          table_for user_profile.product_logs.includes(:user_profile).order("created_at desc").limit(limits) do
            column :status
            column "Location/Remark" do |d|
              errors = d.try(:error_list).to_s.split(",")
              if d.remark == "errbit" && d.status == false
                link_to(d.remark_values["id"], d.remark_values["url"], target: :_blank)
              else
                out = ""
                out += "#{d.cache ? "Cached " : ""}#{(d.location_id != user_profile.location_id) ? d.location_id : ""} #{d.remark} "
                out += if errors[0] == "download_file_for_details"
                  link_to "see error list", s3_links_admin_product_log_url(d, file: ProductUpdateStatus::FILE_ERROR_LIST, name: "error_list.csv"), target: :_blank
                else
                  "#{errors[0]}#{(errors.count > 1) ? " (+#{errors.count - 1} more)" : ""}"
                end
                out += " #{d.remark_values}"

                if errors[0] == "download_file_for_details"
                  out.html_safe
                else
                  out
                end
              end
            end
            column "Errbit" do |d|
              link_to("Link", d.error_link, target: :_blank) if d.error_link
            end

            unless profile.feed_type == "update"
              column :init_credits
            end

            column "trigger" do |d|
              if d&.trigger_by&.start_with?("delay")
                words = d.trigger_by.split("_")
                "#{words.first}x#{words.count - 1} #{words.last}"
              else
                d.trigger_by
              end
            end
            column "No. SKU Updated" do |d|
              user_profile.stats(d).html_safe
            end
            column "Created At" do |d|
              "#{d.created_at.in_time_zone(user_profile.timezone).strftime("%m-%d %H:%M %z")} (#{time_ago_in_words(d.created_at)}) "
            end
            column "Taken & Q" do |d|
              taken = if d.start_time && d.end_time
                distance_of_time_in_words(d.start_time, d.end_time)
              else
                "-"
              end
              webhook_query_time = if d.webhook_query_created_at.present? && d.webhook_query_completed_at.present?
                " (#{distance_of_time_in_words(d.webhook_query_created_at, d.webhook_query_completed_at)})"
              else
                ""
              end
              "Taken:#{taken} / Q:#{(d.queuing_at && d.start_time) ? distance_of_time_in_words(d.queuing_at, d.start_time) : "N/A"}#{webhook_query_time}".html_safe
            end
            column :resumed
            column "More" do |d|
              link_to "More", admin_product_log_url(d)
            end
            column "Products since" do |d|
              link_to "List", load_last_log_admin_user_profile_url(user_profile, log_id: d.id)
            end
            if user_profile.feed_type == "import" || user_profile.feed_type == "remove"
              column :revert_process do |log|
                revert_link(log)
              end
            end
          end
        end

        panel "Email Logs" do
          table_for user_profile.email_logs.order("created_at desc").limit(5) do
            column :recipient
            column "Created At" do |d|
              d.created_at.in_time_zone(user_profile.timezone).strftime("%m-%d %H:%M %z")
            end
            column :ago do |d|
              time_ago_in_words(d.created_at)
            end
            column :read
            column "Attachment" do |d|
              attachment_link = "#{Settings.hostname}#{d.source_file.url}".gsub("/system", "/drive/")
              if attachment_link.to_s.index("missing.png").present?
                "No Attachment"
              else
                link_to "Download", attachment_link
              end
            end
            column "Actions" do |d|
              link_to "View", admin_email_log_url(d)
            end
          end
        end

        if user_profile.run_webhook_mode?
          panel "Webhook Logs" do
            table_for user_profile.webhook_logs.order("created_at desc").limit(5) do
              column "Activity Log" do |web|
                if web.product_log_id.present?
                  link_to(web.product_log_id, admin_product_log_url(id: web.product_log_id))
                else
                  "No Log"
                end
              end
              column "Stages" do |web|
                if web.max_stage.to_i > 0
                  "Running for #{web.current_stage}/#{web.max_stage}"
                else
                  "Single Stage"
                end
              end
              column :external_id
              column "Get Current File" do |web|
                link_to "Download", current_webhook_file_admin_user_profile_url(profile, params: {external_id: web&.external_id&.split("debug::")&.last}), target: "_blank"
              end
              column "More" do |web|
                link_to "More", admin_webhook_log_url(web)
              end
            end
          end
        end

        panel "Fargate Events #{link_to "View all", admin_schedulable_events_url(order: "created_at_desc", q: {schedulable_id_eq: user_profile.id})}".html_safe do
          table_for Schedulable::Event.where(schedulable_id: user_profile.id).latest_first.limit(10) do
            column :id
            column :created_at do |d|
              "#{d.created_at.in_time_zone(user_profile.timezone)} (#{time_ago_in_words(d.created_at)})"
            end
            column :trigger_by
            column :view_logs do |event|
              link = event.fargate_log_url
              if link
                link_to(event.fargate_task_id, link, target: :_blank)
              else
                event.remark
              end
            end
            column :view_log_insights do |event|
              link = event.fargate_log_insight_url
              if link
                link_to("View", link, target: :_blank)
              end
            end
          end
        end
      end
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.messages.keys)
    f.inputs "Edit User Profile" do
      input :profile_name
      input :delete_mode, as: :select, collection: Settings.delete_mode.map { |k, v| [v, k] }.sort, include_blank: false
      input :partial_match
      input :feed_type, collection: ["import", "update", "remove"], include_blank: false
      input :fargate_task_definition, collection: f.object.available_fargate_tasks
      input :cache_expiry
      input :bypass_cache
    end

    f.inputs "Shopify Filters", class: "filter_text" do
      input :published_filter, collection: ["any", "published", "visible_option", "hidden"], include_blank: false
      input :collection_filter_id, label: "Collection"

      input :product_type_filter
      input :exclude_product_types
      input :include_tags
      input :exclude_tags
      input :vendor_filter, as: :text, input_html: {rows: 2}
      input :exclude_vendors

      if current_admin_user.role == "admin" || f.object.feed_type != "import"
        input :include_skus, as: :text, input_html: {rows: 2}
        input :exclude_skus, as: :text, input_html: {rows: 2}
        input :barcode_filter, as: :text, input_html: {rows: 2}
        input :exclude_barcodes, as: :text, input_html: {rows: 2}
        input :product_title_filter, as: :text, input_html: {rows: 2}
        input :exclude_product_titles, as: :text, input_html: {rows: 2}
      end

      input :ignore_update_when_qty_low_level
      input :ignore_dont_track_inventory
      input :ignore_zero_quantity
      input :bypass_blank_row
      input :update_only_when_zero_qty, label: "Ignore variants with quantity"
      input :store_filters, as: :jsonb
    end

    f.inputs "Feed Data Filters" do
      input :filter_params
      input :skip_total_rows, as: :number
    end

    # f.inputs "Schedule" do
    #   input :scheduler_enabled, input_html: {:disabled => true}
    #   input :status, as: :select, collection: Settings.user_profile_status.map{ |k,v| [v,k] }, include_blank: false, input_html: {:disabled => true}
    #   input :job_type, as: :select, collection: Settings.job_type.map{ |k,v| [v,k] }, include_blank: false, input_html: {:disabled => true}
    #   input :job_time, input_html: {:disabled => true}
    #   input :job_interval, input_html: {:disabled => true}
    #   input :every_minutes, input_html: {:disabled => true}
    #   #input :process_in_nodejs
    # end
    f.inputs "Source/Connection settings" do
      input :source_type, as: :select, collection: Settings.admin_source_types.map { |k, v| [v, k] }.sort, include_blank: false
      input :file_name
      input :source_file_file_name
      input :path_to_file
      input :ftp_rename
      input :custom_file_name
      input :ftp_whitelist
      input :ftp_mode
      input :source_url, input_html: {rows: 2}
      input :url_date_formatting
      input :login_url
      input :cache_data, label: "Cache downloaded data"
      input :file_format, as: :select, collection: Settings.file_format.map { |k, v| [v, k] }, include_blank: false

      input :io_mode, as: :select, collection: Settings.io_mode.map { |k, v| [v, k] }, include_blank: false
      input :source_auth
      input :auth_type, as: :select, collection: {"basic_auth" => "Basic Auth", "bearer_token" => "Bearer Token", "bearer_token_body" => "Bearer Token Body", "token_path_body" => "Token on path", "oauth_2" => "OAuth2", "oauth_2_with_password" => "OAuth2 with Password", "oauth_2_for_odoo" => "OAuth2 for Odoo", "none" => "None"}.map { |k, v| [v, k] }, include_blank: false
      input :acc_name
      input :namespace_identifier
      input :google_out_insert, label: "Google Sheet - Create new line"
      input :clear_google_sheet, label: "Google Sheet - Replace all"
      input :sheet_name
      input :filename_in_zip, label: "Select specific file in a zipfile"
      # input :uudecode
      input :custom_login_field
      input :custom_login_password_field, as: :string
      input :custom_extra_field
      input :custom_extra_field_value
      input :special_cookie

      input :checksum_check, label: "Checksum check (Enable minute schedule for FTP update feed)"
      input :file_encoding, as: :select, collection: Settings.file_encoding.map { |k, v| [v, k] }, include_blank: false
      input :woocommerce_consumer_key
      input :woocommerce_consumer_secret
      input :connection_settings, as: :jsonb
    end

    f.inputs "Email settings" do
      input :received_email, label: "Another unique email to identify this feed"
      input :match_subject, label: "Email subject matching"
      if current_admin_user.role == "admin"
        input :email, label: "Profile Email"
      end
      input :export_email, label: "Export Email"
    end

    f.inputs "CSV settings" do
      input :auto_file_settings
      input :csv_class, as: :select, collection: [["Default", ""], ["FastCSV", "FastCSV"], ["Csv Reader (no col separator)", "raw"], ["Raw Reader", "raw_file"], ["Raw Reader v2", "raw_filev2"], ["Read using foreach", "raw_filev3"]], include_blank: false
      input :col_sep, as: :select, collection: Settings.column_separators.map { |k, v| [v, k] }, include_blank: false
      input :row_sep
      input :quote_char
      input :use_quote, label: "Disable auto remove double quotes when file can't read for the first time"
      input :find_val
      input :replace_val
      input :xlsx_v2_enabled, label: "Use v2 XLSX opener (XLSX only)"
    end

    f.inputs "Product Key settings" do
      input :has_header
      input :shopify_product_key
      input :store_prefix
      input :prefix
      input :postfix
      input :case_sensitive

      # input :product_id_key
      input :variant_key_1
      input :auto_remove_default_title
      input :variant_key_2
      input :variant_key_3
      input :source_key_1
      input :source_key1_rules, as: :text, input_html: {rows: 2}
      input :source_key_2
      input :source_key2_rules, as: :text, input_html: {rows: 2}
      input :source_key_3
      input :source_key3_rules, as: :text, input_html: {rows: 2}
      input :product_key_separator

      # input :file_format, as: :select, collection: Settings.file_format.map{ |k,v| [v,k] }, include_blank: false
      input :parent_node
      input :variant_node
    end

    f.inputs "Import Settings" do
      # input :quantity_key
      # input :rules_json
      # input :add_to_init_quantity

      # input :price_key
      # input :price_formula

      # input :public_file
      # input :price_delimiter

      # input :compare_price_at_key
      # input :force_override_compare_at_price
      # input :barcode_key
      # input :product_title_key
      # input :weight_key
      # input :weight_unit
      # input :published_key
      # input :eq_hide_value
      # input :eq_show_value
      # input :price_round, as: :select, collection: Settings.price_round_options.map{ |k,v| [v,k] }, include_blank: false
      input :variant_image_link
      input :assign_variants_to_first_image
      input :skip_import_with_zero_qty
      input :shopify_inventory_management

      input :import_tags
      input :import_sort
      input :export_sort
      # input :tags_key
    end

    f.inputs "Update Settings" do
      # input :override_tags
      input :auto_reset_qty_when_above_this_level
      input :low_stock_level

      input :published_apply_to_all
      input :unpublished_apply_to_all

      input :published_apply_matching_products
      input :unpublished_apply_matching_products

      input :auto_visible_check_all_sku
      # input :zero_qty
      input :update_duplicate_product_key
      input :shopify_track_inventory
      input :load_products_cache
      input :remove_product_when_all_locations_nil
      input :use_high_duplicate_qty
      input :use_low_duplicate_qty
    end

    f.inputs "Warning" do
      input :match_and_delete, label: "When found match it will not continue find same match again"
      input :auto_reset_quantity, label: "Unmatched only against variants that updated by syncX: Stock Sync before."
      input :hide_unmatch_products, label: "Unmatched against all variants"
      # input :unmatched_force_hide
    end

    f.inputs "HTTP Params" do
      input :call_params
      input :http_method, as: :select, collection: [["get", "get"], ["post", "post"]], include_blank: false
      input :header_params
      input :body_raw
      input :start_page
      input :max_page
      input :page_limit
      input :api_sleep_sec_per_page
    end

    f.inputs "Advanced" do
      input :extra_options, as: :jsonb
      input :job_queue
      input :location_id
      input :auto_pick_first_available_location
      input :active_product_log_id
      # input :webhook_mode
      input :enable_image_validation
    end
    f.actions
  end
end
