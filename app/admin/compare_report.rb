ActiveAdmin.register_page "Compare Report" do
  menu parent: "reports_or_logs"

  content do
    panel "Compare Report" do
      render "/admin/report/compare_report"
    end
  end

  controller do
    def index
      @data = []
      ShopifyReview.where(app: "syncee").order("times desc").each do |review|
        if (user = User.where(shopify_domain: review.link).take)
          row = {}
          row[:user] = user
          row[:review] = review
          @data << row
        end
      end
    end
  end
end
