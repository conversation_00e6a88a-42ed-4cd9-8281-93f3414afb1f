ActiveAdmin.register_page "Region" do
  menu parent: "setting"

  page_action :change_region, method: :get do
    RedisManager.set_ecs_region(params[:region])
    render json: {data: RedisManager.get_ecs_region}
  end

  page_action :reset_region, method: :get do
    RedisManager.del_ecs_region
    render json: {data: RedisManager.get_ecs_region}
  end

  content do
    render partial: "region", locals: {region: RedisManager.get_ecs_region}
  end
end
