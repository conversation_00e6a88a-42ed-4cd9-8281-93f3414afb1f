ActiveAdmin.register_page "Unpaid Stripe subscriptions" do
  menu parent: "reports_or_logs"

  content do
    panel "Unpaid Stores" do
      render "/admin/report/unpaid_stripe"
    end
  end

  controller do
    def index
      # Query all unpaid Stripe subscriptions
      @unpaid_plans = []
      result = Stripe::Subscription.list({limit: 100, status: "unpaid"})
      if result.data.length > 0
        @unpaid_plans << result.data
      end
      @unpaid_plans.flatten!
    end
  end
end
