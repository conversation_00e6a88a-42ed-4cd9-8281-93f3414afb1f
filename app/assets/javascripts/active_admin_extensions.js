document.addEventListener('DOMContentLoaded', (event) => {
  //fetch caching
  const CACHE_VERSION = 1;
  const CURRENT_CACHES = {
    defaultExtraAttribute: `default_extra_attributes_v${CACHE_VERSION}`,
  };
  const expectedCacheNamesSet = new Set(Object.values(CURRENT_CACHES));

  //delete cache not in CURRENT_CACHES
  caches
    .keys()
    .then((cacheNames) =>
      Promise.all(
        cacheNames.map((cacheName) => {
          if (!expectedCacheNamesSet.has(cacheName)) {
            return caches.delete(cacheName);
          }
        })
      )
    )
    .then(() => { });

  //cache fetch wrapper
  const cached_fetch = async (key, request) => {
    const cache = await caches.open(key);
    let response = await cache.match(key);
    if (!response) {
      response = await request;
    }
    if (response.ok) {
      cache.put(key, response.clone());
      return response.json();
    } else {
      return Promise.resolve({});
    }
  };

  //reset sync field extra attr
  document
    .querySelectorAll('#reset-extra-attributes-default')
    .forEach((node) => {
      node.addEventListener('click', (e) => {
        e.preventDefault();
        let fieldName = document.getElementById('sync_field_field_name');
        const target = document.querySelector(
          '#sync_field_extra_attributes_input > textarea'
        );
        if (fieldName && target) {
          fieldName = fieldName.value.replace(/^(.*)_[0-9]*$/g, '$1');
          cached_fetch(
            CURRENT_CACHES.defaultExtraAttribute,
            fetch('/admin/sync_fields/default_extra_attributes')
          )
            .then((response) => {
              target.value = JSON.stringify(response[fieldName] || {});
            })
            .catch((error) => {
              alert(error.message);
            });
        }
      });
    });

  //copy profile
  //copy all profile from store
  document
    .querySelectorAll('.copy_all_profiles_to, .copy_profile_to')
    .forEach((node) => {
      node.addEventListener('click', (e) => {
        e.preventDefault();
        const csrf = document.querySelector("meta[name='csrf-token']");
        const current_user = node.getAttribute('data-target');
        const user_id = document.getElementById(`user_id_${current_user}`);
        const method = node.getAttribute('data-method');
        const resource = node.getAttribute('data-resource');
        if (user_id && user_id.value && csrf) {
          fetch(`/admin/${resource}/${current_user}/${method}`, {
            method: 'POST',
            body: JSON.stringify({ id: current_user, user_id: user_id.value }),
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrf.content,
            },
          })
            .then((response) => response.json())
            .then((data) => {
              window.location = `/admin/${resource}/${data.id}`;
            })
            .catch((error) => {
              alert(error.message);
              window.location = `/admin/${resource}`;
            });
        }
      });
    });

  // pre load products count
  document.querySelectorAll('.load-store').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();
      let profile_id = window.location.pathname.split('/').pop();
      fetch(`/admin/user_profiles/${profile_id}/pre_load_store`)
        .then((response) => response.text())
        .then((data) => {
          const comfirmation = confirm(
            `Warning, we're loading ${data} of product, do you want to proceed?`
          );
          if (comfirmation === true) {
            window.open(
              `/admin/user_profiles/${profile_id}/load_store`,
              '_blank'
            );
          }
        })
        .catch((error) => alert(error.message));
    });
  });

  // check cache feed data key
  document.querySelectorAll('.check-process-cache-key').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();
      node.classList.add('disabled');
      const cache = document.getElementById(node.getAttribute('data-target'));
      cache.innerText = '';
      fetch(
        `/admin/user_profiles/${window.location.pathname.split('/').pop()}/check_process_cache_key`
      )
        .then((response) => response.text())
        .then((data) => {
          node.classList.remove('disabled');
          cache.innerText = data;
        })
        .catch((error) => {
          node.classList.remove('disabled');
          cache.innerText = error.message;
        });
    });
  });

  document.querySelectorAll('.is-valid-api-check').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();
      node.classList.add('disabled');
      const cache = document.getElementById(node.getAttribute('data-target'));
      cache.innerText = '';
      fetch(
        `/admin/stores/${window.location.pathname.split('/').pop()}/check_valid_api`
      )
        .then((response) => response.text())
        .then((data) => {
          node.classList.remove('disabled');
          cache.innerText = data;
        })
        .catch((error) => {
          node.classList.remove('disabled');
          cache.innerText = error.message;
        });
    });
  });

  document.querySelectorAll('.get-profile-locations').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();

      node.classList.add('disabled');

      const targetDiv = node.dataset.target;
      const profileId = node.dataset.profile;
      const dataval = document.getElementById(targetDiv);

      dataval.innerHTML = '';

      fetch(`/admin/user_profiles/${profileId}/get_current_location`)
        .then((res) => res.text())
        .then((data) => {
          node.classList.remove('disabled');
          dataval.innerHTML = data;
        })
        .catch((err) => {
          node.classList.remove('disabled');
          dataval.textContent = err.message;
        });
    });
  });

  // variants count
  document.querySelectorAll('.variants-check').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();
      fetch(
        `/admin/user_profiles/${window.location.pathname.split('/').pop()}/check_variant_count`
      )
        .then((response) => response.text())
        .then((data) => alert(`Total variants in store: ${data}`))
        .catch((error) => alert(error.message));
    });
  });

  // auto populate delims for csv types
  document.querySelectorAll('#user_profile_csv_class').forEach((node) => {
    node.addEventListener('change', (e) => {
      const colsep = document.getElementById('#user_profile_col_sep');
      const rowsep = document.getElementById('#user_profile_row_sep');
      const value = node.value;
      if (colsep && rowsep) {
        if (value === 'raw_filev2') {
          colsep.value = ',';
          rowsep.value = '\\r\\n';
        } else if (value === 'FastCSV') {
          colsep.value = '';
          rowsep.value = '';
        } else {
          colsep.value = ',';
          rowsep.value = '';
        }
      }
    });
  });

  // check woocom validity
  document.querySelectorAll('.load-woocommerce').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();
      fetch(
        `/admin/user_profiles/${window.location.pathname.split('/').pop()}/check_woocommerce_validity`
      )
        .then((response) => response.text())
        .then((data) => alert(data))
        .catch((error) => alert(error.message));
    });
  });


  //copy text button function
  document.querySelectorAll('.copy-button').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();
      const target = document.getElementById(node.getAttribute('data-target'));
      if (target) {
        if (!navigator.clipboard) {
          target.focus();
          target.select();
          document.execCommand('copy');
        } else {
          navigator.clipboard
            .writeText(target.innerText)
            .then(() => { })
            .catch((e) => { });
        }
      }
    });
  });

  //get woocom domain
  document.querySelectorAll('.woocom_redirect_link').forEach((node) => {
    node.addEventListener('click', (e) => {
      e.preventDefault();
      fetch(
        `/admin/user_profiles/${window.location.pathname.split('/').pop()}/get_woocommerce_domain`
      )
        .then((response) => response.text())
        .then((data) => alert(data))
        .catch((error) => alert(error.message));
    });
  });
});

