class BigcommerceClient
  ALL_PRODUCTS_ENDPOINT = "v3/catalog/products"
  ALL_VARIANTS_ENDPOINT = "v3/catalog/variants"
  ALL_BRANDS_ENDPOINT = "v3/catalog/brands"
  ALL_CATEGORY_TREES = "v3/catalog/trees/categories"
  STORE_INFO_ENDPOINT = "v2/store"
  PRODUCT_QUERY_FIELDS = "variants,custom_fields,images"

  def initialize(user)
    @user = user
    @query_limit = user.api_query_limit || 250
    @bigcommerce_host = "https://api.bigcommerce.com/stores/#{@user.bigcommerce_store_hash}/"
  end

  def get_info
    url = @bigcommerce_host + STORE_INFO_ENDPOINT
    response = HTTParty.get(url, headers: api_headers)
    response.parsed_response
  end

  # Currently, only filter by brand id is supported on stock sync
  def get_products_by_brand(page_limit = nil, brand_ids = [])
    return get_all_products(page_limit) if brand_ids.empty?
    bigcommerce_products = []
    url = File.join(@bigcommerce_host, ALL_PRODUCTS_ENDPOINT)

    brand_ids.each do |brand_id|
      current_page = 1
      total_pages = nil

      loop do
        puts "Querying (Brand ID: #{brand_id}) - page #{current_page}"
        query = {include: PRODUCT_QUERY_FIELDS, limit: @query_limit, page: current_page, brand_id: brand_id.strip}
        response = HTTParty.get(url, query: query, headers: api_headers)
        response = response.parsed_response

        if response["data"].present?
          bigcommerce_products << response["data"]
          bigcommerce_products.flatten!
        end

        total_pages ||= response["meta"]["pagination"]["total_pages"]

        current_page += 1
        break if page_limit && bigcommerce_products.length > page_limit.to_i
        break if current_page > total_pages
      end
    end

    bigcommerce_products
  end

  def get_all_products(page_limit = nil, filters = {})
    bigcommerce_products = []
    current_page = 1
    url = File.join(@bigcommerce_host, ALL_PRODUCTS_ENDPOINT)
    query = {include: PRODUCT_QUERY_FIELDS, limit: @query_limit}
    query.merge!(filters) if filters.present?

    loop do
      query[:page] = current_page
      response = HTTParty.get(url, query: query, headers: api_headers)
      response = response.parsed_response

      if response["data"].present?
        bigcommerce_products << response["data"]
        bigcommerce_products.flatten!
      end

      total_pages ||= response["meta"]["pagination"]["total_pages"]

      current_page += 1
      break if page_limit && bigcommerce_products.length > page_limit.to_i
      break if current_page > total_pages.to_i
    end

    bigcommerce_products
  end

  def get_products(page_limit, profile)
    if profile.has_filter?
      get_products_by_brand(page_limit, profile.vendor_filter.split(",").reject(&:blank?))
    else
      get_all_products(page_limit)
    end
  end

  def get_products_count
    url = File.join(@bigcommerce_host, ALL_PRODUCTS_ENDPOINT)
    query = {page: 1, limit: 1}
    response = HTTParty.get(url, query: query, headers: api_headers)
    response = response.parsed_response
    response["meta"]["pagination"]["total"]
  end

  def get_variants_count
    url = File.join(@bigcommerce_host, ALL_VARIANTS_ENDPOINT)
    query = {page: 1, limit: 1}
    response = HTTParty.get(url, query: query, headers: api_headers)
    response = response.parsed_response
    response["meta"]["pagination"]["total"]
  end

  def get_product(id)
    url = File.join(@bigcommerce_host, ALL_PRODUCTS_ENDPOINT, id.to_s)
    query = {include: PRODUCT_QUERY_FIELDS}

    response = HTTParty.get(url, query: query, headers: api_headers)
    response = response.parsed_response
    response["data"]
  end

  # def get_product_by_sku(sku, use_sku = true)
  def get_product_by_sku(sku, filter_type = "sku")
    is_variant = false
    data = nil
    product_route = File.join(@bigcommerce_host, ALL_PRODUCTS_ENDPOINT)
    variant_route = File.join(@bigcommerce_host, ALL_VARIANTS_ENDPOINT)

    query = {include: PRODUCT_QUERY_FIELDS}

    filter_type = "keyword" if filter_type == "name" # keyword is more accurate than name
    query[filter_type.to_sym] = sku.strip # support :sku, :upc, :name, :mpn

    product_response = HTTParty.get(product_route, query: query, headers: api_headers)
    product_response = product_response.parsed_response
    data = product_response["data"] if product_response.present?
    return data, is_variant unless data.blank?

    is_variant = true
    query.delete(:include)
    variant_response = HTTParty.get(variant_route, query: query, headers: api_headers)
    variant_response = variant_response.parsed_response
    data = variant_response["data"] if variant_response.present?
    [data, is_variant]
  end

  def get_all_brands
    url = File.join(@bigcommerce_host, ALL_BRANDS_ENDPOINT)
    current_page = 1
    brands = []

    loop do
      response = HTTParty.get(url, headers: api_headers, query: {include_fields: "id,name", limit: @query_limit, page: current_page})
      response = response.parsed_response

      if response["data"].present?
        brands << response["data"]
        brands.flatten!
      end

      total_pages ||= response["meta"]["pagination"]["total_pages"]
      current_page += 1

      break if current_page > total_pages.to_i
    end
    brands
  end

  def get_all_categories_v2
    url = File.join(@bigcommerce_host, ALL_CATEGORY_TREES)
    current_page = 1
    categories = []

    loop do
      response = HTTParty.get(url, headers: api_headers, query: {include_fields: "category_id,name", limit: @query_limit, page: current_page})
      response = response.parsed_response

      if response["data"].present?
        categories.concat(response["data"])
      end

      total_pages ||= response["meta"]["pagination"]["total_pages"]
      current_page += 1

      break if current_page > total_pages.to_i
    end
    categories
  end

  def get_locations
    url = File.join(@bigcommerce_host, "v3/inventory/locations")
    response = HTTParty.get(url, headers: api_headers)
    locations = response.parsed_response.fetch("data", []).map { |loc| loc.slice("id", "label", "enabled") }
    locations.each { |location| location["name"] = location.delete("label") } # new label for graphql purpose
    locations
  end

  def get_locations_inventory(sku)
    url = File.join(@bigcommerce_host, "/v3/inventory/items")
    response = HTTParty.get(url, headers: api_headers, query: {"sku:in": sku})
    response.parsed_response["data"] || []
  end

  def get_all_categories
    url = "https://api.bigcommerce.com/stores/#{@user.bigcommerce_store_hash}/v3/catalog/categories"
    current_page = 1
    total_pages = nil
    categories = []

    loop do
      query = {include_fields: "id,name,parent_id", page: current_page, limit: @query_limit}
      response = HTTParty.get(url, query: query, headers: api_headers)
      begin
        response = response.parsed_response

        if response["data"].present?
          categories << response["data"]
          categories.flatten!
        end

        Rails.logger.debug response["meta"].inspect

        total_pages ||= response["meta"]["pagination"]["total_pages"] if response["meta"]
      rescue JSON::ParserError => e
        Rails.logger.debug(e.message)
        Rails.logger.debug(e.backtrace.join("\n"))
        if total_pages.nil?
          Airbrake.notify(e, user_profile_id: @user.bigcommerce_store_hash)
          break
        end
      end

      current_page += 1
      break if current_page > total_pages.to_i
    end

    categories
  end

  private

  def api_headers
    {
      "X-Auth-Client": ENV["BIGCOMMERCE_CLIENT_ID"],
      "X-Auth-Token": @user.bigcommerce_access_token,
      "Content-Type": "application/json",
      Accept: "application/json"
    }
  end
end
