<div id = "container">
  <div>
    <label for="parent-node">Parent Node</label><br>
    <input type="text" id="parent-node" value="<%= @parent_node %>" placeholder="JMESPath + Liquid" class="JP-expr form-control">
    <label for="variant-node">Variant Node</label><br>
    <input type="text" id="variant-node" value="<%= @variant_node %>" placeholder="JMESPath + Liquid" class="JP-expr form-control">
    <label for="limit-filter-node">Limit filter</label><br>
    <input type="text" id="limit-filter-node" value="" placeholder="| [[0],[1]]" class="JP-expr form-control">
    <p>If the feed data is too big, you can use limit filter to chain additional JMESPath syntax to filter, for example using | [[0],[1]] to only process first 2 products, | [0] to only process the first product, etc.</p>
  </div>
  <div>
    <button type="button" id="execute" class="filters-form-submit mt-3" onclick="load_feed_data()">Load Feed Data</button>
    <button type="button" id="test-node" class="filters-form-submit mt-3" onclick="test_mapping()">Test Mapping</button>
    <button type="button" id="save-node" class="filters-form-submit mt-3" onclick="save_nodes()">Save Nodes</button>
  </div>
  <div class="grid grid-cols-2">
    <div class="code">
      <p>Feed data</p>
      <div id="feed"></div>
    </div>
    <div class="code">
      <p>Transformed data</p>
      <div id="transformed"></div>
    </div>
  </div>
</div>

<script type="text/javascript">
  const themes = {
    dark: {json: "ace/theme/vibrant_ink"},
    light: {json: "ace/theme/tomorrow"},
  };

  var dark = document.documentElement.classList.contains("dark");

  const editor_feed = ace.edit("feed",{
    theme: themes[dark ? "dark" : "light"].json,
    mode: "ace/mode/json",
    tabSize: 2,
    autoScrollEditorIntoView: true,
    maxLines: 60,
    minLines: 30
  });
  const editor_transformed = ace.edit("transformed",{
    theme: themes[dark ? "dark" : "light"].json,
    mode: "ace/mode/json",
    tabSize: 2,
    autoScrollEditorIntoView: true,
    maxLines: 60,
    minLines: 30
  });

  document.querySelector("button.dark-mode-toggle").addEventListener("click", () => {
    dark = !dark;
    editor_feed.setTheme(themes[dark ? "dark" : "light"].json);
    editor_transformed.setTheme(themes[dark ? "dark" : "light"].json);
  })

  const engine = new window.liquidjs.Liquid({cache: true});
  const csrf = document.querySelector("meta[name='csrf-token']").content;
  
  var feedLoad = false;
  var nodeLoad = false;

  const fd_button = document.getElementById("execute");
  const sn_button = document.getElementById("save-node");
  const parent_node = document.getElementById("parent-node");
  const variant_node = document.getElementById("variant-node");
  const limit_filter = document.getElementById("limit-filter-node");
  const loadFeedUrl = '<%=escape_javascript load_jmespath_feed_data_admin_user_profile_url %>';
  const saveNodeUrl = '<%=escape_javascript save_nodes_admin_user_profile_url %>';
  const loadFeed = new XMLHttpRequest();
  const saveNode = new XMLHttpRequest();

  // on ready state
  loadFeed.onreadystatechange = function () {
    if(this.readyState === 4){
      feedLoad = false;
      fd_button.textContent = "Load Feed Data";
      if(this.status === 200){
        editor_feed.setValue(JSON.stringify(JSON.parse(this.response), undefined, 2), -1);
      }
    }
  };

  function onAbort() {
    feedLoad = false;
    fd_button.textContent = "Load Feed Data";
    editor_feed.setValue("{\"status\": \"Load feed canceled\"}", -1)
  }

  loadFeed.addEventListener("abort", onAbort);

  function load_feed_data() {
    if (!feedLoad){
      feedLoad = true;
      loadFeed.open('GET', loadFeedUrl);
      loadFeed.setRequestHeader("X-CSRF-Token", csrf);
      loadFeed.send();
      editor_feed.setValue("{\"status\": \"Loading feed data...\"}", -1)
      fd_button.textContent = "Cancel";
    } else if(loadFeed !== null) {
      loadFeed.abort();
    }
  }

  saveNode.onreadystatechange = function () {
    if(this.readyState === 4){
      sn_button.classList.add("filters-form-submit");
      sn_button.textContent = "Save Nodes"
      nodeLoad = false;
      if(this.status === 200){
        alert(this.response); 
      } else {
        alert(`Save failed: ${this.response}`);
      }
    }
  };

  function save_nodes() {
    if (!nodeLoad){
      payload = {}
      payload["parent_node"] = parent_node.value || '';
      payload["variant_node"] = variant_node.value || '';
      sn_button.textContent = "Saving..."
      sn_button.classList.remove("filters-form-submit");
      saveNode.open("POST", saveNodeUrl);
      saveNode.setRequestHeader("Content-Type", "application/json");
      saveNode.setRequestHeader("X-CSRF-Token", csrf);
      nodeLoad = true;
      saveNode.send(JSON.stringify(payload));
    }
  }

  function test_mapping() {
    try {
      const data = JSON.parse(editor_feed.getValue());
      const limit_value = limit_filter.value
      const parent_value = limit_value ? `${engine.parseAndRenderSync((parent_node.value || ''), {})} ${limit_value}` : engine.parseAndRenderSync((parent_node.value || ''), {});
      let products = null;

      if (parent_value){
        products = jmespath.search(data, parent_value);
      } else if(data && data.constructor === Array){
        products = data;
      }

      if (variant_node.value && products && products.constructor === Array){
        let variant_value = variant_node.value || '';
        const root_data = (variant_value.charAt(0) === ".");
        variant_value = variant_value.substring(root_data ? 1 : 0);
        const products_length = products.length;
        for(let i = 0; i < products_length; i++){
          variants = jmespath.search(root_data ? data : products[0], engine.parseAndRenderSync((variant_value || ''), {product: products[0]}));
          variants = (variants && variants.constructor === Object) ? [variants] : variants
          if (variants && variants.constructor === Array && variants.length > 0){
            variants.forEach((variant, i) => {
              variant = (variant && variant.constructor === Array) ? variant.reduce(
                (accumulator, currentValue) => (
                currentValue.constructor === Object ? Object.assign(accumulator, currentValue) : accumulator
              ),{}) : variant;
              
              products.push(JSON.parse(JSON.stringify(variant)));
              Object.keys(variant).forEach((variant_key, i) => {
                let new_key = variant_key
                if (new_key in products[0]){
                  new_key = `stock_sync_variant_${new_key}`
                }
                for(let _ = 0; _ < 60000; _++){
                  if(new_key in products[0]){
                    new_key = `_${new_key}`;
                  } else {
                    break;
                  }
                }
                if (new_key != variant_key){
                  products[products.length -1][new_key] = products[products.length -1][variant_key];
                  delete products[products.length -1][variant_key];
                }
              });
              products[products.length -1] = {...products[products.length -1], ...products[0]};
            });
            products.splice(0,1);
          } else {
            products.push(products.splice(0,1)[0]);
          }
        }
      }

      if (products){
        editor_transformed.setValue(JSON.stringify(products, undefined, 2), -1);
      } else {
        editor_transformed.setValue(JSON.stringify({error: "products empty"}, undefined, 2), -1);
      }
    } catch(e) {
      let message = '';
      Object.getOwnPropertyNames(e).forEach((e_key, i) => {
        message += e[e_key];
        message += "\n";
      });
      editor_transformed.setValue(message, -1);
      console.error(e);
    }
  }
  
</script>