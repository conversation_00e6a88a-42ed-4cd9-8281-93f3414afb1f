<% if flash[:error].present? %>
  <div class="flash flash_error">
    <%= flash[:error] %>
  </div>
<% end %>

<%
  platforms = [
    { name: "All", value: "" },
    { name: "BigCommerce", value: "bigcommerce" },
    { name: "WooCommerce", value: "woocommerce" },
    { name: "Wix", value: "wix" },
    { name: "Square", value: "square" },
    { name: "EKM", value: "ekm" },
    { name: "Squarespace", value: "squarespace" },
    { name: "N/A", value: "n_a_platform" }
  ]

  # Calculate monthly revenue
  monthly_revenue = {}
  num_months_to_track = 1 # Track only the current month for nw. 
  months_to_track = (0...num_months_to_track).map { |i| (Time.current - i.months).strftime("%Y-%m") }.reverse

  months_to_track.each do |month_key|
    monthly_revenue[month_key] = {} unless monthly_revenue.key?(month_key)
    platforms.each { |p| monthly_revenue[month_key][p[:value]] = 0 }
  end

  @subscriptions.each do |sub|
    platform_key = sub.metadata&.[]('provider') || 'n_a_platform'

    # monthly equivalent amount in cents
    amount_in_cents = sub.plan.amount.to_f
    monthly_amount_in_cents = sub.plan.interval == 'year' ? amount_in_cents / 12.0 : amount_in_cents
    months_to_track.each do |month_key|
      month_start = Date.strptime(month_key, "%Y-%m").beginning_of_month
      month_end = Date.strptime(month_key, "%Y-%m").end_of_month

      if (sub.current_period_start.present? && sub.current_period_end.present? && 
          (Time.at(sub.current_period_start).to_date <= month_end) && 
          (Time.at(sub.current_period_end).to_date >= month_start))

        monthly_revenue[month_key][platform_key] ||= 0
        monthly_revenue[month_key][platform_key] += monthly_amount_in_cents
      end
    end
  end

  # convert back to dollars
  monthly_revenue.each do |month, platforms_data|
    platforms_data.each do |platform, amount_in_cents|
      monthly_revenue[month][platform] = amount_in_cents / 100.0
    end
  end

  platforms_for_revenue_table = platforms.reject { |p| p[:value].blank? }
%>

<div class="bg-white rounded-lg shadow-md p-6 mb-6">
  <h3 class="text-xl font-semibold mb-4">Monthly Revenue Summary</h3>
  <% if monthly_revenue.empty? || months_to_track.all? { |m| monthly_revenue[m].values.all? { |a| a == 0 } } %>
    <p class="text-gray-600">No revenue data available for the tracked months.</p>
  <% else %>
    <div class="overflow-x-auto">
      <table class="w-full table-auto border-collapse">
        <thead>
          <tr class="bg-gray-50 text-gray-600 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-center">Month</th>
            <% platforms_for_revenue_table.each do |p| %>
              <th class="py-3 px-6 text-center"><%= p[:name] %></th>
            <% end %>
            <th class="py-3 px-6 text-center">Total</th>
          </tr>
        </thead>
        <tbody class="text-sm font-light">
          <% monthly_revenue.keys.sort.each do |month_key| %>
            <tr class="border-b border-gray-200 hover:bg-gray-50">
              <td class="py-3 px-6 text-center"><%= Date.strptime(month_key, "%Y-%m").strftime("%B %Y") %></td>
              <% total_month_revenue = 0 %>
              <% platforms_for_revenue_table.each do |p| %>
                <td class="py-3 px-6 text-center">
                  <% amount = monthly_revenue[month_key][p[:value]] || 0 %>
                  <%= number_to_currency(amount) %>
                  <% total_month_revenue += amount %>
                </td>
              <% end %>
              <td class="py-3 px-6 font-semibold text-center">
                <%= number_to_currency(total_month_revenue) %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  <% end %>
</div>


<div class="bg-white rounded-lg shadow-md p-6">
  <h3 class="text-xl font-semibold mb-4">Active Subscriptions (<%= @total_count %> total)</h3>

  <div class="mb-5">
    <div class="border-b border-gray-200">
      <div class="flex flex-wrap gap-1" id="platform-tabs">
        <% platforms.each do |platform| %>
          <button 
            data-platform="<%= platform[:value] %>"
            class="platform-tab inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-t-lg border border-transparent border-b-0 -mb-px transition-colors duration-200 <%= (platform[:value].blank? && params[:provider].blank?) || (platform[:value] == params[:provider]) ? 'bg-white text-blue-600 border-gray-200 border-b-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' %>"
          >
            <%= platform[:name] %>
            <span class="count-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= (platform[:value].blank? && params[:provider].blank?) || (platform[:value] == params[:provider]) ? 'bg-blue-50 text-blue-600' : 'bg-gray-100 text-gray-600' %>">
              0
            </span>
          </button>
        <% end %>
      </div>
    </div>
  </div>

  <div class="overflow-x-auto">
    <table class="w-full table-auto border-collapse">
      <thead>
        <tr class="bg-gray-50 text-gray-600 uppercase text-sm leading-normal">
          <th class="py-3 px-6 text-left">Subscription ID</th>
          <th class="py-3 px-6 text-left">Customer</th>
          <th class="py-3 px-6 text-left">Platform</th>
          <th class="py-3 px-6 text-left">Plan</th>
          <th class="py-3 px-6 text-left">Current Period</th>
          <th class="py-3 px-6 text-left">Amount</th>
          <th class="py-3 px-6 text-left">Store</th>
        </tr>
      </thead>
      <tbody class="text-sm font-light" id="subscriptions-table">
        <% @subscriptions.each do |subscription| %>
          <tr class="subscription-row border-b border-gray-200 hover:bg-gray-50" data-platform="<%= subscription.metadata&.[]('provider').blank? ? 'n_a_platform' : subscription.metadata&.[]('provider') %>">
            <td class="py-3 px-6">
              <%= link_to subscription.id, "https://dashboard.stripe.com/subscriptions/#{subscription.id}", target: "_blank", class: "text-blue-500 hover:text-blue-600" %>
            </td>
            <td class="py-3 px-6">
              <%= link_to subscription.customer.name, "https://dashboard.stripe.com/customers/#{subscription.customer.id}", target: "_blank", class: "text-blue-500 hover:text-blue-600" %>
            </td>
            <td class="py-3 px-6"><%= subscription.metadata&.[]('provider')&.titleize || 'N/A' %></td>
            <td class="py-3 px-6"><%= subscription.plan.nickname %></td>
            <td class="py-3 px-6">
              <%= Time.at(subscription.current_period_start).strftime("%Y-%m-%d") %> to 
              <%= Time.at(subscription.current_period_end).strftime("%Y-%m-%d") %>
            </td>
            <td class="py-3 px-6">
              <%= number_to_currency(subscription.plan.amount.to_f / 100) %> / 
              <%= subscription.plan.interval %>
            </td>
            <td class="py-3 px-6">
              <% if subscription&.metadata&.[]('shop_id').present? %>
                <%= link_to "View Store", "https://admin.stock-sync.com/admin/stores/#{subscription.metadata['shop_id']}", class: "text-blue-500 hover:text-blue-600" %>
              <% end %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const tabs = document.querySelectorAll('.platform-tab');
  const rows = document.querySelectorAll('.subscription-row');
  
  function updateCounts() {
    const platformCounts = {};

    rows.forEach(row => {
      const platform = row.dataset.platform || 'n_a_platform'; // Default to 'n_a_platform' if blank
      platformCounts[platform] = (platformCounts[platform] || 0) + 1;
    });

    tabs.forEach(tab => {
      const tabPlatformValue = tab.dataset.platform;
      let countToDisplay;

      if (tabPlatformValue === '') {
        countToDisplay = Object.values(platformCounts).reduce((sum, current) => sum + current, 0);
      } else {
        countToDisplay = platformCounts[tabPlatformValue] || 0;
      }

      const badge = tab.querySelector('.count-badge');
      badge.textContent = countToDisplay;
    });
  }

  function filterRows(platform) {
    rows.forEach(row => {
      const rowPlatform = row.dataset.platform || '';
      if (platform === '' || rowPlatform === platform) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  }

  function updateActiveTab(activeTab) {
    tabs.forEach(tab => {
      const platformValue = tab.dataset.platform;
      if (tab === activeTab) {
        tab.classList.add('bg-white', 'text-blue-600', 'border-gray-200', 'border-b-white');
        tab.classList.remove('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-900');
        const badge = tab.querySelector('.count-badge');
        badge.classList.add('bg-blue-50', 'text-blue-600');
        badge.classList.remove('bg-gray-100', 'text-gray-600');
      } else {
        tab.classList.remove('bg-white', 'text-blue-600', 'border-gray-200', 'border-b-white');
        tab.classList.add('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-900');
        const badge = tab.querySelector('.count-badge');
        badge.classList.remove('bg-blue-50', 'text-blue-600');
        badge.classList.add('bg-gray-100', 'text-gray-600');
      }
    });
  }

  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const platform = this.dataset.platform;
      filterRows(platform);
      updateActiveTab(this);
      updateCounts();
    });
  });

  const initialPlatform = "<%= params[:provider] %>";
  let initialActiveTab = document.querySelector(`.platform-tab[data-platform="${initialPlatform}"]`);
  if (!initialActiveTab && initialPlatform === '') { // handles the All tab case
    initialActiveTab = document.querySelector(`.platform-tab[data-platform=""]`);
  }
  
  if (initialActiveTab) {
    filterRows(initialPlatform);
    updateActiveTab(initialActiveTab);
  } else { // default to All if no specific platform is set or recognized
    filterRows('');
    updateActiveTab(document.querySelector(`.platform-tab[data-platform=""]`));
  }

  // initialize counts after initial filtering
  updateCounts();
});
</script>