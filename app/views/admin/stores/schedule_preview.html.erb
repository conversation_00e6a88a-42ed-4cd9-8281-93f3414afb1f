<head>
  <%= javascript_include_tag "application" %>
  <script src="https://cdn.jsdelivr.net/npm/apexcharts" type="text/javascript"></script>
</head>

<div class="mb-3 flex gap-4">
  <div class="w-1/4 space-y-2">
    <h2 class="text-lg font-semibold">Last 24 Hours Profile Runs</h2>
    <label for="profile_select" class="block font-medium mb-1">
      Profile ID(s):
    </label>
    <select id="profile_select" multiple size="20" class="w-full h-96 border rounded p-1 focus:outline-none bg-gray-800 text-white">
      <% @series.first[:data].map { |d| d[:x] }.uniq.each do |label| %>
        <% pid = label.split.last %>
        <option value="<%= pid %>"><%= pid %></option>
      <% end %>
    </select>
    <small class="text-gray-400">(Ctrl/Cmd‑click to toggle)</small>
  </div>
  <div class="flex-1 overflow-x-auto">
    <div id="profile_timeline"></div>
  </div>

</div>



<script type="text/javascript">
  document.addEventListener("DOMContentLoaded", () => {
    const FULL_SERIES = <%= raw(@series.to_json) %>;

    const options = {
      chart:  { type: "rangeBar", height: 600, width: "100%", zoom: { enabled: true, type: "x" } },
      plotOptions: { bar: { horizontal: true, rangeBarGroupRows: true, barHeight: "70%" } },
      xaxis: { type: "datetime", labels: { datetimeUTC: false, format: "HH:mm" }},
      dataLabels: {
        enabled: true,
        formatter: v => Math.round((v[1] - v[0]) / 60000) + " min"
      },
      tooltip: {
          x: {
            format: "yyyy‑MM‑dd HH:mm:ss"
          }
        },
      legend: { position: "top", horizontalAlign: "left" },
      colors: ["#4285F4", "#EA4335", "#FBBC05"],
      series: FULL_SERIES
    };

    const chart = new ApexCharts(document.querySelector("#profile_timeline"), options);
    chart.render();

    const selectEl = document.getElementById("profile_select");

    const filterSeries = () => {
      const chosen = Array.from(selectEl.selectedOptions).map(o => o.value);

      const filtered = FULL_SERIES.map(s => ({
        name: s.name,
        data: chosen.length === 0
          ? s.data
          : s.data.filter(d => chosen.some(id => d.x.endsWith(id)))
      }));

      chart.updateSeries(filtered);
    };

    selectEl.addEventListener("change", filterSeries);
    filterSeries();
  });
</script>
