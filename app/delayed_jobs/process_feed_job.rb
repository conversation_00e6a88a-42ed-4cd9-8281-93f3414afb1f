class ProcessFeedJob
  attr_reader :user_profile_id, :options
  def initialize(user_profile_id, opts = {})
    @user_profile_id = user_profile_id
    @options = opts
  end

  def enqueue(job)
    job.update(user_profile_id: @user_profile_id)
  end

  def perform
    user_profile = UserProfile.find_by(id: @user_profile_id)
    user_profile.adjustment(options)
  rescue ActiveRecord::RecordNotFound
    Rails.logger.error("not found user profile #{@user_profile_id}")
    nil
  end

  def after(job)
  end
end
