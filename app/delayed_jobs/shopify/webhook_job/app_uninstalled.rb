class Shopify::WebhookJob::AppUninstalled
  def initialize(data, options)
    @data = data
    @options = options
  end

  def perform
    @data = JSON.parse(@data)
    if @data.present? && @options[:provider] == "shopify"
      user = User.where(shopify_domain: @data["myshopify_domain"], provider: "shopify").limit(1).first
      user.process_uninstall_event
      true
    else
      false
    end
  rescue => e
    Airbrake.notify(e, message: "Shopify::WebhookJob::AppUninstalled error: #{e.message}", args: {data: @data, options: @options})
    false
  end

  def queue_name
    "events"
  end

  def after(job)
    # https://github.com/collectiveidea/delayed_job/wiki/How-to-release-memory-after-jobs-completed-%3F
    Delayed::Worker.logger.info "RevertJob: #{Process.pid} process_ver: #{@deploy_version} now_ver: #{DeployInformation.instance.get_latest_commit}"
    if !@deploy_version.nil? && @deploy_version != DeployInformation.instance.get_latest_commit
      DeployInformation.kill_process(Process.pid)
    end
  end
end
