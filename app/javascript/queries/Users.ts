import { gql } from 'urql';
import { z } from 'zod';

import type { PricingPlan, SalesChannel } from '@/types';

interface GetSingleProductType {
  data: {
    product: {
      category: string | null;
      createdAt: string;
      collections: {
        nodes: any[];
      };
      description: string;
      onlineStorePreviewUrl: string;
      id: string;
      title: string;
      vendor: string;
      productType: string;
      handle: string;
      updatedAt: string;
      publishedAt: string;
      tags: string[];
      templateSuffix: string | null;
      tracksInventory: boolean;
      totalInventory: number;
      variantsCount: {
        count: number;
        precision: string;
      };
      status: string;
      seo: {
        description: string | null;
        title: string | null;
      };
      metafieldDefinitions: {
        nodes: Array<{
          key: string;
          namespace: string;
          ownerType: 'PRODUCT';
          type: {
            category: string;
            name: string;
          };
        }>;
      };
      variants: {
        nodes: Array<{
          id: string;
          title: string;
          price: string;
          sku: string;
          position: number;
          inventoryPolicy: string;
          image: string | null;
          compareAtPrice: string | null;
          createdAt: string;
          updatedAt: string;
          taxable: boolean;
          barcode: string | null;
          inventoryItem: {
            id: string;
            countryCodeOfOrigin: string | null;
            harmonizedSystemCode: string | null;
            inventoryHistoryUrl: string;
            locationsCount: {
              count: number;
              precision: string;
            };
            tracked: boolean;
            requiresShipping: boolean;
            measurement: {
              weight: {
                unit: string;
                value: number;
              };
            };
            unitCost: string | null;
          };
          selectedOptions: Array<{
            name: string;
            value: string;
          }>;
          inventoryQuantity: number;
        }>;
      };
      resourcePublicationsV2: {
        nodes: Array<{
          isPublished: boolean;
          publication: {
            id: string;
            app: {
              handle: string;
            };
            name: string;
          };
        }>;
      };
      options: Array<{
        id: string;
        name: string;
        position: number;
        values: string[];
      }>;
      media: {
        nodes: any[];
        preview: {
          image: {
            url: string;
          };
        };
      };
      locations: any;
      lowStockLevel: number;
    } | null;
  };
}

export const GetSingleProduct = gql<{
  getSingleProduct: GetSingleProductType;
}>`
  query GetSingleProduct($id: String, $vid: String) {
    getSingleProduct(id: $id, vid: $vid)
  }
`;

export const GetProductChanges = gql`
  query GetProductChanges(
    $size: Int
    $page: Int
    $productId: String
    $variantId: String
  ) {
    getProductChanges(
      size: $size
      page: $page
      productId: $productId
      variantId: $variantId
    )
  }
`;

export const GetGoogleShoppingCategories = gql`
  query GetGoogleShoppingCategories {
    getGoogleShoppingCategories
  }
`;

type MetafieldDefinition = {
  key: string;
  name: string;
  namespace: string;
  ownerType: 'PRODUCT' | 'PRODUCTVARIANT';
  type: {
    category: 'TEXT';
    name:
      | 'multi_line_text_field'
      | 'single_line_text_field'
      | 'list.single_line_text_field';
  };
};

export const GetMetafieldDefinitions = gql<{
  getMetafieldDefinitions: {
    product: MetafieldDefinition[];
    variant: MetafieldDefinition[];
  };
}>`
  query GetMetafieldDefinitions {
    getMetafieldDefinitions
  }
`;

export const GetMatchedMetafields = gql<{
  getMatchedMetafields: MetafieldDefinition[];
}>`
  query GetMatchedMetafields($publicToken: String) {
    getMatchedMetafields(publicToken: $publicToken)
  }
`;

export const UpdatePaymentInfo = gql<{
  updatePaymentInfo: {
    data: string;
    error: string;
  };
}>`
  query UpdatePaymentInfo {
    updatePaymentInfo
  }
`;

export const CreateFlexiCheckout = gql`
  mutation CreateFlexiCheckout($importLimit: Int!) {
    createFlexiCheckout(importLimit: $importLimit) {
      status
      error
      log
    }
  }
`;

export const CreateShopifyCheckout = gql`
  mutation CreateShopifyCheckout($planName: String!, $isYearly: Boolean) {
    createShopifyCheckout(planName: $planName, isYearly: $isYearly) {
      status
      error
      log
    }
  }
`;

export const CreateCustomShopifyCheckout = gql`
  mutation CreateCustomShopifyCheckout(
    $price: Float!
    $variantLimit: Int!
    $profileLimit: Int!
    $scheduleLimit: Int!
    $annually: Boolean
  ) {
    createCustomShopifyCheckout(
      price: $price
      variantLimit: $variantLimit
      profileLimit: $profileLimit
      scheduleLimit: $scheduleLimit
      annually: $annually
    ) {
      status
      error
      log
    }
  }
`;

export const CreateWixCheckout = gql<
  string,
  {
    planName: PricingPlan['key'];
    isYearly: boolean;
  }
>`
  mutation CreateWixCheckout($planName: String!, $isYearly: Boolean) {
    createWixCheckout(planName: $planName, isYearly: $isYearly) {
      status
      error
      log
    }
  }
`;

export const CreateStripeCheckout = gql`
  mutation CreateStripeCheckout($planName: String!, $isYearly: Boolean) {
    createStripeCheckout(planName: $planName, isYearly: $isYearly) {
      status
      error
      log
    }
  }
`;

export const CreateStripeLinkForCredits = gql`
  mutation CreateStripeLinkForCredits($totalCredits: Int!) {
    createStripeLinkForCredits(totalCredits: $totalCredits) {
      status
      error
      log
    }
  }
`;

export const CreateCustomStripeCheckoutLink = gql`
  mutation CreateCustomStripeCheckoutLink(
    $price: Float!
    $variantLimit: Int!
    $profileLimit: Int!
    $scheduleLimit: Int!
    $annually: Boolean
  ) {
    createCustomStripeCheckoutLink(
      price: $price
      variantLimit: $variantLimit
      profileLimit: $profileLimit
      scheduleLimit: $scheduleLimit
      annually: $annually
    ) {
      status
      error
      log
    }
  }
`;

export const GetStoresByPublicToken = gql<{
  queryStoresByPublicToken;
}>`
  query GetStoresByPublicToken {
    queryStoresByPublicToken
  }
`;

export const UnresolvedConversationsCount = gql<{
  unresolvedConversationsCount: number;
}>`
  query UnresolvedConversationsCount {
    unresolvedConversationsCount
  }
`;

export const GetSalesChannels = gql<{
  getSalesChannels: Array<SalesChannel>;
}>`
  query GetSalesChannels {
    getSalesChannels
  }
`;

export const productCountsSchema = z.object({
  filtered: z.string(),
  total: z.string(),
  precision: z.union([z.literal('exact'), z.literal('at_least')]),
});

export type ProductCounts = z.infer<typeof productCountsSchema>;

export const GetProductCounts = gql<{
  getProductCounts: ProductCounts;
}>`
  query GetProductCounts($feedId: Int!, $storeFilters: JSON) {
    getProductCounts(feedId: $feedId, storeFilters: $storeFilters)
  }
`;

interface Categories {
  id: string;
  name: string;
}

export const GetCategories = gql<{
  getCategories: Array<Categories>;
}>`
  query GetCategories {
    getCategories
  }
`;

export const GetVendors = gql<{
  getVendors: Array<string>;
}>`
  query GetVendors {
    getVendors
  }
`;
export const GetProductTypes = gql<{ getProductTypes: Array<string> }>`
  query GetProductTypes {
    getProductTypes
  }
`;
