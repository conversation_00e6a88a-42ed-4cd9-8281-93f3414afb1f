import { gql } from 'urql';

import * as fragments from '@/queries/fragments';
import type { ActivityLog, UserProfile } from '@/types';

export const QueryChangeLog = gql<{
  queryChangeLog: {
    total_products: number;
    data: Array<{
      'After Qty': string;
      'Image Src': string;
      Remark: string;
      SKU: string;
      Title: string;
      'Variant ID': string;
    }>;
  };
}>`
  query QueryChangeLog($limit: Int, $feedId: Int!) {
    queryChangeLog(limit: $limit, feedId: $feedId)
  }
`;

export const QueryByKeyword = gql<{
  queryByKeyword: Array<{
    'After Qty': string;
    'Image Src': string;
    Remark: string;
    SKU: string;
    Title: string;
    'Variant ID': string;
  }>;
}>`
  query QueryByKeyword($keyword: String, $limit: Int, $feedId: Int!) {
    queryByKeyword(keyword: $keyword, limit: $limit, feedId: $feedId)
  }
`;

const authorizationUrls = [
  'airtable',
  'aliexpress',
  'bling',
  'box',
  'ebay_v2',
  'ebay',
  'etsy',
  'google_shopping',
  'google',
  'one_drive_file',
  'one_drive',
  'onshopfront',
  'quickbooks',
  'tiktokshop',
  'vend',
  'woocommerce',
  'xero',
  'zoho_inventory',
] as const;

export type AuthorizationUrl = (typeof authorizationUrls)[number];

export const GetAuthorizationUrls = gql<{
  getAuthorizationUrls: {
    authorization_urls: Record<AuthorizationUrl, string>;
    disconnect_urls: Record<AuthorizationUrl, string>;
  };
}>`
  query GetAuthorizationUrls($feedId: Int!) {
    getAuthorizationUrls(feedId: $feedId)
  }
`;

export const GetActivityLogs = gql<{
  getActivityLogs: {
    hasMore: boolean;
    logs: ActivityLog[];
  };
}>`
  query GetActivityLogs($feedId: Int!, $status: Boolean, $page: Int!) {
    getActivityLogs(feedId: $feedId, status: $status, page: $page) {
      hasMore
      logs {
        __typename
        ...ProductLog
      }
    }
  }
  ${fragments.ProductLog}
`;

// * * mutations * * //

export const CreateNewFeed = gql`
  ${fragments.UserProfile}
  mutation CreateNewFeed(
    $feedType: String!
    $feedName: String
    $supplierId: String
  ) {
    createNewFeed(
      feedType: $feedType
      feedName: $feedName
      supplierId: $supplierId
    ) {
      feed {
        __typename
        ...UserProfile
      }
      status
      error
    }
  }
`;

export const UpdateFeedName = gql`
  mutation UpdateFeedName($profileName: String!, $feedId: Int!) {
    updateFeedName(profileName: $profileName, feedId: $feedId) {
      status
      errors
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const UpdatePreviewMode = gql`
  mutation UpdatePreviewMode($preview: Boolean!, $feedId: Int!) {
    updatePreviewMode(preview: $preview, feedId: $feedId) {
      status
      errors
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const DuplicateFeed = gql`
  mutation DuplicateFeed($feedId: Int!, $convertToUpdateFeed: Boolean!) {
    duplicateFeed(feedId: $feedId, convertToUpdateFeed: $convertToUpdateFeed) {
      feed {
        __typename
        ...UserProfile
      }
      status
      error
    }
  }
  ${fragments.UserProfile}
`;

export const UpdateSchedule = gql`
  mutation UpdateSchedule($attributes: ScheduleInput!, $feedId: Int!) {
    updateSchedule(attributes: $attributes, feedId: $feedId) {
      status
      error
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const ToggleScheduler = gql`
  mutation ToggleScheduler($feedId: Int!) {
    toggleScheduler(feedId: $feedId) {
      status
      errors
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const ExecuteFeed = gql`
  mutation ExecuteFeed(
    $feedId: Int!
    $preview: Boolean!
    $forceStart: Boolean
  ) {
    runNow(feedId: $feedId, preview: $preview, forceStart: $forceStart) {
      status
      error
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const CancelProcess = gql`
  mutation CancelProcess($feedId: Int!) {
    cancelProcess(feedId: $feedId) {
      status
      error
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const DeleteFeed = gql`
  mutation DeleteFeed($feedId: Int!) {
    deleteFeed(feedId: $feedId) {
      status
      error
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const TogglePin = gql`
  mutation TogglePin($feedId: Int!, $toPin: Boolean!) {
    togglePin(feedId: $feedId, toPin: $toPin) {
      feed {
        __typename
        ...UserProfile
      }
      status
      error
    }
  }
  ${fragments.UserProfile}
`;

export const ValidatePricingConditions = gql`
  query ValidatePricingConditions($feedId: Int!, $pricingCondition: JSON!) {
    validatePricingConditions(
      feedId: $feedId
      pricingCondition: $pricingCondition
    )
  }
`;

export const RunNowSample = gql`
  mutation RunNowSample($feedId: Int!) {
    runNowSample(feedId: $feedId) {
      status
      error
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const UploadFile = gql`
  mutation UploadFile(
    $feedId: Int!
    $processNow: Boolean!
    $file: Upload!
    $runAsSample: Boolean!
  ) {
    uploadFile(
      feedId: $feedId
      processNow: $processNow
      file: $file
      runAsSample: $runAsSample
    ) {
      status
      error
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export interface UpdateFeedSourceVariables
  extends Partial<Omit<UserProfile, 'id' | 'callParams' | 'headerParams'>> {
  feedId: number;
  callParams?: string;
  headerParams?: string;
}

type UpdateFeedSourceData = any; // TODO:

export const UpdateFeedSource = gql<
  UpdateFeedSourceData,
  UpdateFeedSourceVariables
>`
  mutation UpdateFeedSource(
    $feedId: Int!
    $combinedSourceType: String
    $accName: String
    $password: String
    $sourceUrl: String
    $oneDriveShareUrl: String
    $fileFormat: String
    $parentNode: String
    $variantNode: String
    $hasHeader: Boolean
    $autoFileSettings: Boolean
    $pathToFile: String
    $exportEmail: String
    $sshKey: String
    $sourceAuth: Boolean
    $sheetName: String
    $namespaceIdentifier: String
    $httpMethod: String
    $callParams: String
    $etsyAgree: Boolean
    $ftpRename: Int
    $customFileName: String
    $ftpMode: String
    $fileEncoding: String
    $headerParams: String
    $bodyRaw: String
    $colSep: String
    $s3AccessKeyId: String
    $s3SecretAccessKey: String
    $s3BucketName: String
    $customLoginField: String
    $customLoginPasswordField: String
    $clearGoogleSheet: Boolean
    $googleOutInsert: Boolean
    $unleashedApiId: String
    $unleashedApiKey: String
    $aliexpressAppId: String
    $aliexpressAppSecret: String
    $ftpWhitelist: Boolean
    $googleShoppingCategory: String
    $productKeySeparator: String
    $woocommerceApiVersion: String
    $woocommerceConsumerKey: String
    $woocommerceConsumerSecret: String
    $walmartClientId: String
    $walmartClientSecret: String
    $authType: String
    $connectionSettings: JSON
    $skipTotalRows: Int
    $publicFile: Boolean
    $fileName: String
  ) {
    updateFeedSource(
      feedId: $feedId
      combinedSourceType: $combinedSourceType
      accName: $accName
      password: $password
      sourceUrl: $sourceUrl
      oneDriveShareUrl: $oneDriveShareUrl
      fileFormat: $fileFormat
      parentNode: $parentNode
      variantNode: $variantNode
      hasHeader: $hasHeader
      autoFileSettings: $autoFileSettings
      pathToFile: $pathToFile
      exportEmail: $exportEmail
      sshKey: $sshKey
      sourceAuth: $sourceAuth
      sheetName: $sheetName
      namespaceIdentifier: $namespaceIdentifier
      httpMethod: $httpMethod
      callParams: $callParams
      etsyAgree: $etsyAgree
      ftpRename: $ftpRename
      customFileName: $customFileName
      fileName: $fileName
      ftpMode: $ftpMode
      fileEncoding: $fileEncoding
      headerParams: $headerParams
      bodyRaw: $bodyRaw
      colSep: $colSep
      s3AccessKeyId: $s3AccessKeyId
      s3SecretAccessKey: $s3SecretAccessKey
      s3BucketName: $s3BucketName
      customLoginField: $customLoginField
      customLoginPasswordField: $customLoginPasswordField
      clearGoogleSheet: $clearGoogleSheet
      googleOutInsert: $googleOutInsert
      unleashedApiId: $unleashedApiId
      unleashedApiKey: $unleashedApiKey
      aliexpressAppId: $aliexpressAppId
      aliexpressAppSecret: $aliexpressAppSecret
      ftpWhitelist: $ftpWhitelist
      googleShoppingCategory: $googleShoppingCategory
      productKeySeparator: $productKeySeparator
      woocommerceApiVersion: $woocommerceApiVersion
      woocommerceConsumerKey: $woocommerceConsumerKey
      woocommerceConsumerSecret: $woocommerceConsumerSecret
      walmartClientId: $walmartClientId
      walmartClientSecret: $walmartClientSecret
      authType: $authType
      connectionSettings: $connectionSettings
      skipTotalRows: $skipTotalRows
      publicFile: $publicFile
    ) {
      status
      errors
      feed {
        __typename
        ...UserProfile
      }
    }
  }
  ${fragments.UserProfile}
`;

export const UpdateFeedSettings = gql`
  mutation UpdateFeedSettings(
    $feedId: Int!
    $shopifyProductKey: String
    $shopifyVariantKey: String
    $storePrefix: String
    $prefix: String
    $postfix: String
    $caseSensitive: Boolean
    $syncFieldsAttributes: [JSON!]
    $syncFieldSettings: [JSON!]
    $vendorFilter: String
    $excludeVendors: String
    $excludeProductTypes: String
    $excludeTags: String
    $excludeCollections: String
    $includeSkus: String
    $excludeSkus: String
    $includeTags: String
    $isAutoGenerateMetafieldDefinitions: Boolean
    $productTypeFilter: String
    $autoResetQuantity: Boolean
    $hideUnmatchProducts: Boolean
    $ignoreDontTrackInventory: Boolean
    $ignoreZeroQuantity: Boolean
    $updateDuplicateProductKey: Boolean
    $publishedApplyToAll: Boolean
    $skipImportWithZeroQty: Boolean
    $variantImageLink: Boolean
    $assignVariantsToFirstImage: Boolean
    $unpublishedApplyToAll: Boolean
    $shopifyTrackInventory: Boolean
    $publishedApplyMatchingProducts: Boolean
    $unpublishedApplyMatchingProducts: Boolean
    $lowStockLevel: Int
    $locationId: String
    $locationName: String
    $hasHeader: Boolean
    $importSort: Boolean
    $shopifyInventoryManagement: String
    $publishedFilter: String
    $forceOverrideCompareAtPrice: Boolean
    $importTags: String
    $filterParams: JSON
    $productKeySeparator: String
    $deleteMode: Int
    $partialMatch: Boolean
    $bigcommerceRetainAvailability: Boolean
    $updateProductLevel: Boolean
    $removeProductWhenAllLocationsNil: Boolean
    $bypassBlankRow: Boolean
    $storeFilters: [JSON!]
    $feedFilters: [JSON!]
    $metafields: [JSON!]
    $productIdentifierSyncField: JSON
    $productOptionsSyncField: [JSON!]
    $woocommerceProductAttributes: [JSON!]
    $salesChannelIds: [String!]
    $extraOptions: JSON
  ) {
    updateFeedSettings(
      feedId: $feedId
      shopifyProductKey: $shopifyProductKey
      shopifyVariantKey: $shopifyVariantKey
      storePrefix: $storePrefix
      prefix: $prefix
      postfix: $postfix
      caseSensitive: $caseSensitive
      syncFieldsAttributes: $syncFieldsAttributes
      syncFieldSettings: $syncFieldSettings
      vendorFilter: $vendorFilter
      excludeVendors: $excludeVendors
      excludeProductTypes: $excludeProductTypes
      excludeTags: $excludeTags
      excludeCollections: $excludeCollections
      includeSkus: $includeSkus
      excludeSkus: $excludeSkus
      includeTags: $includeTags
      isAutoGenerateMetafieldDefinitions: $isAutoGenerateMetafieldDefinitions
      productTypeFilter: $productTypeFilter
      autoResetQuantity: $autoResetQuantity
      hideUnmatchProducts: $hideUnmatchProducts
      ignoreDontTrackInventory: $ignoreDontTrackInventory
      ignoreZeroQuantity: $ignoreZeroQuantity
      updateDuplicateProductKey: $updateDuplicateProductKey
      publishedApplyToAll: $publishedApplyToAll
      skipImportWithZeroQty: $skipImportWithZeroQty
      variantImageLink: $variantImageLink
      assignVariantsToFirstImage: $assignVariantsToFirstImage
      unpublishedApplyToAll: $unpublishedApplyToAll
      shopifyTrackInventory: $shopifyTrackInventory
      publishedApplyMatchingProducts: $publishedApplyMatchingProducts
      unpublishedApplyMatchingProducts: $unpublishedApplyMatchingProducts
      lowStockLevel: $lowStockLevel
      locationId: $locationId
      locationName: $locationName
      hasHeader: $hasHeader
      importSort: $importSort
      shopifyInventoryManagement: $shopifyInventoryManagement
      publishedFilter: $publishedFilter
      forceOverrideCompareAtPrice: $forceOverrideCompareAtPrice
      productKeySeparator: $productKeySeparator
      deleteMode: $deleteMode
      partialMatch: $partialMatch
      bigcommerceRetainAvailability: $bigcommerceRetainAvailability
      updateProductLevel: $updateProductLevel
      removeProductWhenAllLocationsNil: $removeProductWhenAllLocationsNil
      bypassBlankRow: $bypassBlankRow
      importTags: $importTags
      filterParams: $filterParams
      storeFilters: $storeFilters
      feedFilters: $feedFilters
      metafields: $metafields
      productIdentifierSyncField: $productIdentifierSyncField
      productOptionsSyncField: $productOptionsSyncField
      woocommerceProductAttributes: $woocommerceProductAttributes
      salesChannelIds: $salesChannelIds
      extraOptions: $extraOptions
    ) {
      status
      errors
      feed {
        __typename
        ...SettingsUserProfile
      }
    }
  }
  ${fragments.SettingsUserProfile}
`;

export const TestConnection = gql`
  mutation TestConnection(
    $feedId: Int!
    $combinedSourceType: String
    $sourceUrl: String
    $pathToFile: String
    $accName: String
    $password: String
    $sourceAuth: Boolean
    $sshKey: String
    $s3SecretAccessKey: String
    $s3AccessKeyId: String
    $s3BucketName: String
    $woocommerceConsumerKey: String
    $woocommerceConsumerSecret: String
  ) {
    testConnection(
      feedId: $feedId
      combinedSourceType: $combinedSourceType
      sourceUrl: $sourceUrl
      pathToFile: $pathToFile
      accName: $accName
      password: $password
      sourceAuth: $sourceAuth
      sshKey: $sshKey
      s3SecretAccessKey: $s3SecretAccessKey
      s3AccessKeyId: $s3AccessKeyId
      s3BucketName: $s3BucketName
      woocommerceConsumerKey: $woocommerceConsumerKey
      woocommerceConsumerSecret: $woocommerceConsumerSecret
    ) {
      status
      errors
      message
    }
  }
`;

export const ResetFeed = gql`
  mutation ResetFeed(
    $feedId: Int!
    $supplierId: String!
    $removeExistingMapping: Boolean
  ) {
    resetFeed(
      feedId: $feedId
      supplierId: $supplierId
      removeExistingMapping: $removeExistingMapping
    ) {
      feed {
        __typename
        ...UserProfile
      }
      status
      error
    }
  }
  ${fragments.UserProfile}
`;

export const ResetToTemplate = gql`
  mutation ResetToTemplate($feedId: Int!) {
    resetToTemplate(feedId: $feedId) {
      feed {
        __typename
        ...UserProfile
      }
      status
      error
    }
  }
  ${fragments.UserProfile}
`;

export const ReadSampleData = gql<{
  readSampleData: Array<{ key: string; value: string }>;
}>`
  query ReadSampleData($feedId: Int!, $retries: Int) {
    readSampleData(feedId: $feedId, retries: $retries)
  }
`;
export const FieldMappingPrediction = gql<{
  fieldMappingPrediction: Array<{ column: string; field: string }>;
}>`
  query FieldMappingPrediction($feedId: Int!, $retries: Int) {
    fieldMappingPrediction(feedId: $feedId, retries: $retries)
  }
`;

export const UndeleteFeed = gql`
  mutation UndeleteFeed($feedId: Int!) {
    undeleteFeed(feedId: $feedId) {
      feed {
        __typename
        ...UserProfile
      }
      status
      error
    }
  }
  ${fragments.UserProfile}
`;

export const CreateRemoveFeed = gql`
  mutation CreateRemoveFeed($feedId: Int!) {
    createRemoveFeed(feedId: $feedId) {
      feed {
        __typename
        ...UserProfile
      }
      updateFeed {
        __typename
        id
        autoClearNotInFeedCreated
      }
      status
      error
    }
  }
  ${fragments.UserProfile}
`;

export const CopyFeedByPublicToken = gql`
  mutation CopyFeedByPublicToken($publicToken: String!, $feedId: Int!) {
    copyFeedByPublicToken(publicToken: $publicToken, feedId: $feedId) {
      status
      error
    }
  }
`;
