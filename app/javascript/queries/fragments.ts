import { gql } from 'urql';

export const Plan = gql`
  fragment Plan on Plan {
    hidden
    id
    importLimit
    importSourceLimit
    isCustomPlan
    key
    limit
    minHour
    monthlyCreditLoad
    monthlyCreditPlan
    popular
    price
    schedule
    schedulePerDay
    sourceLimit
    version
  }
`;

export const Supplier = gql`
  fragment Supplier on Supplier {
    categories
    countryCode
    description
    email
    id
    imageUrl
    name
    notes
    popular
    redirectTo
    supplierType
    url
  }
`;

export const ProductLog = gql`
  fragment ProductLog on ProductLog {
    actualProductUpdated
    actualProductRetryUpdated
    cache
    createdAt
    downloadLinks
    errorList
    extraParams
    mergeTagUsed
    importTagUsed
    fileName
    id
    isRevertingImport
    numberOfProductHidden
    numberOfProductPublished
    numberProductUpdated
    numberProductUpdateFailed
    partial
    percentageMatched
    remainingSkusToProcess
    remark
    remarkValues
    resumed
    status
    timeTaken
    totalFeedFilteredRows
    totalFeedRows
    totalLeftOverIds
    totalOutOfStock
    totalStoreSkus
    triggerBy
    undoAt
    updatedAt
    endTime
  }
`;
//

export const SettingsUserProfile = gql`
  fragment SettingsUserProfile on UserProfile {
    autoFileSettings
    accName
    activityLogsUrl
    aliexpressAccessToken
    aliexpressAppId
    aliexpressAppSecret
    assignVariantsToFirstImage
    authType
    autoResetQuantity
    autoTagging
    bigcommerceRetainAvailability
    bodyRaw
    boxAccessToken
    boxRefreshToken
    bulkLoading
    bypassBlankRow
    callParams
    caseSensitive
    clearGoogleSheet
    collectionFilterTitle
    colSep
    combinedSourceType
    connectionSettings
    createdAt
    customFileName
    customLoginField
    customLoginPasswordField
    decryptedPassword
    deleteMode
    detectedFileFormat
    ebayAuthToken
    ebaySessionId
    email
    enabled
    etsySecret
    etsyToken
    excludeBpn
    excludeCollections
    excludeProductTypes
    excludeSkus
    excludeTags
    excludeVendors
    exportEmail
    extraOptions
    fastTrack
    fastTrackDownloadLink
    feedFileLocation
    feedFilters
    feedType
    feedType
    fileEncoding
    fileFormat
    fileName
    filterParams
    forceOverrideCompareAtPrice
    formattedJobTimezone
    formattedJobType
    frequencyTime
    fromTemplate
    ftpRename
    ftpWhitelist
    googleOutInsert
    googleShoppingCategory
    hasFilter
    hasHeader
    headerParams
    hideUnmatchProducts
    hourlyEndTime
    hourlyStartTime
    httpMethod
    humanizeFeedType
    id
    ignoreDontTrackInventory
    ignoreZeroQuantity
    importSort
    importTags
    includeBpn
    includeSkus
    includeTags
    ioMode
    isDeleted
    isAutoGenerateMetafieldDefinitions
    jobInterval
    jobTime
    jobType
    lastEmailCreatedAt
    locationId
    locationName
    lowStockLevel
    metafields
    namespaceIdentifier
    notInFeedUrl
    oauthGranted
    oauthGrantedTo
    oneDriveRefreshToken
    oneDriveShareUrl
    oneDriveToken
    origStatus
    parentNode
    partialMatch
    pathToFile
    pinned
    postfix
    prefix
    preview
    processingNo
    productIdentifier
    productIdentifierSyncField
    productKeySeparator
    productKeySeparator
    productOptionsSyncField
    productTypeFilter
    profileName
    profileStatus
    progress
    publicFile
    publishedApplyMatchingProducts
    publishedApplyToAll
    publishedFilter
    quickbooksAccessToken
    quickbooksRealmId
    quickbooksRefreshToken
    receivedEmail
    removeProductWhenAllLocationsNil
    removeProfileId
    s3AccessKeyId
    s3BucketName
    s3SecretAccessKey
    salesChannelIds
    schedulerEnabled
    sheetName
    shopifyInventoryManagement
    shopifyProductKey
    shopifyTrackInventory
    skipImportWithZeroQty
    skipTotalRows
    sourceAuth
    sourceFileFileName
    sourceType
    sourceUrl
    sshKey
    status
    storeFilters
    storePrefix
    supplier {
      __typename
      ...Supplier
    }
    syncFieldSettings
    unleashedApiId
    unleashedApiKey
    unpublishedApplyMatchingProducts
    unpublishedApplyToAll
    updatedAt
    updateDuplicateProductKey
    updateProductLevel
    useTimeRange
    variantImageLink
    variantNode
    vendAccessToken
    vendDomainPrefix
    vendorFilter
    vendRefreshToken
    walmartAccessToken
    walmartClientId
    walmartClientSecret
    runWebhookMode
    woocommerceApiVersion
    woocommerceConsumerKey
    woocommerceConsumerSecret
    xeroAccessToken
    xeroClientId
    xeroClientSecret
    xeroRefreshToken
    xeroTenantId
    zeroQty
    woocommerceProductAttributes
  }
  ${Supplier}
`;

export const UserProfile = gql`
  fragment UserProfile on UserProfile {
    accName
    activityLogs {
      __typename
      ...ProductLog
    }
    activityLogsUrl
    aliexpressAccessToken
    aliexpressAppId
    aliexpressAppId
    aliexpressAppSecret
    aliexpressAppSecret
    assignVariantsToFirstImage
    authType
    autoClearNotInFeedCreated
    autoResetQuantity
    autoTagging
    autoFileSettings
    bigcommerceRetainAvailability
    bodyRaw
    boxAccessToken
    boxRefreshToken
    bulkLoading
    bypassBlankRow
    callParams
    caseSensitive
    clearGoogleSheet
    collectionFilterTitle
    colSep
    combinedSourceType
    connectionSettings
    createdAt
    customFileName
    customLoginField
    customLoginPasswordField
    decryptedPassword
    deleteMode
    detectedFileFormat
    ebayAuthToken
    ebaySessionId
    email
    enabled
    etsySecret
    etsyToken
    excludeBpn
    excludeCollections
    excludeProductTypes
    excludeSkus
    excludeTags
    excludeVendors
    extraOptions
    exportEmail
    fastTrack
    fastTrackDownloadLink
    feedFileLocation
    feedFilters
    feedType
    feedType
    fileEncoding
    fileFormat
    fileName
    filterParams
    forceOverrideCompareAtPrice
    formattedJobTimezone
    formattedJobType
    frequencyTime
    fromTemplate
    ftpRename
    ftpWhitelist
    googleOutInsert
    googleShoppingCategory
    hasFilter
    hasHeader
    headerParams
    hideUnmatchProducts
    hourlyEndTime
    hourlyStartTime
    httpMethod
    humanizeFeedType
    id
    ignoreDontTrackInventory
    ignoreZeroQuantity
    importSort
    importTags
    includeBpn
    includeSkus
    includeTags
    ioMode
    isDeleted
    isCanceling
    isAutoGenerateMetafieldDefinitions

    jobInterval
    jobTime
    jobType
    lastEmailCreatedAt
    locationId
    locationName
    lowStockLevel
    lowStockLevel
    metafields
    namespaceIdentifier
    notInFeedUrl
    oauthGranted
    oauthGrantedTo
    oneDriveRefreshToken
    oneDriveShareUrl
    oneDriveShareUrl
    oneDriveToken
    origStatus
    parentNode
    partialMatch
    pathToFile
    pinned
    postfix
    prefix
    preview
    processingNo
    productIdentifier
    productIdentifierSyncField
    productKeySeparator
    productKeySeparator
    productOptionsSyncField
    productTypeFilter
    profileName
    profileStatus
    progress
    publicFile
    publishedApplyMatchingProducts
    publishedApplyToAll
    publishedFilter
    quickbooksAccessToken
    quickbooksRealmId
    quickbooksRefreshToken
    receivedEmail
    removeProductWhenAllLocationsNil
    removeProfileId
    s3AccessKeyId
    s3BucketName
    s3SecretAccessKey
    salesChannelIds
    schedulerEnabled
    sheetName
    shopifyInventoryManagement
    shopifyProductKey
    shopifyTrackInventory
    skipImportWithZeroQty
    skipTotalRows
    sourceAuth
    sourceFileFileName
    sourceType
    sourceUrl
    sshKey
    status
    storeFilters
    storePrefix
    supplier {
      __typename
      ...Supplier
    }
    syncFieldSettings
    unleashedApiId
    unleashedApiId
    unleashedApiKey
    unleashedApiKey
    unpublishedApplyMatchingProducts
    unpublishedApplyToAll
    updatedAt
    updateDuplicateProductKey
    updateProductLevel
    useTimeRange
    variantImageLink
    variantNode
    vendAccessToken
    vendDomainPrefix
    vendorFilter
    vendRefreshToken
    walmartAccessToken
    walmartClientId
    walmartClientId
    walmartClientSecret
    walmartClientSecret
    runWebhookMode
    woocommerceApiVersion
    woocommerceConsumerKey
    woocommerceConsumerSecret
    xeroAccessToken
    xeroClientId
    xeroClientSecret
    xeroRefreshToken
    xeroTenantId
    zeroQty
    woocommerceProductAttributes
  }
  ${ProductLog}
  ${Supplier}
`;
