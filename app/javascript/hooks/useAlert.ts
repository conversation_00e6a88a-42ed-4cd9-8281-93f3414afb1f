import { atom, useAtom } from 'jotai';
import * as React from 'react';

const defaultAtom = atom('');

export function useAlert() {
  const [atomMessage, setAtomMessage] = useAtom(defaultAtom);

  React.useEffect(() => {
    if (!atomMessage) return;

    const interval = setTimeout(() => {
      setAtomMessage('');
    }, 8000);
    return () => {
      clearInterval(interval);
    };
  }, [atomMessage, setAtomMessage]);

  return {
    message: atomMessage,
    setMessage: (message: string) => setAtomMessage(message),
  };
}
