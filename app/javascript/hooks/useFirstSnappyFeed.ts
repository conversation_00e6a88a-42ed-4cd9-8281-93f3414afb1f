import { gql, useQuery } from 'urql';

import type { UserProfile } from '@/types';

import { useCurrentStore } from './useCurrentStore';

export interface FirstSnappyFeed
  extends Pick<UserProfile, 'id' | 'storeFilters' | 'profileName'> {}

export function useFirstSnappyFeed() {
  const { currentStore } = useCurrentStore();

  return useQuery({
    query: gql<{ firstSnappyFeed: FirstSnappyFeed }>`
      query FirstSnappyFeed {
        firstSnappyFeed {
          __typename
          id
          profileName
          storeFilters
        }
      }
    `,
    pause: currentStore.provider !== 'shopify',
  });
}
