import introJs from 'intro.js';
import type { IntroJs } from 'intro.js/src/intro';
import type { Options } from 'intro.js/src/option';
import * as React from 'react';

import 'intro.js/introjs.css';

interface UseIntroJsProps {
  /**
   * `Window.localeStorage` key name to scope hints.
   *
   * https://developer.mozilla.org/en-US/docs/Web/API/Storage
   */
  key: string;
  /**
   * <PERSON><PERSON><PERSON> toggle to show or hide hints.
   */
  enabled: boolean;
  /**
   * Hints as JSON.
   */
  hints?: Options['hints'];
  /**
   * Callback on closing hint.
   */
  onClose?: IntroJs['_hintCloseCallback'];
}

/**
 *
 * Show hints for all elements with `data-hint` attribute
 *
 * https://introjs.com/docs/hints/examples/hello-world
 *
 * @returns
 */
export function useIntroJs({ key, enabled, hints, onClose }: UseIntroJsProps) {
  const introRef = React.useRef<IntroJs | null>(null);
  React.useEffect(() => {
    if (!enabled) return;
    if (localStorage.getItem(key) === 'true') return;
    introRef.current = introJs();

    // We need to remove all hints otherwise new hints won't be added.
    // https://github.com/HiDeoo/intro.js-react/blob/main/src/components/Hints/index.js#L119
    introRef.current.removeHints();

    if (hints) {
      introRef.current.setOptions({ hints: hints });
      introRef.current.showHints();
      if (onClose) introRef.current.onhintclose(onClose);
    } else {
      introRef.current.addHints();
    }

    return () => {
      console.log(`hide hints:`, key); // TODO: rm
      introRef.current?.removeHints();
      introRef.current = null;
      localStorage.setItem(key, 'true');
    };
  }, [enabled, key, hints, onClose]);

  return;
}
