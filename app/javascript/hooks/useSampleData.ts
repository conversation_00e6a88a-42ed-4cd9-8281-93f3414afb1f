import { useQuery } from 'urql';
import * as React from 'react';

import { ReadSampleData } from '@/queries/FeedSettings';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import type { DefaultKeyValue } from '@/types';

export function useSampleData() {
  const { currentFeed } = useCurrentFeed();
  const interval = 3000;
  const maxPollingRetries = 6; //between 5 - 10

  const isObject = (value) => {
    return value !== null && typeof value === 'object';
  };

  const filterOutObjectType = (o: DefaultKeyValue) =>
    !isObject(o.key) && !isObject(o.value);

  const [result, reexecuteQuery] = useQuery({
    query: ReadSampleData,
    variables: {
      feedId: parseInt(currentFeed?.id ?? ''),
      retries: maxPollingRetries,
    },
  });

  React.useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    if (result?.data?.readSampleData.length === 0) {
      for (let i = 0; i <= maxPollingRetries; i++) {
        timers.push(
          setTimeout(() => {
            reexecuteQuery({ requestPolicy: 'network-only' });
          }, i * interval)
        );
      }
      return () => {
        timers.forEach((timerId) => clearTimeout(timerId));
      };
    }
  }, [reexecuteQuery, result?.data?.readSampleData.length]);

  return {
    data: (result?.data?.readSampleData ?? []).filter(filterOutObjectType),
    loading: result?.fetching,
  };
}
