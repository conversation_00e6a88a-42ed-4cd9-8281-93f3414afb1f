import groupBy from 'lodash/groupBy';
import { z } from 'zod';

// https://github.com/sindresorhus/type-fest/blob/main/source/primitive.d.ts
export type Primitive =
  | null
  | undefined
  | string
  | number
  | boolean
  | symbol
  | bigint;

// https://github.com/sindresorhus/type-fest/blob/main/source/literal-union.d.ts
export type LiteralUnion<LiteralType, BaseType extends Primitive> =
  | LiteralType
  | (BaseType & Record<never, never>);

export const fileFormats = [
  'Auto',
  'CsvFile',
  'Edi',
  'HtmlTable',
  'Json',
  'PlainText',
  'Tsv',
  'Xls',
  'Xlsx',
  'Xml',
] as const;
export type FileFormat = (typeof fileFormats)[number];
export const fileFormatEnums = z.enum(fileFormats);

export const httpMethods = ['get', 'put', 'post'] as const;
export type HttpMethod = (typeof httpMethods)[number];

interface MetaFieldAttributes {
  blank_val: string | null;
  create_key_if_not_found?: boolean;
  custom_field_key?: null;
  find: string | null;
  is_false_indicator: 'false' | 'true';
  is_true_indicator: 'true' | 'false';
  list_separator?: string;
  metafield_date_format: string | null;
  metafield_dimension_unit: 'cm';
  metafield_key: string | null;
  metafield_namespace: string | null;
  metafield_owner: 'product' | 'variant' | null;
  metafield_type:
    | 'boolean'
    | 'date_time'
    | 'date'
    | 'dimension'
    | 'list.dimension'
    | 'list.single_line_text_field'
    | 'multi_line_text_field'
    | 'single_line_text_field'
    | 'volume'
    | 'weight'
    | null;
  metafield_volume_unit: string;
  metafield_weight_unit: 'auto';
  original_language?: string;
  remove_if_blank?: boolean;
  replace: string | null;
  returned_language?: string;
  skip_if_blank?: boolean;
  static_flag: boolean;
  update_only_if_nil?: boolean;
}

export interface DefaultKeyValue {
  key: string;
  value: string;
}

export const emailSubscriptionKeys = [
  'daily_summary',
  'sync_success',
  'sync_failure',
  'low_stock_alert',
  'low_credit_alert',
  'back_in_stock_alert',
  'zero_qty_update_alert',
  'transactions',
  'newsletter',
  'milestone',
  'incoming_email_confirmation',
] as const;

const providers = [
  'shopify',
  'bigcommerce',
  'wix',
  'woocommerce',
  'squarespace',
  'ekm',
  'square',
  'quickbooks',
  'prestashop',
] as const;

export const jobTypes = [
  'daily',
  'friday',
  'minute_periodically',
  'monday',
  'mondaysaturday',
  'on_email_received',
  'on_file_uploaded',
  'periodically',
  'saturday',
  'sunday',
  'sundayfriday',
  'sundaythursday',
  'thursday',
  'tuesday',
  'tuesdaysaturday',
  'wednesday',
  'wednesdaysaturday',
  'wednesdaysunday',
  'weekdays',
  'weekends',
] as const;

export const storeSchema = z.object({
  emailSubscriptions: z.array(z.enum(emailSubscriptionKeys)),
  feedConstants: z.object({
    email_subscription_options: z.array(
      z.object({
        key: z.enum(emailSubscriptionKeys),
        value: z.enum(emailSubscriptionKeys),
      })
    ),
    job_type: z.array(
      z.object({
        key: z.enum(jobTypes),
        value: z.enum(jobTypes),
      })
    ),
  }),
  notificationEmail: z.union([
    z.literal(''),
    z
      .string()
      .refine(
        (value) =>
          value[0] !== '"' && !value.includes("'") && !value.includes('"'),
        'Kindly remove single or double quotes before saving'
      )
      .refine((value) => {
        if (!value) return true;
        const re =
          /^[\W]*([\w+\-.%]+@[\w\-.]+\.[A-Za-z]{2,4}[\W]*,{1}[\W]*)*([\w+\-.%]+@[\w\-.]+\.[A-Za-z]{2,4})[\W]*$/;
        return re.test(String(value).toLowerCase());
      }, 'Please enter a valid email address'),
  ]),
  provider: z.enum(providers),
  reachEmail: z.string(),
  shopifyDomain: z.string(),
  woocommerceKey: z
    .string()
    .catch('')
    .pipe(z.string().min(1, 'field_required')),
  woocommerceSecret: z
    .string()
    .catch('')
    .pipe(z.string().min(1, 'field_required')),
});

// TODO: dont export, only use in Store, replace Store later
type ZodStore = z.infer<typeof storeSchema>;

// TODO: old Store
export interface Store
  extends Pick<
    ZodStore,
    | 'emailSubscriptions'
    | 'notificationEmail'
    | 'provider'
    | 'reachEmail'
    | 'shopifyDomain'
    | 'woocommerceKey'
    | 'woocommerceSecret'
  > {
  allowImportFeed: boolean;
  autoGetHigherQty: boolean;
  bigcommerceDomain: string;
  chargePeriod: ChargePeriod;
  currentPlan: PricingPlan;
  enableTranslationFeature: boolean;
  feedConstants: {
    case_convert_options: Array<DefaultKeyValue>;
    column_separators: Array<DefaultKeyValue>;
    compare_at_price_round_options: Array<DefaultKeyValue>;
    cost_round_options: Array<DefaultKeyValue>;
    file_encoding: Array<DefaultKeyValue>;
    file_format: Array<{
      key: FileFormat;
      value: string;
    }>;
    file_rename_options: Array<{
      key: '0' | '1' | '2' | '3';
      value: string;
    }>;

    email_subscription_options: ZodStore['feedConstants']['email_subscription_options'];
    http_methods: Array<{
      key: HttpMethod;
      value: string;
    }>;
    image_col_sep_options: Array<DefaultKeyValue>;
    video_link_col_sep_options: Array<DefaultKeyValue>;
    import_product_identifiers: Array<DefaultKeyValue>;
    is_visible: Array<DefaultKeyValue>;
    job_type: ZodStore['feedConstants']['job_type'];
    metafield_owners: Array<DefaultKeyValue>;
    metafield_types: Array<DefaultKeyValue>;
    price_delimiter_options: Array<DefaultKeyValue>;
    price_round_options: Array<DefaultKeyValue>;
    published_status_filters: Array<DefaultKeyValue>;
    quantity_filters: Array<DefaultKeyValue>;
    rest_api_types: Array<{
      key: AuthType;
      value: AuthType;
    }>;
    rules_operators: Array<DefaultKeyValue>;
    rules_operators_with_keys_required: Array<DefaultKeyValue>;
    shopify_product_key: Array<DefaultKeyValue>;
    export_shopify_product_key: Array<DefaultKeyValue>;
    status_filters: Array<DefaultKeyValue>;
    quantity_delimiter_options: Array<DefaultKeyValue>;
    tags_col_sep_options: Array<DefaultKeyValue>;
    title_separator_options: Array<DefaultKeyValue>;
    weight_delimiter_options: Array<DefaultKeyValue>;
    delimiter_options: Array<DefaultKeyValue>;
    converter_options: Array<DefaultKeyValue>;
    weight_formula_options: Array<DefaultKeyValue>;
    weight_unit_options: Array<DefaultKeyValue>;
    wrap_tag_options: Array<DefaultKeyValue>;
    metafield_weight_unit_options: Array<DefaultKeyValue>;
    metafield_volume_unit_options: Array<DefaultKeyValue>;
    metafield_dimension_unit_options: Array<DefaultKeyValue>;
  };
  feedsCountByType: { import: number; update: number; remove: number };
  fullPlanName: string;
  hasSubscription: boolean;
  importLimitSkus: number;
  importMinHour: number;
  importProfileLimit: number;
  id: string;
  installedDate: string;
  isEmbedded: boolean;
  isExpired: boolean;
  isImpersonatingAdmin: boolean;
  isShowQuickGuide: boolean;
  latestPackageCharge: number;
  limitSkus: number;
  locale: string;
  locations: Array<{
    id: string;
    hasActiveInventory: boolean;
    isActive: boolean;
    isFulfillmentService: boolean;
    name: string;
  }>;
  monthlyCredit: string;
  monthlyCreditPlan: boolean;
  multiLocationEnabled: boolean | null;
  minHour: number;
  nextBillingDate: string;
  package: LiteralUnion<PricingPlan['key'], string>;
  profileLimit: number;
  planVersion: number;
  prePlanVersion: number;
  prestashopApiKey: string;
  profilesCount: number;
  publicToken: string;
  quickbooksRealmId: string | null;
  removeProfileLimit: number;
  status: string;
  shopifyStaff: boolean;
  stripeCustomerId: string;
  squareLocationId: string | null;
  bigcommerceStoreHash: string;
  schedulePerDay: number;
  syncFieldConstants: {
    metafield: MetaFieldAttributes;
  };
  timezone: string;
  trialExpired: boolean;
  totalProcess: number;
  totalSkusCount: number;
  warningRemoveProducts: number;
  warningZeroQtyUpdate: number;
  validWoocommerceStore: boolean;
}

export const combinedSourceTypes = [
  'aapd',
  'africa_altitude',
  'africa_amrod',
  'agis',
  'airtable_v2',
  'airtable',
  'aliexpress_in',
  'aliexpress_out',
  'aliexpress_scraper',
  'allure_lingerie',
  'alterego_csv',
  'alterego_xml',
  'amazon_in',
  'amazon_out',
  'amrod_v2',
  'amrod',
  'artisan_furniture',
  'ascolour',
  'asi',
  'atelier_api',
  'axiz',
  'azure_blob',
  'azure_green',
  'azure_sql_db',
  'backblaze_b2',
  'backmarket',
  'banggood',
  'banned_apparel',
  'beauty_gross',
  'beautyfort',
  'bems',
  'bewicked',
  'big_toys',
  'bigcommerce_feed',
  'bigcommerce_store',
  'billiet',
  'bling',
  'boards_and_more_v2',
  'boards_and_more',
  'box',
  'brands_distribution',
  'brightpoint_soap',
  'bsale_chile',
  'bts_wholesaler',
  'busyx',
  'captivity',
  'chattanooga_shooting',
  'cj',
  'clf',
  'clickhere2shop',
  'coasteramer',
  'combisteel',
  'controlport',
  'copper_bay_digital',
  'costway',
  'crawler',
  'custom_login',
  'daisy_corset',
  'daniel_smart_manufacturing',
  'dear_lover',
  'depasquale_salon_systems',
  'diamond_sky',
  'dicker_data',
  'digital_ocean',
  'dresscode_cloud',
  'dropbox_api',
  'dropbox',
  'dropship_clothes',
  'dropshipping_b2b_api',
  'dropshipping_b2b_v2',
  'dropshipping_b2b',
  'dropshipzone',
  'ebay_in',
  'ebay_out',
  'ebay_v2_in',
  'ebay_v2_out',
  'ebihr',
  'ecomdash',
  'efashion',
  'elegant_moments',
  'eldorado',
  'emag_marketplace',
  'email_in',
  'email_link',
  'email_out',
  'encompass',
  'engel_dropship',
  'erply',
  'etsy_in',
  'etsy_out',
  'fan_mats',
  'fast_furnishings',
  'feed_4akid_shopify_store',
  'feed',
  'feed4akid',
  'feed4apet',
  'ficeda',
  'files_api',
  'football_souvenir',
  'forge_motorsport',
  'fragrance_x',
  'fragrancenet',
  'ftp_houzz_out',
  'ftp_in',
  'ftp_multiple',
  'ftp_out',
  'ftps_multiple',
  'ftps',
  'ftpsimplicit',
  'fulfillrite',
  'fullfashion',
  'furnicher',
  'gaia_suite',
  'gateway_nwg',
  'giga_cloud',
  'global_craft',
  'google_drive_v3',
  'google_drive',
  'google_sheet_published',
  'google_shopping_in',
  'google_shopping_out',
  'google_spreadsheet_in',
  'google_spreadsheet_out',
  'green_dropship',
  'grupo_logi',
  'happy_nest',
  'heo_api',
  'heo_csv',
  'heo',
  'hill_interiors',
  'hlc',
  'hofman_animal_care',
  'honeys_place',
  'horizon_hobby',
  'hypercel',
  'hyundai_nl',
  'ingram_content_ftp',
  'ingram_content',
  'ingram_micro',
  'inque_style',
  'intcomex',
  'internet_bikes',
  'ital_trading',
  'jdm_products',
  'keno',
  'kerusso',
  'kevro',
  'killerdeal',
  'kitchway',
  'kitchway',
  'kleerance',
  'kotryna',
  'likewise_floors',
  'logsta',
  'magento',
  'malabs',
  'marashoes',
  'marathon_leisure',
  'maropost',
  'matas',
  'matterhorn',
  'maween_trading',
  'maxevan',
  'mclabels',
  'microsoft_sharepoint',
  'midocean',
  'mma_silver_data_feed',
  'mma_silver_retail_xml',
  'morris_costumes',
  'mstgolf',
  'mustek',
  'my_bertus',
  'mysale_api_out',
  'nae_vegan',
  'naturaldispensary',
  'net13',
  'new_temptations_clearance',
  'new_temptations',
  'nod',
  'novaengel',
  'nrdata',
  'one_drive_file',
  'one_on_one_wholesale',
  'one_style',
  'online_file',
  'onshopfront',
  'orso',
  'overnight_mountings',
  'parts_unlimited',
  'pet_dropshipper',
  'phorest',
  'pleaser',
  'premier_wd',
  'prenta',
  'project_verte',
  'prv',
  'puckator_api',
  'puckator_dropship',
  'qbp_v2',
  'qbp_pos',
  'qbp',
  'quickbooks',
  'rct_data_feed',
  'rekman',
  'rest_api',
  'restlet',
  'retail_edge',
  'richmond_interiors',
  'roma_costume',
  's3',
  'sanmar',
  'sap_business_one_sl_api',
  'schake',
  'seagull',
  'sex_toy_distributing_images',
  'sex_toy_distributing',
  'sftp_in',
  'sftp_multiple',
  'sftp_out',
  'sherco_network',
  'shipnetwork',
  'shopify_feed',
  'shopify_public',
  'shopify_self',
  'shopify_store',
  'shooting_warehouse_inventory',
  'siigo',
  'silverbene',
  'simpro',
  'skybound',
  'smiffys_api',
  'smiffys',
  'soap',
  'spm_network',
  'square_store',
  'ss_active_wear',
  'stileo_out',
  'storeden',
  'strawberry_net',
  'stricker_europe',
  'stuller',
  'sunkys',
  'svs_vetchannel',
  'swift_stock_flat',
  'swift_stock_nested',
  'swift_stock_removal',
  'synnex_ftp',
  'synnex',
  'sysco',
  'syscom',
  'talksky',
  'tarsus_distribution',
  'tiktokshop',
  'timco_api',
  'tme',
  'toptex',
  'tpl_central',
  'transamerican',
  'treasure_house',
  'trendcollection',
  'triple_xcessories',
  'tsigaridas_books',
  'turn14',
  'tuscany_leather',
  'twhouse',
  'uk_distributor',
  'uk_shopping_mall',
  'ulefone',
  'unleashed',
  'uploaded_file',
  'veloconnect',
  'vend',
  'verifone',
  'vidaxl',
  'vietti',
  'walmart_api_out',
  'wayfair_out',
  'western_fashion',
  'western_power_sports',
  'wholecell',
  'wholesalesportwear',
  'windsor',
  'wine_logistix',
  'wix_store',
  'woocommerce',
  'wos_action_sports',
  'xero',
  'xtrader',
  'zoho_inventory_out',
  'zoho_sheet',
] as const;

export const combinedSourceTypesEnums = z.enum(combinedSourceTypes);

export const authTypes = [
  'none',
  'basic_auth',
  'bearer_token',
  'bearer_token_body',
  'oauth_2',
  'oauth_2_with_password',
  'oauth_2_for_odoo',
  'token_path_body',
] as const;
export type AuthType = (typeof authTypes)[number];

export const authTypeEnums = z.enum(authTypes, {
  message: 'Invalid Authorization Type',
});

export const woocommerceApiVersions = ['v1', 'v2', 'v3'] as const;
export type WoocommerceApiVersion = (typeof woocommerceApiVersions)[number];

export type ActivityLog = {
  actualProductUpdated: number;
  actualProductRetryUpdated: number;
  cache: boolean;
  createdAt: string;
  downloadLinks: Record<string, string>;
  errorList: string | null;
  extraParams: {
    delete_mode: 1 | null;
    back_in_stock_count: number;
  } | null;
  fileName: string | null;
  mergeTagUsed: string | null;
  importTagUsed: string | null;
  id: string;
  isRevertingImport: boolean;
  numberOfProductHidden: number;
  numberOfProductPublished: number;
  numberProductUpdateFailed: number;
  numberProductUpdated: number;
  partial: boolean;
  percentageMatched: string;
  remainingSkusToProcess: number;
  remark: string | null;
  remarkValues:
    | {
        products_deleted: number;
        variants_deleted: number;
        warning_percentage: number;
        error: string;
      }
    | {
        products_imported: number;
        remaining: number;
      }
    | { product_skus_total: number; limit_skus: number }
    | string
    | Record<string, never>
    | null;
  resumed: boolean;
  status: boolean;
  timeTaken: string;
  totalFeedFilteredRows: number;
  totalFeedRows: number;
  totalLeftOverIds: number;
  totalOutOfStock: number;
  totalStoreSkus: number | null;
  triggerBy:
    | 'delay_system'
    | 'system'
    | 'user'
    | 'support'
    | 'system +10m'
    | 'memory upgrade'
    | 'admin'
    | null;
  undoAt: string | null;
  endTime?: string | null;
  updatedAt: string;
};

export type UserProfileStatus = 'start' | 'pause' | 'processing' | 'queuing';
export type HumanizedFeedType = 'update' | 'import' | 'remove' | 'export';

export type SupplierType =
  | 'connection'
  | 'marketplace'
  | 'platform'
  | 'supplier'
  | 'bulk';
export interface Supplier {
  id: string;
  name: string;
  description: string | null;
  email: string;
  notes: string | null;
  popular: boolean;
  redirectTo: string;
  url: string;
  countryCode: string;
  supplierType: SupplierType;
  categories: Array<string>;
  imageUrl: string | null;
}
export interface SupplierCategory {
  code: string;
  name: string;
}

export interface SupplierCountry {
  code: string;
  name: string;
}

export type FormattedJobType =
  | 'daily'
  | 'hourly'
  | 'minutes'
  | 'on_email_received'
  | 'on_file_uploaded';

export interface SyncFieldSetting {
  _destroy?: '1';
  extra_attributes: {
    _col_sep?: string;
    _location?: {
      id: string;
    };
    _low_stock_level?: string;
    _quantity_option?: string;
    add_tags?: boolean;
    add_to_init_quantity?: boolean;
    // it depend on compare_price_at_region and price_region selected
    additional_currencies?: Array<{
      currency: string;
      mapping: string;
      static: boolean;
    }>;
    allow_backorder?: 'yes';
    allow_but_notify?: 'notify';
    alt_mapping?: null;
    auto_ignore_option_words?: boolean;
    case_convert?: string;
    category_name?: string | null;
    clear_current_images?: boolean;
    col_sep?: string;
    condition_is_new?: string;
    condition_is_used?: string;
    condition_is_refurbished?: string;
    compare_at_pricing_conditions?: Array<{
      condition: 'any';
      formula: string;
    }>;
    compare_price_at_restrict_conditions?: Array<{
      attribute: string;
      value: string;
    }>;
    convert_line_break?: boolean;
    cost_pricing_conditions?: Array<{
      condition: 'any';
      formula: string;
    }>;
    cost_restrict_conditions?: Array<{
      attribute: string;
      value: string;
    }>;
    create_key_if_not_found?: boolean;
    create_location?: boolean;
    currency_converter?: boolean;
    custom_preorder_message?: null;
    date_format?: string;
    deduct_quantity_from_column?: null;
    default_currency?: 'USD';
    default_image_url?: null;
    dimension_delimiter?: string;
    dimension_formula?: string;
    disable_condition?: 'false';
    disallow_backorder?: 'no';
    discount_type?: boolean;
    enable_condition?: 'true';
    eq_disable_track_qty_value?: 'false';
    eq_enable_track_qty_value?: 'true';
    eq_hide_value?: 'false';
    eq_show_value?: 'true';
    false_values?: string;
    field_prefix?: null;
    find?: string;
    force_override_compare_at_price?: boolean;
    force_override_cost?: boolean;
    force_override_description?: boolean;
    force_override?: boolean;
    hs_code_suffix?: null;
    ignore_tags?: null;
    ignore_words?: string;
    image_cdn?: boolean;
    in_stock_flag?: 'true';
    is_not_visible_value?: null;
    is_redirect_new_handle?: boolean;
    is_visible_value?: null;
    labels?: null;
    language_code?: 'en';
    metadata_key?: null;
    new_currency?: 'USD';
    not_taxable_flag?: 'false';
    only_deduct_quantity?: boolean;
    only_tags?: string;
    only_update_stock_location?: boolean;
    original_language?: string;
    out_stock_flag?: 'false';
    override_categories?: boolean;
    override_publish?: boolean;
    override_tags?: boolean;
    parent_category_id?: string;
    policy_continue_value?: 'continue';
    policy_deny_value?: 'deny';
    pricing_conditions?: Array<{
      condition: 'any';
      formula: LiteralUnion<'*1', string>;
    }>;
    price_delimiter?: LiteralUnion<'auto', string>;
    price_round?: number;
    purchase_available_flag?: 'available';
    purchase_disabled_flag?: 'disabled';
    purchase_preorder_flag?: 'preorder';
    quantity_delimiter?: 'auto';
    quantity_use_on_hand?: boolean | string;
    remove_preoder_on_date?: boolean;
    replace?: string;
    restrict_conditions?: Array<{
      attribute: string;
      value: string;
    }>;
    returned_language?: string;
    rules_json?: Array<{
      operator: string;
      key: string;
      value: string;
    }>;
    set_date_if_zero_qty?: boolean;
    section_title?: string | null;
    reset_init_quantity?: boolean;
    skip_if_blank?: boolean;
    skip_zero_blank_labels?: boolean;
    static_flag: boolean | 'true' | 'false';
    status_active_flag?: 'active';
    status_archived_flag?: 'archived';
    status_draft_flag?: 'draft';
    taxable_flag?: 'true';
    title_separator?: string;
    update_only_if_nil?: boolean;
    url_prefix?: null;
    url_unescape?: boolean;
    variant_image_fallback?: string | null;
    weight_delimiter?: string;
    weight_formula?: string;
    weight_unit?: string;
    with_free_shipping_value?: null;
    without_free_shipping_value?: null;
    wrap_tag?: string;
  };
  field_mapping: string | null;
  field_name: string;
  id: number;
  is_locked: boolean;
  user_profile_id: number;
}

export interface ProductOptionsSyncField {
  created_at: string;
  extra_attributes: {
    case_convert?: string;
    dynamic_option_name?: boolean;
    find?: string;
    replace?: string;
    option1_name?: string;
    option2_name?: string;
    option3_name?: string;
    existing_product_identifier?: '' | 'barcode' | 'handle' | 'product_title';
    existing_product_tag?: string;
    auto_remove_default_title?: boolean;
    original_language?: string;
    returned_language?: string;
  };
  field_mapping: string | null;
  field_name: string;
  id: number;
  is_locked: boolean;
  updated_at: string;
  user_profile_id: number;
}
export interface Metafield {
  _namespaceAndKey?: string /* UI derived */;
  _destroy?: '1';
  created_at: string;
  extra_attributes: MetaFieldAttributes;
  field_mapping: string | null;
  field_name: string;
  id: number;
  is_locked: boolean;
  updated_at: string;
  user_profile_id: number;
}

export const feedTypes = ['update', 'import', 'remove'] as const;

export const storeFilterKeys = [
  '', // new filter initial state
  'barcode',
  'bpn',
  'brand',
  'category',
  'collection',
  'ignore_dont_track_inventory',
  'ignore_zero_quantity',
  'inventory_quantity',
  'is_visible',
  'product_type',
  'product_category_id',
  'published',
  'sku',
  'status',
  'tags',
  'title',
  'update_only_when_zero_qty',
  'vendor',
] as const;

export const storeFilterConditionOperators = [
  'contains',
  'end_with',
  'equals',
  'greater_than_or_equal',
  'greater_than',
  'less_than_or_equal',
  'less_than',
  'not_equals',
  'start_with',
] as const;

const feedFilterConditionOperators = [
  'between',
  'equals',
  'contains',
  'end_with',
  'greater_than',
  'less_than',
  'not_contains',
  'not_equals',
  'start_with',
] as const;

const FeedFilterConditionOperatorEnum = z.enum(feedFilterConditionOperators);

const StoreFilterKeyEnum = z.enum(storeFilterKeys);
const StoreFilterConditionOperatorEnum = z.enum(storeFilterConditionOperators, {
  message: 'need_select_one',
});

export const minStringSchema = z
  .string()
  .catch('')
  .pipe(z.string().min(1, 'field_required'));

// TODO: need two? stringNumberSchema VS minStringNumberSchema

export const minStringNumberSchema = z.union([
  z.number(),
  minStringSchema.transform((v) => Number(v)),
]);

const stringNumberSchema = z.union([
  z.number(),
  z.string().transform((v) => Number(v)),
]);

export const userProfileSchema = z.object({
  accName: z.string().nullable(),
  aliexpressAppId: z.string().nullable(),
  aliexpressAppSecret: z.string().nullable(),
  authType: authTypeEnums,
  autoFileSettings: z.boolean().catch(true),
  bodyRaw: z.string().catch(''),
  bypassBlankRow: z.boolean(),
  callParams: z.record(z.string(), z.string()).catch({}),
  clearGoogleSheet: z.boolean().catch(false),
  colSep: z.string().catch(','),
  combinedSourceType: combinedSourceTypesEnums,
  connectionSettings: z
    .object({
      access_key: z.string(),
      access_token_secret: z.string(),
      access_token: z.string(),
      account_code: z.string(),
      account_id: z.string(),
      api_account: z.string(),
      api_password: z.string(),
      api_key: z.string(),
      api_secret: z.string(),
      app_id: z.string(),
      assortment_id: z.string(),
      auth: z.object({
        id: z.string(),
        login_instructions: z.string(),
        login_url: z.string(),
        password: z.string(),
        render_js: z.boolean(),
      }),
      branch_id: z.string(),
      brand_id: z.string().catch(''),
      brand: z.string(),
      business_id: z.string(),
      buyers_id: z.string(),
      catalog: z.object({
        label: z.string(),
        value: z.string(),
      }),
      category: z.union([
        z.string(),
        z.object({
          label: z.string(),
          value: z.string(),
        }),
      ]),
      class: z.string(),
      client: z.string(),
      client_id: z.string(),
      client_secret: z.string(),
      channel_key: z.string(),
      consumer_key: z.string(),
      consumer_secret: z.string(),
      cookies_key: z.string(),
      customer: z.string(),
      customer_code: z.string().catch(''),
      customer_number: z.string(),
      data_center_region: z.string(),
      data_key: z.string(),
      data_url: z.string(),
      date: z.union([
        z.string(),
        z.object({}).passthrough(), // dayjs object
      ]),
      days: stringNumberSchema,
      db_context: z.string(),
      email: z.string(),
      end_date: z.string(),
      endpoint: z.string(),
      file_path: z.string(),
      gaia_url: z.string(),
      grant_type: z.string(),
      host: z.string(),
      id: z.string(),
      identification_code: z.string(),
      inner_products_url: z.object({
        block_resources: z.boolean(),
        extract_rules: z.string(),
        instructions: z.string(),
        render_js: z.boolean(),
        wait: z.string(),
      }),
      item_numbers: z.string(),
      key: z.string(),
      language_id: z.string(),
      language: z.string(),
      login: z.string(),
      list_filter: z.string(),
      login_url: z.string(),
      max_page: stringNumberSchema,
      ocp_key: z.string(),
      option: z.string(),
      page_offset: z.number(), // TODO: test number<>string
      page_param: z.string(),
      password: z.string(),
      portal_id: z.string(),
      product_detail: z.object({
        block_resources: z.boolean(),
        extract_rules: z.string(),
        instructions: z.string(),
        render_js: z.boolean(),
        wait: z.string(),
      }),
      product_line: z.string(),
      products_url: z.object({
        block_resources: z.boolean(),
        category_pattern: z.string(),
        category: z.string().optional(),
        extract_rules: z.string(),
        instructions: z.string(),
        render_js: z.boolean(),
        wait: z.union([z.string(), z.number()]), // crawler discontinued connection
      }),
      pwd_mkt: z.string(),
      realm: z.string(),
      region: z.string(),
      retailer_list: z.string(),
      secret_client: z.string(),
      secret_key: z.string(),
      source_url: z.string(),
      start_date: z.string(),
      start_page: z.union([z.string(), z.number()]), // crawler discontinued connection
      store_code: z.string(),
      subscription: z.string(),
      subscription_key: z.string(),
      site: z.string(),
      token_key: z.string(),
      token_secret: z.string(),
      token: z.string(),
      type: z.string(),
      url_list: z.string(),
      use_dynamic_price: z.boolean(),
      user_id: z.string(),
      user_identifier: z.string(),
      user_secret: z.string(),
      username: z.string(),
      usr_mkt: z.string(),
      warehouse_codes: z.string(),
      warehouses: z.string(),
    })
    .partial(),
  customFileName: z.string().nullable(),
  customLoginField: z.string().nullable(),
  customLoginPasswordField: z.string().nullable(),
  detectedFileFormat: z.union([fileFormatEnums, z.null()]),
  email: z.string(),
  exportEmail: z.string().nullable(),
  feedFilters: z
    .array(
      z.object({
        key: z.string().min(1, 'field_required'),
        op: FeedFilterConditionOperatorEnum,
        value: z.string().min(1, 'field_required'),
      })
    )
    .superRefine((filters, ctx) => {
      // eval uniques / duplicates with new Set
      const keyOpList = filters.map((o) => `${o.key}-${o.op}`);
      const uniqueSet = new Set(keyOpList);
      const noDuplicates = filters.length === uniqueSet.size;

      if (noDuplicates) return;

      const duplicateKeyOpList = Object.entries(
        groupBy(keyOpList) // https://lodash.com/docs/4.17.15#groupBy
      )
        .filter(([, groupedKeyOp]) => (groupedKeyOp as []).length > 1)
        .map(([keyOp]) => keyOp);

      const duplicateIndexes: number[] = [];
      keyOpList.forEach((keyOp, index) => {
        if (duplicateKeyOpList.includes(keyOp)) duplicateIndexes.push(index);
      });
      console.log(`duplicateIndexes:`, duplicateIndexes);
      duplicateIndexes.forEach((filterIndex) => {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Duplicate Field Name and Condition',
          path: [filterIndex],
        });
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '',
          path: [filterIndex, 'key'],
        });
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '',
          path: [filterIndex, 'op'],
        });
      });
    })
    .default([]),
  feedType: z.enum(feedTypes),
  fileEncoding: z.string().catch(''),
  fileFormat: fileFormatEnums,
  fileName: z.string().nullable(),
  ftpRename: z.union([z.literal(0), z.literal(1), z.literal(2), z.literal(3)]),
  ftpWhitelist: z.boolean().catch(false),
  googleOutInsert: z.boolean().catch(true),
  googleShoppingCategory: z.string().catch(''),
  hasHeader: z.boolean().catch(false),
  headerParams: z.record(z.string(), z.string()).catch({}),
  hourlyEndTime: z.string(),
  hourlyStartTime: z.string(),
  httpMethod: z.enum(httpMethods, { message: 'Invalid HTTP Method' }),
  id: z.string(),
  ignoreZeroQuantity: z.boolean(),
  jobInterval: z.string(),
  jobTime: z.string(),
  jobType: z.enum(jobTypes),
  namespaceIdentifier: z.string().nullable(),
  oauthGranted: z.boolean().catch(false),
  oauthGrantedTo: z.string().nullable(),
  oneDriveShareUrl: z.string().nullable(),
  parentNode: z.string().catch(''),
  password: z
    .union([z.undefined(), z.string().min(1)])
    // Important:
    // Convert empty string "" to `undefined`, else saved as ""
    // `undefined` omits `password` param from submit,
    // maintains previously saved password in backend
    .catch(undefined), // only user input should update password
  pathToFile: z.string().catch(''),
  productIdentifierSyncField: z.object({
    _destroy: z.union([z.undefined(), z.literal('1')]),
    created_at: z.union([z.string(), z.null()]), // when create template no prefill
    extra_attributes: z.object({}),
    field_mapping: z.string().nullable(),
    field_name: z.string(),
    id: z.union([z.number(), z.null()]), // when create template no prefill
    is_locked: z.boolean(),
    updated_at: z.union([z.string(), z.null()]), // when create template no prefills
    user_profile_id: z.number(),
  }),
  profileName: z.string(),
  publicFile: z.boolean().catch(true),
  s3AccessKeyId: z.string().nullable(),
  s3BucketName: z.string().nullable(),
  s3SecretAccessKey: z.string().nullable(),
  schedulerEnabled: z.boolean(),
  sheetName: z.string().catch(''),
  skipImportWithZeroQty: z.boolean(),
  skipTotalRows: z.union([z.string(), z.number()]).catch(0), // TODO: test number<>string
  sourceAuth: z.boolean().catch(false),
  sourceUrl: z.string().catch(''),
  sshKey: z.string().nullable(),
  storeFilters: z
    .array(
      z.discriminatedUnion('key', [
        z.object({
          key: StoreFilterKeyEnum.extract(['']).refine((v) => v.length > 0, {
            message: 'field_required',
          }),
          conditions: z.array(z.never()),
        }),
        z.object({
          key: StoreFilterKeyEnum.extract([
            'ignore_dont_track_inventory',
            'ignore_zero_quantity',
            'update_only_when_zero_qty',
          ]),
          conditions: z.array(
            z.object({
              operator: StoreFilterConditionOperatorEnum,
              value: z.boolean({ message: 'need_select_one' }),
            })
          ),
        }),
        z.object({
          key: StoreFilterKeyEnum.extract(['inventory_quantity']),
          conditions: z.array(
            z.object({
              operator: StoreFilterConditionOperatorEnum,
              value: z.union([
                z.number(),
                z
                  .string()
                  .min(1, 'field_required')
                  .transform((v) => Number(v)),
              ]),
            })
          ),
        }),
        z.object({
          key: StoreFilterKeyEnum.extract(['status']),
          conditions: z.array(
            z.object({
              operator: StoreFilterConditionOperatorEnum,
              value: z.string(),
              _value: z
                .array(
                  z.object({
                    id: z.string(),
                    title: z.string().optional(),
                  })
                )
                .min(1, 'field_required')
                .optional(),
            })
          ),
        }),
        z.object({
          key: StoreFilterKeyEnum.extract(['collection']),
          conditions: z
            .array(
              z.object({
                operator: StoreFilterConditionOperatorEnum,
                value: z.string(),
                _value: z
                  .array(
                    z.object({
                      id: z.union([z.string(), z.number()]),
                      title: z.string().optional(),
                      handle: z.string().optional(),
                    })
                  )
                  .optional(),
              })
            )
            .superRefine((conditions, ctx) => {
              // validate either condition has value
              if (!conditions[0].value && !conditions[1].value) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: 'field_required',
                  path: [0, '_value'],
                });
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: 'field_required',
                  path: [1, '_value'],
                });
              }
            }),
        }),
        z.object({
          key: StoreFilterKeyEnum.extract(['product_category_id']),
          conditions: z.array(
            z.object({
              operator: StoreFilterConditionOperatorEnum,
              value: z.string(),
              _value: z
                .object({
                  id: z.string(),
                  title: z.string().optional(),
                })
                .optional(),
            })
          ),
        }),
        z.object({
          key: StoreFilterKeyEnum.extract(['product_type']), // use one shape for match both selection and tag field
          conditions: z
            .array(
              z.object({
                operator: StoreFilterConditionOperatorEnum,
                value: z.string(),
                _value: z.array(z.string()).optional(),
              })
            )
            .superRefine((conditions, ctx) => {
              // validate either condition has value
              if (!conditions[0].value && !conditions[1].value) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: 'field_required',
                  path: [0, '_value'],
                });
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: 'field_required',
                  path: [1, '_value'],
                });
              }
            }),
        }),
        z.object({
          key: StoreFilterKeyEnum.exclude([
            '',
            'product_type',
            'ignore_dont_track_inventory',
            'ignore_zero_quantity',
            'update_only_when_zero_qty',
            'inventory_quantity',
            'status',
            'collection',
            'product_category_id',
          ]),
          conditions: z
            .array(
              z.object({
                operator: StoreFilterConditionOperatorEnum,
                value: z.string(),
              })
            )
            .superRefine((conditions, ctx) => {
              // validate either condition has value
              if (!conditions[0].value && !conditions[1].value) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: 'field_required',
                  path: [0, 'value'],
                });
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: 'field_required',
                  path: [1, 'value'],
                });
              }
            }),
        }),
      ])
    )
    .default([]),
  unleashedApiId: z.string().nullable(),
  unleashedApiKey: z.string().nullable(),
  useTimeRange: z.boolean(),
  variantNode: z.string().catch(''),
  walmartClientId: z.string().nullable(),
  walmartClientSecret: z.string().nullable(),
  woocommerceApiVersion: z.enum(woocommerceApiVersions).catch('v3'),
  woocommerceConsumerKey: z.string().nullable(),
  woocommerceConsumerSecret: z.string().nullable(),
});

// TODO: dont export, only use in UserProfile, replace UserProfile later
type ZodUserProfile = z.infer<typeof userProfileSchema>;

export interface UserProfile
  extends Pick<
    ZodUserProfile,
    | 'accName'
    | 'aliexpressAppId'
    | 'aliexpressAppSecret'
    | 'autoFileSettings'
    | 'authType'
    | 'bodyRaw'
    | 'bypassBlankRow'
    | 'callParams'
    | 'clearGoogleSheet'
    | 'colSep'
    | 'combinedSourceType'
    | 'connectionSettings'
    | 'customFileName'
    | 'customLoginField'
    | 'customLoginPasswordField'
    | 'detectedFileFormat'
    | 'email'
    | 'exportEmail'
    | 'feedFilters'
    | 'feedType'
    | 'fileEncoding'
    | 'fileFormat'
    | 'fileName'
    | 'ftpRename'
    | 'ftpWhitelist'
    | 'googleOutInsert'
    | 'googleShoppingCategory'
    | 'hasHeader'
    | 'headerParams'
    | 'hourlyEndTime'
    | 'hourlyStartTime'
    | 'httpMethod'
    | 'id'
    | 'ignoreZeroQuantity'
    | 'jobInterval'
    | 'jobTime'
    | 'jobType'
    | 'namespaceIdentifier'
    | 'oauthGranted'
    | 'oauthGrantedTo'
    | 'oneDriveShareUrl'
    | 'parentNode'
    | 'password'
    | 'pathToFile'
    | 'productIdentifierSyncField'
    | 'profileName'
    | 'publicFile'
    | 's3AccessKeyId'
    | 's3BucketName'
    | 's3SecretAccessKey'
    | 'schedulerEnabled'
    | 'sheetName'
    | 'skipImportWithZeroQty'
    | 'skipTotalRows'
    | 'sourceAuth'
    | 'sourceUrl'
    | 'sshKey'
    | 'storeFilters'
    | 'unleashedApiId'
    | 'unleashedApiKey'
    | 'useTimeRange'
    | 'variantNode'
    | 'walmartClientId'
    | 'walmartClientSecret'
    | 'woocommerceApiVersion'
    | 'woocommerceConsumerKey'
    | 'woocommerceConsumerSecret'
  > {
  activityLogsUrl: string;
  aliexpressAccessToken: string | null;
  apiSleepSecPerPage: number;
  assignVariantsToFirstImage: boolean;
  autoClearNotInFeedCreated: boolean;
  autoResetQuantity: boolean;
  autoTagging: boolean;
  bigcommerceRetainAvailability: boolean;
  boxAccessToken: string | null;
  boxRefreshToken: string | null;
  bulkLoading: boolean;
  caseSensitive: boolean;
  checksumCheck: boolean;
  createdAt: string;
  decryptedPassword: string | null;
  deleteMode: 1 | null;
  ebayAuthToken: string | null;
  ebaySessionId: string | null;
  enabled: boolean;
  etsySecret: string | null;
  etsyToken: string | null;
  excludeBpn: string | null;
  excludeCollections: string | null;
  excludeProductTypes: string | null;
  excludeSkus: string | null;
  excludeTags: string | null;
  excludeVendors: string | null;
  extraOptions: {
    sync_inventory_availability?: boolean;
  };
  fastTrack: boolean; // Snappy
  fastTrackDownloadLink: string;
  feedFileLocation: string | null;
  filterParams: string | null;
  forceOverrideCompareAtPrice: boolean;
  formattedJobTimezone: 'string';
  formattedJobType: FormattedJobType;
  frequencyTime: 'job_type_at_timezone';
  fromTemplate: boolean;
  hasFilter: boolean;
  hideUnmatchProducts: boolean;
  humanizeFeedType: HumanizedFeedType;
  ignoreDontTrackInventory: boolean;
  importSort: boolean;
  importTags: string;
  includeBpn: string | null;
  includeSkus: string | null;
  includeTags: string | null;
  ioMode: 'in' | 'out';
  isAutoGenerateMetafieldDefinitions: boolean;
  isCanceling: boolean;
  isDeleted: boolean;
  isQtyRuleLocked: boolean;
  isQueryingProducts: boolean;
  lastEmailCreatedAt: string | null;
  latestActivityLog?: ActivityLog;
  locationId: string | null;
  locationName: string | null;
  lowStockLevel: number;
  metafields: Array<Metafield> | null;
  notInFeedUrl: string;
  oneDriveRefreshToken: string | null;
  oneDriveToken: string | null;
  origStatus: string | null;
  partialMatch: boolean;
  pinned: boolean;
  postfix: string | null;
  prefix: string | null;
  preview: boolean | null;
  processingNo: number | null;
  productIdentifier: string;
  productKeySeparator: string;
  productOptionsSyncField: Array<ProductOptionsSyncField> | null;
  productTypeFilter: string | null;
  profileStatus: string;
  progress: string;
  publishedApplyMatchingProducts: boolean;
  publishedApplyToAll: boolean;
  publishedFilter: string | null;
  quickbooksAccessToken: string | null;
  quickbooksRealmId: string | null;
  quickbooksRefreshToken: string | null;
  receivedEmail: string | null;
  removeProductWhenAllLocationsNil: boolean;
  removeProfileId: number | null;
  salesChannelIds: Array<string>;
  shopifyInventoryManagement: string | null;
  shopifyProductKey: string | null;
  shopifyTrackInventory: boolean;
  sourceFileFileName: string | null;
  sourceType: string;
  status: UserProfileStatus;
  storePrefix: string | null;
  supplier?: Supplier;
  syncFieldSettings: Array<SyncFieldSetting>;
  unpublishedApplyMatchingProducts: boolean;
  unpublishedApplyToAll: boolean;
  updatedAt: string;
  updateDuplicateProductKey: boolean;
  updateProductLevel: boolean;
  variantImageLink: boolean;
  vendAccessToken: string | null;
  vendDomainPrefix: string | null;
  vendorFilter: string | null;
  vendRefreshToken: string | null;
  walmartAccessToken: string | null;
  runWebhookMode: boolean;
  woocommerceProductAttributes: Array<Metafield> | null;
  xeroAccessToken: string | null;
  xeroClientId: string | null;
  xeroClientSecret: string | null;
  xeroRefreshToken: string | null;
  xeroTenantId: string | null;
  zeroQty: boolean;
}

export type CombinedSourceType = UserProfile['combinedSourceType'];
export type StoreFilter = UserProfile['storeFilters'][number];
export type StoreFilterKey = StoreFilter['key'];
export type StoreFilterCondition = StoreFilter['conditions'][number];
export type StoreFilterConditionOperator = StoreFilterCondition['operator'];

export type FeedFilter = UserProfile['feedFilters'][number];

// NOTE:
// - dropdown display follows array order
// - value is t(value) label
export const feedFilterOperatorOptions: Array<{
  key: FeedFilter['op'];
  value: string;
}> = [
  { key: 'equals', value: 'contains' },
  { key: 'not_equals', value: 'not_contains' },
  { key: 'greater_than', value: 'greater_than' },
  { key: 'less_than', value: 'lower_than' },
  { key: 'start_with', value: 'start_with' },
  { key: 'end_with', value: 'end_with' },
  { key: 'contains', value: 'contain' },
  { key: 'not_contains', value: 'not_contain' },
  { key: 'between', value: 'between' },
];

// gql GetCollections
export type Collection = {
  id: number;
  title: string;
};

export type ChargePeriod = 'monthly' | 'annually';

export type PricingPlan = {
  hidden: boolean;
  id: string;
  importLimit: number;
  importSourceLimit: number;
  isCustomPlan: boolean;
  key:
    | 'Snappy'
    | 'Trial'
    | 'Free'
    | 'Basic'
    | 'Basic + Credit'
    | 'Pro'
    | 'Business'
    | 'ProX + Credit'
    | 'ProXL + Credit'
    | 'Business + Credit'
    | `Custom Plan ${string}`;
  limit: number;
  minHour: number;
  monthlyCreditLoad: number;
  monthlyCreditPlan: boolean;
  popular: boolean;
  price: number;
  schedule: string;
  schedulePerDay: number;
  sourceLimit: number;
  version: 'v5';
};

// some different props from ActivityLog
export type WebSocketActivityLog = {
  actual_product_updated: number;
  cache: boolean;
  created_at: string;
  download_links: {
    imported_logs: string;
    out_of_stock: string;
    remove_logs: string;
    skus_not_found_in_feed: string;
    skus_not_found_in_store: string;
    update_logs: string;
  };
  file_name: string | null;
  id: number;
  number_product_update_failed: number;
  number_product_updated: number;
  partial: boolean;
  percentage_matched: number;
  remark: string | null;
  remark_values: object;
  status: boolean;
  time_taken: string | null;
  total_left_over_ids: number;
  total_out_of_stock: number;
  total_store_skus: number | null;
  trigger_by: string | null;
  undo_at: string | null;
  updated_at: string;
};

export interface SalesChannel {
  id: string;
  name: string;
  supportsFuturePublishing: boolean;
  app: {
    handle: 'online_store' | 'shopify-graphiql-app';
  };
}
