import {
  FormHelperText as MuiFormHelperText,
  type FormHelperTextProps as MuiFormHelperTextProps,
} from '@mui/material';

export interface FormHelperTextProps extends MuiFormHelperTextProps {}

export function FormHelperText({ sx, ...props }: FormHelperTextProps) {
  return (
    <MuiFormHelperText
      sx={{
        fontSize: '12px',
        overflowWrap: 'break-word',
        ...sx,
      }}
      {...props}
    />
  );
}
