import {
  Popover,
  Stack,
  Typography,
  type PopoverProps,
  type StackProps,
} from '@mui/material';
import merge from 'lodash/merge';

import { Button, type ButtonProps } from '@/components/Button';

export interface PopoverConfirmProps extends Omit<PopoverProps, 'PaperProps'> {
  cancelButtonProps?: ButtonProps;
  okButtonProps?: ButtonProps;
  buttonGroupSx?: StackProps['sx'];
}

export function PopoverConfirm({
  slotProps,
  children,
  cancelButtonProps,
  okButtonProps,
  buttonGroupSx,
  ...props
}: PopoverConfirmProps) {
  return (
    <Popover
      slotProps={merge(
        { paper: { sx: { padding: '24px 16px', width: 350 } } },
        slotProps
      )}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      {...props}
    >
      <Typography variant="body1">{children}</Typography>
      {(cancelButtonProps || okButtonProps) && (
        <Stack
          direction="row"
          sx={{
            justifyContent: 'flex-end',
            alignItems: 'center',
            marginTop: '24px',
            ...buttonGroupSx,
          }}
          spacing="8px"
        >
          {cancelButtonProps && (
            <Button {...cancelButtonProps} size="small" variant="outlined" />
          )}
          {okButtonProps && (
            <Button
              {...okButtonProps}
              size="small"
              variant="contained"
              disableRipple
            />
          )}
        </Stack>
      )}
    </Popover>
  );
}
