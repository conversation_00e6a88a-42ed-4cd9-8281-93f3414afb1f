import {
  Switch as MuiSwitch,
  Typography,
  type FormControlLabelProps,
  type SwitchProps as MuiSwitchProps,
} from '@mui/material';
import * as React from 'react';
import {
  useController,
  type FieldValues,
  type UseControllerProps,
} from 'react-hook-form';

import { FormControlLabel } from '@/components/FormControlLabel';
import { FormHelperText } from '@/components/FormHelperText';

export interface SwitchProps<TFieldValues extends FieldValues = FieldValues>
  extends Pick<UseControllerProps<TFieldValues>, 'name' | 'control'>,
    Pick<FormControlLabelProps, 'label' | 'sx' | 'disabled'>,
    Omit<MuiSwitchProps, 'name' | 'ref' | 'disabled'> {
  helperText?: React.ReactNode;
}

export function Switch<TFieldValues extends FieldValues>({
  name,
  control,
  label,
  helperText,
  disabled,
  sx,
  onChange,
  ...props
}: SwitchProps<TFieldValues>) {
  const { field, formState } = useController({
    name,
    control,
  });

  return (
    <FormControlLabel
      label={
        <Typography variant="h6">
          {label}
          {helperText && <FormHelperText>{helperText}</FormHelperText>}
        </Typography>
      }
      disabled={disabled || formState.isSubmitting}
      control={
        <MuiSwitch
          checked={field.value ?? false}
          {...props}
          {...field}
          onChange={(event, checked) => {
            field.onChange(event, checked);
            onChange?.(event, checked);
          }}
        />
      }
      sx={{
        // 'flex-start' allows label to be multi-line
        // and still align with control (Switch)
        alignItems: 'flex-start',
        // align label={<Typography>} above with control Switch
        '> .MuiTypography-root': { paddingTop: '6px' },
        ...sx,
      }}
    />
  );
}
