import { Crisp } from 'crisp-sdk-web';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button } from '@/components/Button';
import { Dialog } from '@/components/Dialog';
import * as routes from '@/routes';

interface FeedLimitDialogProps {
  open: boolean;
  handleClose: () => void;
  errorMessage: string;
}
export const FeedLimitDialog = ({
  open,
  handleClose,
  errorMessage,
}: FeedLimitDialogProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const error = React.useMemo(() => {
    switch (errorMessage) {
      case 'reached_limit_import':
        return {
          title: t('contact_us'),
          subtitle: t('import_feed_limit_body'),
          buttonText: t('request'),
          buttonAction: () => {
            Crisp.chat.open();
            Crisp.message.setMessageText(
              'Requesting for additional add product feed'
            );
            handleClose();
          },
        };
      case 'reached_limit_remove':
        return {
          title: t('contact_us'),
          subtitle: t('remove_feed_limit_body'),
          buttonText: t('request'),
          buttonAction: () => {
            Crisp.chat.open();
            Crisp.message.setMessageText(
              'Requesting for additional remove product feed'
            );
            handleClose();
          },
        };
      default:
        return {
          title: t('update_feed_limit_title'),
          subtitle: (
            <span>
              Feed limit exceeded, please <strong>upgrade</strong> to increase
              the feed limit.
            </span>
          ),
          buttonText: t('upgrade'),
          buttonAction: () => {
            navigate(routes.billing);
          },
        };
    }
  }, [errorMessage, navigate, t, handleClose]);

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="xs">
      <Dialog.Title>{error.title}</Dialog.Title>
      <Dialog.Content>{error.subtitle}</Dialog.Content>
      <Dialog.Actions>
        <Button variant="outlined" size="small" onClick={handleClose}>
          {t('cancel')}
        </Button>
        <Button variant="contained" size="small" onClick={error.buttonAction}>
          {error.buttonText}
        </Button>
      </Dialog.Actions>
    </Dialog>
  );
};
