import {
  RadioGroup as MuiRadioGroup,
  Radio,
  Stack,
  Typography,
  type FormControlLabelProps,
  type RadioGroupProps as MuiRadioGroupProps,
  type RadioProps as MuiRadioProps,
  type StackProps,
} from '@mui/material';
import * as React from 'react';
import {
  useController,
  useFormContext,
  type FieldValues,
  type UseControllerProps,
} from 'react-hook-form';

import {
  ErrorHelperText,
  type ErrorHelperTextProps,
} from '@/components/ErrorHelperText';
import { FormControlLabel } from '@/components/FormControlLabel';
import { FormHelperText } from '@/components/FormHelperText';

export interface RadioGroupProps<TFieldValues extends FieldValues = FieldValues>
  extends Pick<UseControllerProps<TFieldValues>, 'control' | 'name'>,
    Omit<MuiRadioGroupProps, 'name' | 'ref' | 'row'>,
    Pick<StackProps, 'spacing' | 'direction'>,
    Pick<ErrorHelperTextProps, 'helperText'>,
    Pick<MuiRadioProps, 'size'> {
  options: Array<{
    label: FormControlLabelProps['label'];
    value: FormControlLabelProps['value'];
    disabled?: FormControlLabelProps['disabled'];
    labelHelperText?: React.ReactNode;
  }>;
}

export function RadioGroup<TFieldValues extends FieldValues>({
  control,
  name,
  options = [],
  onChange,
  spacing = '0px',
  direction,
  helperText,
  defaultValue,
  size = 'medium',
  ...props
}: RadioGroupProps<TFieldValues>) {
  const {
    field,
    formState: { errors, isSubmitting },
  } = useController({
    name,
    control,
  });

  const { setValue } = useFormContext() || {};

  // we are using useEffect because rhf is controlling form state
  // if we update MuiRadioGroup.value directly, out of sync with rhf form state
  // we let rhf update form state and cascade value
  // down to MuiRadioGroup via value={field.value}

  // if `defaultValue` provided & field.value falsy, update field.value
  React.useEffect(() => {
    if (field.value) return; // already has value, abort
    if (defaultValue) setValue?.(name, defaultValue);
  }, [field, defaultValue, setValue, name]);

  return (
    <MuiRadioGroup
      {...props}
      {...field}
      onChange={(event, value) => {
        field.onChange(event, value);
        onChange?.(event, value);
      }}
      value={field.value ?? ''}
      // row={true} prop has no effect since we wrap Stack
    >
      {/* we want spacing control */}
      <Stack
        spacing={spacing ?? direction === 'row' ? '8px' : 0}
        direction={direction}
      >
        {options.map(({ value, label, disabled, labelHelperText }) => (
          <FormControlLabel
            key={label as string}
            value={value}
            control={<Radio size={size} />}
            label={
              <Typography
                sx={{ fontSize: size === 'medium' ? 16 : 12 }}
                component="div"
              >
                {label}
                {labelHelperText && (
                  <FormHelperText
                    component="span"
                    sx={{ marginBottom: '16px', display: 'block' }}
                  >
                    {labelHelperText}
                  </FormHelperText>
                )}
              </Typography>
            }
            disabled={disabled ?? isSubmitting}
          />
        ))}
      </Stack>
      <ErrorHelperText name={name} helperText={helperText} errors={errors} />
    </MuiRadioGroup>
  );
}
