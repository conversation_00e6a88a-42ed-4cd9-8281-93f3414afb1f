import { useTheme } from '@mui/material';
import MuiTooltip, {
  type TooltipProps as MuiTooltipProps,
} from '@mui/material/Tooltip';

export interface TooltipProps extends Omit<MuiTooltipProps, 'PopperProps'> {
  variant?: 'default' | 'primary';
}
export function Tooltip({
  slotProps,
  variant = 'primary',
  ...props
}: TooltipProps) {
  const theme = useTheme();

  return (
    <MuiTooltip
      {...props}
      slotProps={{
        ...slotProps,
        popper: {
          ...slotProps?.popper,
          sx: {
            '&.MuiTooltip-popper': {
              zIndex: 1000, // set below CustomAppBar (1100)
            },
            ...(variant === 'primary'
              ? {
                  '& .MuiTooltip-tooltip': {
                    backgroundColor: '#0C7FE833',
                    color: theme.palette.blue[300],
                  },
                }
              : {}),
          },
        },
      }}
    />
  );
}
