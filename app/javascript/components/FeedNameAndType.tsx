import { faCircleXmark, faPen } from '@fortawesome/pro-light-svg-icons';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons';
import { Box, Stack, Typography, useTheme } from '@mui/material';
import * as React from 'react';
import { useForm } from 'react-hook-form';

import { Alert } from '@/components/Alert';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { TextField } from '@/components/TextField';
import { Tooltip } from '@/components/Tooltip';
import { useAlert } from '@/hooks/useAlert';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { UpdateFeedName } from '@/queries/FeedSettings';
import type { FeedRenderProps } from '@/shared/feedTypes';
import type { UserProfile } from '@/types';

interface FeedNameAndTypeProps extends Pick<FeedNameFormProps, 'feedId'> {
  feedRenderProps: FeedRenderProps;
  feedName: string;
  onSubmit?: (profileName: string) => void;
}

export const FeedNameAndType = ({
  feedRenderProps,
  feedId,
  feedName,
  onSubmit,
}: FeedNameAndTypeProps) => {
  const theme = useTheme();
  const matchesSm = useMatches('sm');
  const [renameActive, setRenameActive] = React.useState(false);
  const alert = useAlert();

  return (
    <>
      {alert.message.length > 0 && <Alert>{alert.message}</Alert>}
      <Stack
        direction="row"
        sx={{
          alignItems: 'center',
          margin: '0 8px 16px 0',
          width: '100%',
        }}
        spacing="8px"
      >
        <Box sx={{ color: feedRenderProps.iconProps.color }}>
          <Icon
            type="kit"
            fontSize={24}
            className={feedRenderProps.iconProps.className}
          />
        </Box>
        {/* Only show feed type on desktop */}
        {!matchesSm && (
          <Typography
            sx={{
              fontSize: '16px',
              color: theme.palette.grey[300],
              ml: 0,
              mr: 2,
            }}
          >
            {feedRenderProps.title}
          </Typography>
        )}
        <Box sx={{ width: '80%' }}>
          {renameActive ? (
            <FeedNameForm
              feedId={feedId}
              feedName={feedName}
              onSubmit={onSubmit}
              onClose={() => setRenameActive(false)}
            />
          ) : (
            <FeedNameDisplay
              feedName={feedName}
              onClick={() => setRenameActive(true)}
            />
          )}
        </Box>
      </Stack>
    </>
  );
};

interface FeedNameDisplayProps {
  feedName: string;
  onClick?: () => void;
}

function FeedNameDisplay({ feedName, onClick }: FeedNameDisplayProps) {
  return (
    <Stack
      sx={{
        alignItems: 'center',

        width: '100%', // truncate with noWrap responsively
      }}
      direction="row"
      spacing={1}
    >
      <Typography variant="h6" color="black" noWrap>
        {feedName}
      </Typography>
      {feedName !== 'Snappy' && (
        <Tooltip variant="default" placement="right" title="Rename">
          <IconButton
            onClick={onClick}
            sx={{ fontSize: '14px' }}
            id="rename-feed"
          >
            <Icon type="default" icon={faPen} />
          </IconButton>
        </Tooltip>
      )}
    </Stack>
  );
}

interface FeedNameFormProps {
  feedId?: string;
  feedName: string;
  onSubmit?: (feedName: string) => void;
  onClose: () => void;
}

function FeedNameForm(props: FeedNameFormProps) {
  const alert = useAlert();

  const { feedId, feedName, onClose } = props;
  const [, updateFeedName] = useMutation(UpdateFeedName, {
    dataKey: 'updateFeedName',
  });
  const { control, handleSubmit, formState } = useForm({
    values: { id: feedId, profileName: feedName },
  });
  const onSubmit = handleSubmit(async (values) => {
    if (values.id) {
      // existing feed
      await updateFeedName(
        {
          feedId: parseInt(values.id),
          profileName: values.profileName,
        },
        {
          onSuccess({ data, enqueueSnackbar }) {
            const profile = data as UserProfile;
            enqueueSnackbar(`Renamed as ${profile.profileName}`, {
              variant: 'success',
            });
          },
          onError({ error }) {
            alert.setMessage(error);
          },
        }
      ).finally(() => onClose());
    } else {
      // create new feed, not yet on backend
      props.onSubmit?.(values.profileName);
      onClose();
    }
  });
  return (
    <Stack
      direction="row"
      spacing="3px"
      sx={{
        alignItems: 'center',
      }}
    >
      <TextField
        sx={{ maxWidth: '50ch' }}
        name="profileName"
        control={control}
        variant="standard"
        autoFocus
        rules={{ required: true }}
        onKeyDownCapture={(e) => {
          if (e.key === 'Escape') onClose();
          // some pages have <form />, don't trigger submit
          if (e.key === 'Enter') {
            e.preventDefault();
            onSubmit(e);
          }
        }}
      />
      <IconButton
        onClick={onClose}
        disabled={formState.isSubmitting}
        id="cancel-rename-feed"
      >
        <Icon type="default" icon={faCircleXmark} />
      </IconButton>
      <IconButton
        // this component used by pages with <form />
        // nested <form /> not allowed. onClick for handleSubmit
        onClick={onSubmit}
        id="submit-rename-feed"
        disabled={formState.isSubmitting}
      >
        <Icon type="default" icon={faCircleCheck} />
      </IconButton>
    </Stack>
  );
}
