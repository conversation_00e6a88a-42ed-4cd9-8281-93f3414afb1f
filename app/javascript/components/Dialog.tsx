import {
  DialogActions,
  DialogContent,
  DialogTitle,
  Dialog as MuiDialog,
  Typography,
  useTheme,
  type DialogActionsProps,
  type DialogContentProps,
  type DialogProps,
  type DialogTitleProps,
} from '@mui/material';

const zIndexAboveScrollableComponents = 50; // stay above dropdowns etc in scrollable DialogContent

function Content({ sx, children, ...props }: DialogContentProps) {
  return (
    <DialogContent
      sx={{
        paddingLeft: '32px',
        paddingRight: '32px',
        paddingBottom: 0,

        ...sx,
      }}
      {...props}
    >
      <Typography variant="button">{children}</Typography>
    </DialogContent>
  );
}

function Actions({ sx, ...props }: DialogActionsProps) {
  const theme = useTheme();
  return (
    <DialogActions
      sx={{
        paddingTop: '24px',
        paddingLeft: '32px',
        paddingRight: '32px',
        paddingBottom: '32px',
        position: 'relative',
        backgroundColor: theme.palette.common.white,
        zIndex: zIndexAboveScrollableComponents,
        ...sx,
      }}
      {...props}
    />
  );
}

function Title({ sx, ...props }: DialogTitleProps) {
  const theme = useTheme();
  return (
    <DialogTitle
      sx={{
        position: 'relative',
        zIndex: zIndexAboveScrollableComponents,
        fontSize: 20,
        padding: '32px 32px 16px',
        backgroundColor: theme.palette.common.white,
        ...sx,
      }}
      {...props}
    />
  );
}

const Dialog = Object.assign(MuiDialog, { Title, Content, Actions });

export { Dialog, type DialogProps };
