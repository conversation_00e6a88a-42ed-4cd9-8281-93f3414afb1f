import {
  FontAwesomeIcon,
  type FontAwesomeIconProps,
} from '@fortawesome/react-fontawesome';
import { type CSSProperties } from 'react';

interface KitIconProps
  extends Pick<JSX.IntrinsicElements['i'], 'className'>,
    Pick<CSSProperties, 'fontSize' | 'width'> {}

export type IconType = 'default' | 'kit';

export type IconProps<T extends IconType = IconType> = T extends 'default'
  ? { type: 'default' } & FontAwesomeIconProps
  : { type: 'kit' } & KitIconProps;

export function Icon<T extends IconType>({
  type,
  fontSize,
  width,
  ...props
}: IconProps<T>) {
  return type === 'kit' ? (
    // Uploaded icons have their own unique `fak` prefix
    // https://fontawesome.com/v5/docs/web/use-kits/upload-icons#using-uploaded-icons-in-a-project
    <i className={props.className} style={{ fontSize, width }} />
  ) : (
    // Official Font Awesome icons
    <FontAwesomeIcon
      style={{ fontSize, width }}
      {...(props as IconProps<'default'>)}
    />
  );
}
