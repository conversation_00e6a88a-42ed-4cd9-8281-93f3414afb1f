import {
  LinearProgress as MuiLinearProgress,
  type LinearProgressProps as MuiLinearProgressProps,
} from '@mui/material';

export interface LinearProgressProps extends MuiLinearProgressProps {}

export function LinearProgress({ sx, ...props }: LinearProgressProps) {
  return (
    <MuiLinearProgress
      sx={{
        backgroundColor: '#e8f5ff',
        ...sx,
      }}
      {...props}
    />
  );
}
