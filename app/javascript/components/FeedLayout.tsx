import * as React from 'react';
import { Outlet, useNavigate, useParams } from 'react-router-dom';
import { gql, useQuery } from 'urql';

import * as routes from '@/routes';
import type { UserProfile } from '@/types';

export function FeedLayout() {
  const params = useParams();
  const navigate = useNavigate();
  const [query] = useQuery({
    query: gql<{
      getFeedById: Pick<UserProfile, 'id'>;
    }>`
      query FeedLayout($feedId: Int!) {
        getFeedById(feedId: $feedId) {
          __typename
          id
        }
      }
    `,
    variables: { feedId: Number(params.id) },
  });

  const hasFeed = Boolean(query.data?.getFeedById);

  // if invalid feed, skip render, redirect home
  React.useEffect(() => {
    if (!query.fetching && !hasFeed) navigate(routes.home);
  }, [query, hasFeed, navigate]);

  return hasFeed ? <Outlet /> : <></>;
}
