import {
  IconButton as MuiIconButton,
  useTheme,
  type IconButtonProps as MuiIconButtonProps,
} from '@mui/material';
import * as React from 'react';

export interface IconButtonProps extends MuiIconButtonProps {}

export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ sx, ...props }, ref) => {
    const theme = useTheme();
    return (
      <MuiIconButton
        sx={{
          height: '42px',
          width: '42px',
          borderRadius: '6px',
          color: theme.palette.primary.main,
          ...sx,
        }}
        {...props}
        ref={ref}
      />
    );
  }
);
