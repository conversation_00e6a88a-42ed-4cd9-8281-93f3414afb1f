import { Notifier } from '@airbrake/browser';
import {
  Button,
  <PERSON>ack,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import * as React from 'react';
import { useRouteError } from 'react-router-dom';

import { LayoutCenter } from '@/components/LayoutCenter';
import { Loading } from '@/components/Loading';
import { useMatches } from '@/hooks/useMatches';
import { GetCurrentStore } from '@/queries/StoreQueries';
import { newAppLogo, problemErrorPage } from '@/shared/images';
import { isProduction } from '@/shared/sharedUtil';
import { urqlClient } from '@/urqlClient';

// we have 2 similar errors on prod, search for phrases in common:
// "error loading dynamically imported module"
// "Failed to fetch dynamically imported module"
const dynamicImportError = 'dynamically imported module';

export function ErrorBoundary() {
  const theme = useTheme();
  const smAndDown = useMatches('sm');
  const aboveSm = useMediaQuery(theme.breakpoints.up('sm'));

  const error = useRouteError() as Error;
  console.error(error);

  const airbrake = new Notifier({
    projectId: 1,
    projectKey: 'c96bf71d753e71049161d4a452a33ed7',
    environment: 'UI (Topgun)',
    host: 'https://errors.stock-sync.com',
    remoteConfig: false,
  });

  airbrake.addFilter((notice) => (isProduction ? notice : null));

  airbrake.notify({
    error,
    params: { info: error, store: window.__CURRENT_ACTIVE_STORE },
  });

  const refresh = () => window.location.reload();

  const isDynamicImportError = error.message.includes(dynamicImportError);

  React.useEffect(() => {
    if (isDynamicImportError) {
      urqlClient
        .query(GetCurrentStore, undefined, {
          requestPolicy: 'network-only', // hit API directly, bypass cache
        })
        .toPromise()
        .then(() => {
          refresh(); // ping store API success, reload page
        });
    }
  }, [isDynamicImportError]);

  if (isDynamicImportError) return <Loading />;

  return (
    <LayoutCenter>
      <Stack sx={{ p: '0 28px 28px', alignItems: 'center' }}>
        <Stack sx={{ maxWidth: { xs: '240px', sm: '300px' } }}>
          <img
            src={newAppLogo}
            style={{ width: '100%', paddingBottom: '24px' }}
          />
          <img src={problemErrorPage} style={{ width: '100%' }} />
        </Stack>
        <Typography
          variant="h2"
          sx={{ mb: '38px', paddingTop: '24px' }}
          align="center"
        >
          There was a problem loading this website
        </Typography>
        <Typography variant="h6" align="center">
          Please try refreshing this page. If you are still facing this issue,
          {aboveSm && <br />} please contact {smAndDown && <br />}
          <EMAIL> {smAndDown && <br />} for further assistance.
        </Typography>
        <Button
          variant="contained"
          sx={{ mt: '48px', fontSize: '20px' }}
          onClick={refresh}
        >
          Refresh
        </Button>
      </Stack>
    </LayoutCenter>
  );
}

//
