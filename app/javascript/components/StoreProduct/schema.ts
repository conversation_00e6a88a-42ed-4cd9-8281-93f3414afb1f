import { z } from 'zod';

import { productCountsSchema } from '@/queries/Users';
import { userProfileSchema } from '@/types';

export const storeFiltersSchema = z.object({
  id: userProfileSchema.shape.id,
  profileName: userProfileSchema.shape.profileName,
  storeFilters: userProfileSchema.shape.storeFilters,
  _filteredProduct: productCountsSchema.shape.filtered,
  _totalProduct: productCountsSchema.shape.total,
  _precision: productCountsSchema.shape.precision,
});

export type StoreFiltersFormValues = z.infer<typeof storeFiltersSchema>;
