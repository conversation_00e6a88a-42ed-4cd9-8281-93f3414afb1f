import { faCircleInfo } from '@fortawesome/pro-light-svg-icons';
import { Box, Stack, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Card } from '@/components/Card';
import { Checkbox } from '@/components/Checkbox';
import { Icon } from '@/components/Icon';
import type { FeedAdvancedFormValues } from '@/pages/FeedAdvancedSettings';
import type { FeedMappingFormValues } from '@/pages/FeedMapping';

export function SalesChannels() {
  const { t } = useTranslation();
  const { control, watch, setValue } = useFormContext<
    FeedAdvancedFormValues | FeedMappingFormValues
  >();
  const salesChannelIds = watch('salesChannelIds');
  const salesChannels = watch('_salesChannels');

  return (
    <>
      <Card
        sx={{
          backgroundColor: '#0C7FE80F',
          padding: '16px',
          boxShadow: 0,
          marginTop: '16px',
          overflow: 'unset',
        }}
      >
        <Stack
          direction="row"
          spacing="8px"
          sx={{
            alignItems: 'flex-start',
          }}
        >
          <Icon
            type="default"
            icon={faCircleInfo}
            style={{
              color: '#00527C',
              fontSize: '16px',
              margin: '4px 0',
            }}
          />
          <Typography
            variant="button"
            sx={{
              fontWeight: 400,
              color: '#00527C',
            }}
          >
            {salesChannelIds.length === 0 ? (
              t('manage_sales_channel_drawer')
            ) : (
              <>
                {t('publish_on')} &nbsp;
                <span style={{ fontWeight: 600 }}>
                  {salesChannels
                    .filter((fsc) => fsc.checked)
                    .map((id) => id.name)
                    .join(', ')}
                </span>
              </>
            )}
          </Typography>
        </Stack>
      </Card>
      <Box sx={{ marginTop: '16px' }}>
        <Stack>
          {salesChannels.map((item, index) => (
            <Checkbox
              key={item.id}
              control={control}
              name={`_salesChannels.${index}.checked`}
              onChange={(_, checked) => {
                let channelIdSet = new Set(salesChannelIds);
                if (checked) {
                  channelIdSet = new Set([...channelIdSet, item.id]);
                } else {
                  channelIdSet.delete(item.id);
                }
                setValue('salesChannelIds', Array.from(channelIdSet));
              }}
              id={item.id}
              label={item.name}
            />
          ))}
        </Stack>
      </Box>
    </>
  );
}
