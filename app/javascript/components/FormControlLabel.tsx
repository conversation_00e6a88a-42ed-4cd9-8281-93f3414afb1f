import {
  FormControlLabel as MuiFormControlLabel,
  type FormControlLabelProps as MuiFormControlLabelProps,
} from '@mui/material';

export interface FormControlLabelProps extends MuiFormControlLabelProps {}

export function FormControlLabel({ sx, ...props }: FormControlLabelProps) {
  return (
    <MuiFormControlLabel
      sx={{
        marginLeft: 0,
        marginRight: 0,
        '.MuiFormControlLabel-label': {
          fontSize: '16px',
        },
        ...sx,
      }}
      {...props}
    />
  );
}
