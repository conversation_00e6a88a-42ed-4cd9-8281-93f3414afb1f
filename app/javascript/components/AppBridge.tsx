import { SaveBar, useAppBridge } from '@shopify/app-bridge-react';
import { useSnackbar } from 'notistack';
import * as React from 'react';

import { Button } from '@/components/Button';
import { useIsEmbeddedApp } from '@/hooks/useIsEmbeddedApp';

const saveBarId = 'stock-sync-save-bar';

interface SaveBar {
  id: string;
  open: () => void;
  close: () => void;
  onDismiss: () => void;
  onSave: () => void;
}

const AppBridgeContext = React.createContext<{ saveBar: SaveBar } | undefined>(
  undefined
);

interface AppBridgeProps {
  children: React.ReactNode;
}

export function AppBridge(props: AppBridgeProps) {
  const { isEmbeddedApp } = useIsEmbeddedApp();
  return isEmbeddedApp ? (
    <AppBridgeWrapper>{props.children}</AppBridgeWrapper>
  ) : (
    <MockAppBridgeWrapper>{props.children}</MockAppBridgeWrapper>
  );
}

interface AppBridgeContextProps {
  children: React.ReactNode;
}

function AppBridgeWrapper(props: AppBridgeContextProps) {
  const shopify = useAppBridge();
  const isOpenRef = React.useRef(false);
  const saveBar: SaveBar = {
    id: saveBarId,
    open: () => {
      if (!isOpenRef.current) {
        shopify.saveBar.show(saveBarId);
        isOpenRef.current = true;
      }
    },
    close: () => {
      shopify.saveBar.hide(saveBarId);
      isOpenRef.current = false;
    },
    onDismiss: () => {
      saveBar.close();
    },
    onSave: () => {
      saveBar.close();
    },
  };
  return (
    <AppBridgeContext.Provider value={{ saveBar }}>
      <SaveBar id={saveBarId}>
        <button
          variant="primary"
          onClick={() => {
            saveBar.onSave();
          }}
        />
        <button
          onClick={() => {
            saveBar.onDismiss();
          }}
        />
      </SaveBar>
      {props.children}
    </AppBridgeContext.Provider>
  );
}

interface MockAppBridgeContextProps {
  children: React.ReactNode;
}

function MockAppBridgeWrapper(props: MockAppBridgeContextProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();
  console.log('MockAppBridge isOpen state:', isOpen);

  const openSaveBar = React.useCallback(() => {
    console.log('MockAppBridge: open() called, isOpen:', isOpen);
    if (!isOpen) {
      const action = () => (
        <>
          <Button
            onClick={() => {
              setIsOpen(false);
              closeSnackbar(saveBarId);
            }}
          >
            Discard
          </Button>
          <Button
            onClick={() => {
              setIsOpen(false);
              closeSnackbar(saveBarId);
            }}
          >
            Save
          </Button>
        </>
      );

      enqueueSnackbar('There are unsaved changes. Save?', {
        key: saveBarId,
        action,
      });
      setIsOpen(true);
      console.log('MockAppBridge: snackbar shown, setIsOpen(true) called');
    }
  }, [isOpen, enqueueSnackbar, closeSnackbar]);

  const closeSaveBar = React.useCallback(() => {
    console.log('MockAppBridge: close() called, isOpen:', isOpen);
    if (isOpen) {
      closeSnackbar(saveBarId);
      setIsOpen(false);
      console.log('MockAppBridge: snackbar closed, setIsOpen(false) called');
    }
  }, [isOpen, closeSnackbar]);

  const saveBar: SaveBar = React.useMemo(
    () => ({
      id: saveBarId,
      open: openSaveBar,
      close: closeSaveBar,
      onDismiss: closeSaveBar,
      onSave: closeSaveBar,
    }),
    [openSaveBar, closeSaveBar]
  );

  return (
    <AppBridgeContext.Provider value={{ saveBar }}>
      {props.children}
    </AppBridgeContext.Provider>
  );
}

interface UseAppBridgeContextProps {
  showSaveBar: boolean;
  onDismiss?: SaveBar['onDismiss'];
  onSave?: SaveBar['onSave'];
}

export function useAppBridgeContext({
  showSaveBar,
  onDismiss,
  onSave,
}: UseAppBridgeContextProps) {
  const context = React.useContext(AppBridgeContext);
  React.useEffect(() => {
    console.log(
      'useAppBridgeContext effect running, showSaveBar:',
      showSaveBar
    );
    if (!context) return;
    if (showSaveBar) {
      console.log('useAppBridgeContext: calling context.saveBar.open()');
      context.saveBar.open();
    } else {
      console.log(
        'useAppBridgeContext: showSaveBar is false, calling context.saveBar.close()'
      );
      context.saveBar.close();
    }

    return () => {
      console.log(
        'useAppBridgeContext cleanup: calling context.saveBar.close()'
      );
      context.saveBar.close();
    };
  }, [showSaveBar, context]);

  if (context === undefined) {
    throw new Error(
      'useAppBridgeContext must be used within a AppBridgeContext.Provider'
    );
  }

  if (typeof onDismiss === 'function') context.saveBar.onDismiss = onDismiss;
  if (typeof onSave === 'function') context.saveBar.onSave = onSave;

  return context;
}
