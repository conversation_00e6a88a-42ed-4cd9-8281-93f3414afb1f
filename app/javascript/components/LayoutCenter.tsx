import { Box, type BoxProps } from '@mui/material';

export interface LayoutCenterProps extends Pick<BoxProps, 'children' | 'sx'> {}

export const LayoutCenter = ({ children, sx }: LayoutCenterProps) => (
  <Box
    sx={{
      height: '100vh',
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      ...sx,
    }}
  >
    {children}
  </Box>
);
