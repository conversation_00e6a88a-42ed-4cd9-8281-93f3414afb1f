import {
  Link as MuiLink,
  useTheme,
  type LinkProps as MuiLinkProps,
} from '@mui/material';
import * as React from 'react';
import {
  Link as RouterLink,
  type LinkProps as RouterLinkProps,
} from 'react-router-dom';

// avoid override from @shopify+app-bridge-types
interface LinkPropsWithoutAppBridgeOverride
  extends Omit<RouterLinkProps, 'variant'> {
  // @shopify+app-bridge-types extends `a` and `button` with `variant` prop - uncomment below and goto definition to see
  // variant: RouterLinkProps['variant'];
}
export interface LinkProps
  extends LinkPropsWithoutAppBridgeOverride,
    Pick<MuiLinkProps, 'sx' | 'underline'> {}

export const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(
  ({ to, target, sx, ...props }, ref) => {
    const theme = useTheme();

    // https://reactrouter.com/en/main/components/link
    // url NOT starting with "/" resolves relative to the parent route
    // so for external links http:// or https://, shorten to "//"
    const normalizedTo =
      to?.toString().replace('https://', '//').replace('http://', '//') ?? '';
    // external link must be target="_blank" else router error
    const normalizedTarget = normalizedTo.startsWith('//') ? '_blank' : target;

    // https://mui.com/material-ui/guides/routing/#component-prop
    return (
      <MuiLink
        sx={{
          color: theme.palette.blue[300],
          cursor: 'pointer',
          textDecorationColor: theme.palette.blue[300],
          // fontFamily: 'Inter, sans-serif',
          '&:hover': {
            color: theme.palette.blue[400],
          },
          ...sx,
        }}
        underline="hover"
        component={RouterLink}
        to={normalizedTo}
        target={normalizedTarget}
        // https://www.jitbit.com/alexblog/256-targetblank---the-most-underestimated-vulnerability-ever/
        rel={normalizedTarget === '_blank' ? 'noopener noreferrer' : undefined}
        {...props}
        ref={ref}
      />
    );
  }
);
