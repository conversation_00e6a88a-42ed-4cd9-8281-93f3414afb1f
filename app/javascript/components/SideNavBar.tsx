import {
  faArrowUpRightFromSquare,
  faBars,
  faBell,
  faChevronDown,
  faChevronUp,
  faComment,
  faEnvelope,
  faFileInvoiceDollar,
  faGear,
  faHandHoldingHand,
  faLaptop,
  faObjectsColumn,
  faScreenUsers,
} from '@fortawesome/pro-light-svg-icons';
import { faCirclePlus } from '@fortawesome/pro-solid-svg-icons';
import {
  Box,
  Divider,
  List,
  ListItemText,
  Menu,
  MenuItem,
  Drawer as MuiDrawer,
  Stack,
  Toolbar,
  Typography,
  useTheme,
  type CollapseProps,
  type DrawerProps,
  type TypographyProps,
} from '@mui/material';
import { type SxProps } from '@mui/material/styles';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import { Button } from '@/components/Button';
import { Collapse } from '@/components/Collapse';
import { Icon, type IconProps } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import {
  ListItemButton,
  type ListItemButtonProps,
} from '@/components/ListItemButton';
import { ListItemIcon } from '@/components/ListItemIcon';
import { Paper } from '@/components/Paper';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { useNavbar } from '@/hooks/useNavbar';
import SSLogo from '@/images/ss-logo.svg';
import { RevokeAccess } from '@/queries/StoreMutations';
import * as routes from '@/routes';
import { renderAdminLink, shortenShopifyStore } from '@/shared/sharedUtil';
import { numberFormat } from '@/shared/util';

const desktopDrawerWidth = 250;
const mobileDrawerWidth = 280;
export const closeSideNavBarWidth = 72;

function ResponsiveDrawer(props: DrawerProps) {
  const theme = useTheme();
  const sx: SxProps = props.open
    ? {
        background: '#f2f2f2',
        overflowX: 'hidden',
        boxShadow: {
          xs: 'none', // remove the box shadow for mobile
        },
        width: {
          xs: mobileDrawerWidth,
          sm: desktopDrawerWidth,
        },
        transition: theme.transitions.create('width', {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.leavingScreen,
        }),
      }
    : {
        overflowX: 'hidden',
        width: closeSideNavBarWidth,
        transition: theme.transitions.create('width', {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.leavingScreen,
        }),
      };
  return (
    <MuiDrawer
      sx={sx}
      slotProps={{
        paper: { sx: sx },
      }}
      {...props}
    />
  );
}

const defaultIcon = {
  fontSize: 16,
  // use largest icon width from upload kit for layout consistency
  width: 20, // "fak fa-account1" icon width
};

interface FaIconProps
  extends Pick<IconProps<'default'>, 'icon' | 'id' | 'fontSize' | 'color'> {}

function FaIcon({
  color,
  id,
  icon,
  fontSize = defaultIcon.fontSize,
}: FaIconProps) {
  return (
    <Icon
      type="default"
      icon={icon}
      id={id}
      fontSize={fontSize}
      width={defaultIcon.width}
      color={color}
    />
  );
}

interface KitIconProps extends Pick<IconProps<'kit'>, 'className'> {}

function KitIcon({ className }: KitIconProps) {
  return (
    <Icon
      type="kit"
      className={className}
      fontSize={defaultIcon.fontSize}
      width={defaultIcon.width}
    />
  );
}

const primaryPreferences: MenuItem[] = [
  {
    icon: <FaIcon icon={faObjectsColumn} />,
    label: 'dashboard',
    href: routes.home,
  },
  {
    icon: <KitIcon className="fak fa-account1" />,
    label: 'account',
    subMenus: [
      {
        icon: <FaIcon icon={faBell} />,
        label: 'notification',
        href: routes.notifications,
      },
      {
        icon: <FaIcon icon={faGear} />,
        label: 'advance',
        href: routes.advancePreferences,
      },
      {
        icon: <FaIcon icon={faFileInvoiceDollar} />,
        label: 'billing',
        href: routes.billing,
      },
      {
        icon: <FaIcon icon={faComment} />,
        label: 'feedback',
        href: routes.feedback,
      },
    ],
  },
  {
    icon: <FaIcon icon={faEnvelope} />,
    label: 'support',
    subMenus: [
      {
        icon: <FaIcon icon={faHandHoldingHand} />,
        label: 'help_center',
        href: 'https://help.stock-sync.com/support/home',
      },
      {
        icon: <FaIcon icon={faScreenUsers} />,
        label: 'video_tutorial',
        href: 'https://www.youtube.com/playlist?list=PLKVbFUxa0CwK85N-urqvsBqRCwHvp2RSC',
      },
      {
        icon: <KitIcon className="fak fa-discord" />,
        label: 'discord',
        href: 'https://discord.gg/mUkEXXuvzE',
      },
      {
        icon: <FaIcon icon={faLaptop} />,
        label: 'demo_store',
        href: 'https://help.stock-sync.com/en/article/stock-sync-demo-store-uzxw21/',
      },
    ],
  },
];

export function SideNavBar() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const matchesSm = useMatches('sm');
  const navbar = useNavbar();
  const navigate = useNavigate();
  const [, revokeAccess] = useMutation(RevokeAccess, {
    dataKey: 'revokeAccess',
  });
  const [menuQb, setMenuQb] = React.useState<HTMLButtonElement | null>(null);
  const closeMenuQb = () => setMenuQb(null);

  const [menuWoo, setMenuWoo] = React.useState<HTMLButtonElement | null>(null);
  const [menuSquare, setMenuSquare] = React.useState<HTMLButtonElement | null>(
    null
  );
  const closeMenuSquare = () => setMenuSquare(null);

  const closeMenuWoo = () => setMenuWoo(null);

  const toggleNavBarInMobile = () => {
    if (matchesSm) navbar.toggle();
  };

  const toolbarHeight = '90px'; // manually match CustomAppBar title
  return (
    <ResponsiveDrawer
      key={matchesSm ? 'mobile' : 'desktop'}
      variant={matchesSm ? 'temporary' : 'permanent'}
      anchor="left"
      open={navbar.open}
      onClose={() => navbar.toggle()}
    >
      <Toolbar
        sx={{
          justifyContent: {
            xs: navbar.open ? 'space-between' : 'center',
          },
          height: toolbarHeight,
          // override default without !important via media query
          paddingRight: { xs: '8px' },
          paddingLeft: { xs: navbar.open ? '30px' : '8px' },
        }}
      >
        <Link
          to={routes.home}
          onClick={() => toggleNavBarInMobile()}
          sx={{ display: 'flex' }}
        >
          <img height="30" src={SSLogo} alt="app-icon" />
          {navbar.open && (
            <Box sx={{ padding: '4px 0 0 8px' }}>
              <Typography variant="body1" sx={{ color: 'black' }}>
                syncX: Stock Sync
              </Typography>
            </Box>
          )}
        </Link>
        {/* Hamburger icon only appears in desktop */}
        {!matchesSm && navbar.open && (
          <IconButton
            sx={{ color: theme.palette.grey[300] }}
            onClick={() => navbar.toggle()}
          >
            <FaIcon icon={faBars} fontSize={20} />
          </IconButton>
        )}
      </Toolbar>

      <Box
        sx={{
          height: `calc(100% - ${toolbarHeight})`,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        {/* inner Box to avoid width shift on scrollbar toggle */}
        <Box
          sx={{
            position: 'absolute',
            left: 0,
            width: navbar.open ? '100%' : 'initial',
            overflowY: 'auto',
            overflowX: 'hidden',
            height: '100%',
            padding: '16px 8px 40px 24px',
            paddingLeft: navbar.open ? '24px' : '8px',
          }}
        >
          <List>
            {['quickbooks', 'woocommerce', 'square'].includes(
              currentStore.provider
            ) ? (
              <Button
                variant="text"
                onClick={(e) => {
                  switch (currentStore.provider) {
                    case 'quickbooks':
                      return setMenuQb(e.currentTarget);
                    case 'woocommerce':
                      return setMenuWoo(e.currentTarget);
                    case 'square':
                      return setMenuSquare(e.currentTarget);
                    default:
                      return null;
                  }
                }}
                sx={{
                  whiteSpace: 'normal',
                  overflowWrap: 'anywhere',
                  textAlign: navbar.open ? 'initial' : 'center',
                  cursor: 'pointer',
                }}
              >
                {navbar.open ? (
                  <span style={{ fontSize: '14px' }}>
                    {shortenShopifyStore(currentStore.shopifyDomain)}
                    &nbsp;
                  </span>
                ) : (
                  ''
                )}
                <FaIcon icon={faArrowUpRightFromSquare} fontSize={14} />
              </Button>
            ) : (
              <Link
                to={
                  renderAdminLink(
                    {
                      shopify: shortenShopifyStore(currentStore.shopifyDomain),
                      wix: currentStore.shopifyDomain,
                      squarespace: currentStore.shopifyDomain,
                      woocommerce: currentStore.shopifyDomain,
                      bigcommerce: `store-${currentStore.bigcommerceStoreHash}.mybigcommerce.com`,
                      ekm: currentStore.shopifyDomain,
                      square: currentStore.shopifyDomain,
                    }[currentStore.provider],
                    currentStore.provider
                  ) ?? ''
                }
                underline="always"
                sx={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: navbar.open ? 'flex-start' : 'center',
                  marginLeft: navbar.open ? '16px' : 0,
                }}
              >
                <NavItemText
                  sx={{
                    whiteSpace: 'normal',
                    overflowWrap: 'anywhere',
                    width: '90%',
                    textAlign: navbar.open ? 'initial' : 'center',
                  }}
                >
                  {navbar.open ? (
                    <span>
                      {shortenShopifyStore(currentStore.shopifyDomain)}
                      &nbsp;
                    </span>
                  ) : (
                    ''
                  )}
                  <FaIcon icon={faArrowUpRightFromSquare} fontSize={14} />
                </NavItemText>
              </Link>
            )}

            {Boolean(menuQb) && (
              <Menu
                elevation={1}
                id="1"
                anchorEl={menuQb}
                open
                onClose={closeMenuQb}
              >
                <MenuItem>
                  <Link to="//app.qbo.intuit.com/app/homepage">
                    {t('back_to_quickbooks_store')}
                  </Link>
                </MenuItem>
                {currentStore.quickbooksRealmId && (
                  <MenuItem
                    onClick={async () =>
                      revokeAccess(
                        {
                          provider: currentStore.provider,
                        },
                        {
                          onSuccess() {
                            window.location.href = routes.newFeed;
                          },
                        }
                      )
                    }
                  >
                    {t('disconnect')}
                  </MenuItem>
                )}

                <Divider sx={{ margin: '0 8px' }} />
                <MenuItem
                  onClick={() => {
                    window.location.href = routes.quickbookSignOutRoute;
                  }}
                  sx={{ color: '#FF0000' }}
                >
                  {t('sign_out')}
                </MenuItem>
              </Menu>
            )}

            {Boolean(menuWoo) && (
              <Menu
                elevation={1}
                id="woo"
                anchorEl={menuWoo}
                open
                onClose={closeMenuWoo}
              >
                <MenuItem>
                  <Link
                    to={
                      renderAdminLink(
                        currentStore.shopifyDomain,
                        currentStore.provider
                      ) ?? ''
                    }
                  >
                    {t('back_to_woocommerce_store')}
                  </Link>
                </MenuItem>
                <Divider sx={{ margin: '0 8px' }} />
                <MenuItem
                  onClick={() => {
                    window.location.href = routes.woocommerceSignOutRoute;
                  }}
                  sx={{ color: '#FF0000' }}
                >
                  {t('sign_out')}
                </MenuItem>
              </Menu>
            )}
            {Boolean(menuSquare) && (
              <Menu
                elevation={1}
                id="woo"
                anchorEl={menuSquare}
                open
                onClose={closeMenuSquare}
              >
                <MenuItem>
                  <Link
                    to={
                      renderAdminLink(
                        currentStore.shopifyDomain,
                        currentStore.provider
                      ) ?? ''
                    }
                  >
                    {t('back_to_square_store')}
                  </Link>
                </MenuItem>
                {currentStore.squareLocationId && (
                  <MenuItem
                    onClick={async () =>
                      revokeAccess(
                        {
                          provider: currentStore.provider,
                        },
                        {
                          onSuccess() {
                            window.location.href = routes.newFeed;
                          },
                        }
                      )
                    }
                  >
                    {t('disconnect')}
                  </MenuItem>
                )}
                <Divider sx={{ margin: '0 8px' }} />
                <MenuItem
                  onClick={() => {
                    window.location.href = routes.squareSignOutRoute;
                  }}
                  sx={{ color: '#FF0000' }}
                >
                  {t('sign_out')}
                </MenuItem>
              </Menu>
            )}
            <Box sx={{ marginTop: '16px' }} />
            {navbar.open ? (
              <Button
                disabled={currentStore.isShowQuickGuide}
                variant="outlined"
                startIcon={<FaIcon icon={faCirclePlus} />}
                sx={{
                  width: '100%',
                  minWidth: '20px',
                  backgroundColor: theme.palette.primary.contrastText,
                  border: 'none',
                  '&:hover': {
                    backgroundColor: theme.palette.primary.contrastText,
                    borderWidth: 1,
                    border: 'none',
                  },
                }}
                onClick={() => navigate(routes.newFeed)}
              >
                <Typography variant="body1">{t('create_new')}</Typography>
              </Button>
            ) : (
              <Button
                variant="outlined"
                sx={{
                  width: '100%',
                  minWidth: '20px',
                  borderRadius: '8px',
                  border: '1px solid',
                  '&:hover': {
                    border: '1px solid',
                  },
                  padding: '4px 0 0 0',
                }}
                onClick={() => navigate(routes.newFeed)}
              >
                <FaIcon icon={faCirclePlus} />
              </Button>
            )}
          </List>
          <List>
            {primaryPreferences.map((props) => (
              <PrimaryListItem key={props.label} {...props} />
            ))}
          </List>
          {/* Secondary Preferences */}
          {navbar.open && <CurrentPlan />}
        </Box>
      </Box>
    </ResponsiveDrawer>
  );
}

const CurrentPlan = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [openBilling, setOpenBilling] = React.useState(false);
  const toggleCollapseBilling = () => setOpenBilling(!openBilling);

  const { currentStore } = useCurrentStore();

  const renderPackage = () => {
    switch (currentStore.package) {
      case 'Snappy':
        return <span>Free Plan</span>;
      case 'Trial':
        return <span>Full Access {currentStore.fullPlanName} Plan</span>;
      default:
        return (
          <span>
            {currentStore.fullPlanName} Plan{' '}
            {currentStore.isExpired && (
              <span style={{ color: theme.palette.red[500] }}>(Expired)</span>
            )}
          </span>
        );
    }
  };
  const totalUpdateFeeds =
    currentStore.feedsCountByType && currentStore.feedsCountByType.update
      ? currentStore.feedsCountByType.update
      : 0;

  return (
    <Paper
      sx={{
        background: '#6E6D7A0D',
        marginTop: '40px',
        padding: '18px 14px 16px',
      }}
    >
      <Stack
        sx={{
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
        }}
      >
        <NavItemText
          sx={{ color: theme.palette.grey[300], whiteSpace: 'break-spaces' }}
        >
          {currentStore.currentPlan.isCustomPlan
            ? t('custom_plan')
            : renderPackage()}
        </NavItemText>
        <Link to={routes.billing}>{t('manage_plan')}</Link>
        <Button
          variant="text"
          onClick={toggleCollapseBilling}
          sx={{
            paddingLeft: 0,
            paddingRight: 0,
          }}
          endIcon={
            <FaIcon
              id={openBilling ? 'on-billing-detail' : 'off-billing-detail'}
              icon={openBilling ? faChevronUp : faChevronDown}
              fontSize={theme.typography.body2.fontSize}
              color={theme.palette.grey[300]}
            />
          }
        >
          <Typography
            variant="body2"
            sx={{ paddingRight: '8px', color: theme.palette.grey[300] }}
          >
            {openBilling ? t('show_less') : t('view_detail')}
          </Typography>
        </Button>
      </Stack>
      <Collapse in={openBilling}>
        <List component="div" disablePadding>
          <Typography
            variant="body2"
            sx={{ color: theme.palette.grey[300], paddingTop: '8px' }}
          >
            {t('total_number_of_variants')}
          </Typography>
          <Typography variant="body2">
            {numberFormat(currentStore.totalSkusCount)} /{' '}
            {numberFormat(currentStore.limitSkus)}
          </Typography>
          {currentStore.monthlyCreditPlan && (
            <>
              <Typography
                variant="body2"
                sx={{ color: theme.palette.grey[300], paddingTop: '8px' }}
              >
                {t('stock_sync_monthly_credits')}
              </Typography>
              <Typography variant="body2">
                {numberFormat(Number(currentStore.monthlyCredit))}
              </Typography>
            </>
          )}
          <Typography
            variant="body2"
            sx={{ color: theme.palette.grey[300], paddingTop: '8px' }}
          >
            {t('feed_limit_used')}
          </Typography>
          <Typography variant="body2">
            {`${totalUpdateFeeds} / ${currentStore.profileLimit}`}
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: theme.palette.grey[300], paddingTop: '8px' }}
          >
            {t('stock_sync_credit')}
          </Typography>
          <Typography variant="body2">
            {numberFormat(currentStore.importLimitSkus)}
          </Typography>
        </List>
      </Collapse>
    </Paper>
  );
};

interface NavItemTextProps extends Pick<TypographyProps, 'children' | 'sx'> {}

function NavItemText({ children, sx }: NavItemTextProps) {
  return (
    <Typography
      variant="body1"
      sx={{
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        ...sx,
      }}
    >
      {children}
    </Typography>
  );
}

interface MenuItem {
  label: string;
  icon?: React.ReactNode;
  href?: string;
  subMenus?: MenuItem[];
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}

interface NavItemButtonProps
  extends Pick<ListItemButtonProps, 'onClick' | 'sx' | 'selected'> {
  icon?: React.ReactNode;
  label: React.ReactNode;
  subMenus?: MenuItem[];
  open?: boolean;
  href?: ListItemButtonProps['to'];
}

function NavItemButton({
  onClick,
  icon,
  label,
  subMenus,
  sx,
  open = false,
  selected,
  href,
}: NavItemButtonProps) {
  const navbar = useNavbar();
  const matchesSm = useMatches('sm');
  return (
    <ListItemButton
      component={href ? Link : 'div'}
      to={href}
      disableRipple
      onClick={(e) => {
        if (matchesSm && !subMenus) {
          navbar.toggle();
        }
        onClick?.(e);
      }}
      selected={selected}
      sx={{
        paddingLeft: navbar.open ? '16px' : 0,
        paddingRight: navbar.open ? '16px' : 0,
        ...sx,
      }}
    >
      <ListItemIcon
        sx={
          navbar.open
            ? {}
            : {
                display: 'flex',
                width: '100%',
                justifyContent: 'center',
              }
        }
      >
        {subMenus || !navbar.open || href === routes.home ? icon : ''}
      </ListItemIcon>
      {navbar.open && (
        <>
          <ListItemText primary={<NavItemText>{label}</NavItemText>} />
          {subMenus && (
            <FaIcon
              id={open ? `${label}-on-subnav` : `${label}-off-subnav`}
              icon={open ? faChevronUp : faChevronDown}
            />
          )}
        </>
      )}
    </ListItemButton>
  );
}

interface SubMenuProps {
  open: CollapseProps['in'];
  items: MenuItem[];
}

function SubMenu({ open, items = [] }: SubMenuProps) {
  const { t } = useTranslation();
  const navbar = useNavbar();
  const { pathname } = useLocation();

  return items.length > 0 ? (
    <Collapse in={open}>
      <List component="div" disablePadding>
        {items.map(({ icon, label, href, onClick }) => (
          <NavItemButton
            key={label}
            href={href}
            onClick={onClick}
            icon={icon}
            label={t(label)}
            sx={{ paddingLeft: navbar.open ? '14px' : 0 }}
            selected={href ? pathname.includes(href) : false}
          />
        ))}
      </List>
    </Collapse>
  ) : (
    <></>
  );
}

interface PrimaryListItemProps
  extends Pick<NavItemButtonProps, 'icon' | 'href' | 'subMenus' | 'label'> {
  //
}

function PrimaryListItem({
  icon,
  label,
  href,
  subMenus,
}: PrimaryListItemProps) {
  const { t } = useTranslation();
  const navbar = useNavbar();
  const [open, setOpen] = React.useState(false);
  const toggleCollapse = () => setOpen(!open);
  const { pathname } = useLocation();

  const subMenuItems = React.useMemo(
    () => (subMenus || []).filter((m) => m.href),
    [subMenus]
  );

  // keep active menu open, else close, like accordion
  React.useEffect(() => {
    const isActiveSubMenu = subMenuItems.some((item) =>
      pathname.includes(item.href ?? '')
    );
    setOpen(isActiveSubMenu);
  }, [subMenuItems, pathname]);

  return (
    <Box sx={{ marginBottom: navbar.open ? 0 : '8px' }}>
      <NavItemButton
        href={href}
        onClick={() => toggleCollapse()}
        icon={icon}
        label={typeof label === 'string' ? t(label) : label}
        subMenus={subMenus}
        open={open}
        selected={pathname === href}
      />
      <SubMenu open={open} items={subMenuItems} />
    </Box>
  );
}
