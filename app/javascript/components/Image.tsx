import { Box, type BoxProps } from '@mui/material';

export interface ImageProps
  extends Pick<BoxProps, 'sx'>,
    React.DetailedHTMLProps<
      React.ImgHTMLAttributes<HTMLImageElement>,
      HTMLImageElement
    > {}

export function Image({ sx, ...imageProps }: ImageProps) {
  return (
    <Box sx={{ display: 'inline-flex', ...sx }}>
      <img style={{ width: '100%' }} {...imageProps} />
    </Box>
  );
}
