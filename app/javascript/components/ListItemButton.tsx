import {
  ListItemButton as MuiListItemButton,
  useTheme,
  type ListItemButtonProps as MuiListItemButtonProps,
} from '@mui/material';

import { type LinkProps } from '@/components/Link';

export interface ListItemButtonProps extends MuiListItemButtonProps {
  to?: LinkProps['to'];
}

export function ListItemButton({ sx, ...props }: ListItemButtonProps) {
  const theme = useTheme();
  return (
    <MuiListItemButton
      sx={{
        borderRadius: '8px',
        color: theme.palette.primary.main,
        '&:hover': {
          color: theme.palette.primary.main,
          background: '#6E6D7A26',
        },
        '&.Mui-selected': {
          color: theme.palette.primary.contrastText,
          background: theme.palette.primary.main,
          '&:hover': {
            color: theme.palette.primary.contrastText,
            background: theme.palette.primary.main,
          },
        },
        ...sx,
      }}
      {...props}
    />
  );
}
