import {
  Box,
  Slider as Mu<PERSON><PERSON><PERSON><PERSON>,
  Stack,
  Typography,
  useTheme,
  type BoxProps,
  type SliderProps as MuiSliderProps,
} from '@mui/material';
import omit from 'lodash/omit';
import { type ReactNode } from 'react';
import {
  useController,
  type FieldValues,
  type UseControllerProps,
} from 'react-hook-form';

import { Card } from '@/components/Card';

interface SliderProps<TFieldValues extends FieldValues>
  extends Pick<UseControllerProps<TFieldValues>, 'name' | 'control'>,
    Omit<MuiSliderProps, 'name' | 'ref'>,
    Pick<BoxProps, 'sx'> {
  label?: ReactNode;
  helperText?: ReactNode;
}

export function Slider<TFieldValues extends FieldValues>({
  name,
  control,
  label,
  helperText,
  sx,
  onChange,
  ...props
}: SliderProps<TFieldValues>) {
  const {
    field,
    formState: { isSubmitting },
  } = useController({
    name,
    control,
  });
  const theme = useTheme();

  return (
    <Stack spacing="40px">
      {label && <Typography variant="h5">{label}</Typography>}
      <Box sx={sx}>
        <MuiSlider
          valueLabelDisplay="on"
          disabled={isSubmitting}
          slots={{
            markLabel: (props) => {
              const { ownerState, style, ...restProps } = omit(props, [
                'markLabelActive',
              ]);
              const index = props['data-index'];
              const isFirstIndex = index === 0;
              const isLastIndex = index === ownerState.marks.length - 1;
              const translateX =
                (isFirstIndex && '0%') || (isLastIndex && '-100%') || '-50%';

              return (
                <span
                  {...restProps}
                  style={{
                    position: 'absolute',
                    color: theme.palette.grey[300],
                    transform: `translate(${translateX},15px)`,
                    whiteSpace: 'nowrap',
                    ...style,
                  }}
                />
              );
            },
          }}
          {...props}
          {...field}
          value={field.value ?? props.min ?? props.marks?.[0].value ?? 0}
          onChange={(event, value, activeThumb) => {
            field.onChange(event);
            onChange?.(event, value, activeThumb);
          }}
        />
        <Card
          sx={{
            backgroundColor: '#6E6D7A0D',
            padding: 0,
          }}
        >
          {helperText}
        </Card>
      </Box>
    </Stack>
  );
}
