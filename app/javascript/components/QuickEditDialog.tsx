import { TextField as Mu<PERSON><PERSON><PERSON><PERSON><PERSON>ield } from '@mui/material';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components/Button';
import { Dialog } from '@/components/Dialog';

interface QuickEditDialogProps {
  value: string;
  open: boolean;
  onClose: () => void;
  onSubmit: (newValue: string) => void;
  title?: string;
  cancelText?: string;
  okText?: string;
}

export function QuickEditDialog({
  value,
  open = false,
  onClose,
  onSubmit,
  title = 'Quick edit',
  cancelText = 'cancel',
  okText = 'done',
}: QuickEditDialogProps) {
  const { t } = useTranslation();
  const editValue = React.useRef(''); // don't trigger rerenders, just store

  return (
    <Dialog
      maxWidth="lg"
      slotProps={{ paper: { sx: { width: '100%' } } }}
      open={open}
      onClose={onClose}
    >
      <Dialog.Title>{title}</Dialog.Title>
      <Dialog.Content>
        {/* TODO:  
          Rhf TextField super lag on key down in this use case. To investigate.
          Use MuiTextField for now. 
          Hmm, this is throw-away field, maybe don't need rhf form state...
       */}
        <MuiTextField
          sx={{ padding: '1px 0' }}
          autoFocus
          fullWidth
          multiline
          defaultValue={value}
          onChange={({ target: { value } }) => (editValue.current = value)}
        />
      </Dialog.Content>
      <Dialog.Actions>
        <Button variant="outlined" size="small" onClick={onClose}>
          {t(cancelText)}
        </Button>
        <Button
          size="small"
          onClick={() => {
            // return truthy stored value
            if (editValue.current) onSubmit?.(editValue.current);
            onClose?.();
          }}
        >
          {t(okText)}
        </Button>
      </Dialog.Actions>
    </Dialog>
  );
}
