import {
  Box,
  Table as MuiTable,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  useTheme,
  type TableProps as MuiTableProps,
  type TableBodyProps,
  type TableCellProps,
  type TableContainerProps,
  type TableHeadProps,
  type TableRowProps,
  type TableSortLabelProps,
} from '@mui/material';
import { useAtom } from 'jotai';
import { atomWithImmer } from 'jotai-immer';
import orderBy from 'lodash/orderBy';
import * as React from 'react';
import { TableVirtuoso, type TableVirtuosoProps } from 'react-virtuoso';

import { Paper } from '@/components/Paper';
import type { LiteralUnion } from '@/types';

const _Table = React.forwardRef<HTMLTableElement, MuiTableProps>(
  ({ sx, ...props }, ref) => (
    <MuiTable
      ref={ref}
      sx={{ borderCollapse: 'separate', tableLayout: 'fixed', ...sx }}
      {...props}
    />
  )
);

// use Object.assign - direct assignment `Table.Head = TableHead` breaks HMR
const Table = Object.assign(
  ({ sx, ...props }: MuiTableProps) => {
    const theme = useTheme();
    return (
      <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
        <_Table
          sx={{ borderColor: theme.palette.grey[400], ...sx }}
          {...props}
        />
      </TableContainer>
    );
  },
  {
    Virtual: VirtualTable,
    SortLabel: React.forwardRef<HTMLSpanElement, TableSortLabelProps>(
      (props, ref) => <TableSortLabel ref={ref} {...props} />
    ),
    Head: React.forwardRef<HTMLTableSectionElement, TableHeadProps>(
      ({ sx, ...props }, ref) => (
        <TableHead ref={ref} sx={{ ...sx }} {...props} />
      )
    ),
    Body: React.forwardRef<HTMLTableSectionElement, TableBodyProps>(
      (props, ref) => <TableBody ref={ref} {...props} />
    ),
    Row: React.forwardRef<HTMLTableRowElement, TableRowProps>((props, ref) => (
      <TableRow ref={ref} {...props} />
    )),
    Cell: function TableCell_({
      sx,
      children,
      width,
      ...props
    }: TableCellProps) {
      const theme = useTheme();
      return (
        <TableCell
          sx={{
            borderColor: theme.palette.grey[400],
            backgroundColor: theme.palette.common.white,
            whiteSpace: 'nowrap',
            width,
            ...sx,
          }}
          {...props}
        >
          <Box
            sx={{
              // with fixed column width, text elements truncate
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {children}
          </Box>
        </TableCell>
      );
    },
  }
);

const expandedRowsAtom = atomWithImmer<Record<any, boolean>>({});

export interface Column<Data> {
  dataKey: LiteralUnion<keyof Data, string>;
  title?: React.ReactNode;
  width?: React.CSSProperties['width'];
  sortProps?: Array<Column<Data>['dataKey']>;
  sortDirections?: Array<'asc' | 'desc'>;
  render?: (
    props: Omit<Column<Data>, 'render'> & {
      sortBy: (
        sortProps: Column<Data>['sortProps'],
        sortDirections: Column<Data>['sortDirections']
      ) => void;
    }
  ) => React.ReactNode;
}

export type ColumnRenderProps<Data = unknown> = Parameters<
  Exclude<Column<Data>['render'], undefined>
>[0];

export interface RenderRowProps<Data, Context = unknown> {
  index: number;
  data: Data;
  context: Context;
  columns: Column<Data>[];
  expanded: boolean;
  /** Toggles row by given key */
  onExpanded: (rowKey: any) => void;
}

export interface VirtualTableProps<Data, Context>
  extends Omit<
    TableVirtuosoProps<Data, Context>,
    'itemContent' | 'fixedHeaderContent'
  > {
  columns: Column<Data>[];
  rowCells?: (props: RenderRowProps<Data, Context>) => React.ReactNode;
  expandedRowCells?: (props: RenderRowProps<Data, Context>) => React.ReactNode;
}

interface TableVirtuosoContext {
  TableProps: MuiTableProps;
  /** Required if `renderExpandedRow` provided  */
  rowKey?: any;
}

// why can't inline tableComponents?
// ...a new instance is passed with each re-render.
// Move the definition out of the component rendering.
// https://github.com/petyosi/react-virtuoso/issues/566#issuecomment-1035950859
const tableVirtuosoComponents = {
  Scroller: React.forwardRef<HTMLDivElement, TableContainerProps>(
    ({ sx, ...props }, ref) => (
      <TableContainer
        ref={ref}
        component={Paper}
        sx={{ boxShadow: 'none', ...sx }}
        {...props}
      />
    )
  ),
  Table: (props) => (
    <_Table
      {...props}
      {...(props.context as TableVirtuosoContext)?.TableProps}
    />
  ),
  TableHead: Table.Head,
  TableRow: Table.Row,
  TableBody: Table.Body,
};

// https://mui.com/material-ui/react-table/#virtualized-table
// https://virtuoso.dev/mui-table-virtual-scroll/
function VirtualTable<Data, Context extends TableVirtuosoContext>({
  data,
  rowCells,
  expandedRowCells,
  columns,
  ...props
}: VirtualTableProps<Data, Context>) {
  const [expandedRows, setExpandedRows] = useAtom(expandedRowsAtom);

  const [sortProps, setSortProps] = React.useState<Column<Data>['sortProps']>();
  const [sortDirections, setSortDirections] =
    React.useState<Column<Data>['sortDirections']>();
  const sortedData = React.useMemo(
    () => orderBy(data, sortProps, sortDirections),
    [data, sortProps, sortDirections]
  );

  // TODO: future - explore other way than doubling array size?
  const withExpandedRowData = React.useMemo(() => {
    const withExpandableRows: Data[] = [];
    sortedData?.forEach((o) => {
      withExpandableRows.push(o); // regular row data
      if (expandedRowCells) withExpandableRows.push(o); // expandable row data
    });
    return withExpandableRows;
  }, [sortedData, expandedRowCells]);

  const onExpanded = React.useCallback(
    (key) =>
      setExpandedRows((prev) => {
        prev[key] = !prev[key];
      }),
    [setExpandedRows]
  );

  return (
    <TableVirtuoso
      {...props}
      data={withExpandedRowData}
      style={{
        height: props.style?.height ?? '400px', // required
      }}
      components={tableVirtuosoComponents}
      fixedHeaderContent={() => (
        <Table.Row>
          {columns.map(({ title, dataKey, width, render }, index) => (
            <React.Fragment key={`${String(dataKey)}-${index}`}>
              {render ? (
                render({
                  title,
                  dataKey,
                  width,
                  sortProps,
                  sortDirections,
                  sortBy: (newProps, newDirections) => {
                    setSortProps(newProps);
                    setSortDirections(newDirections);
                  },
                })
              ) : (
                <Table.Cell width={width}>{title}</Table.Cell>
              )}
            </React.Fragment>
          ))}
        </Table.Row>
      )}
      itemContent={(index, ...props) => {
        const [data, context] = props;
        const isExpandedRow = index % 2 !== 0;
        const expanded = expandedRows[data[context.rowKey]];

        if (expandedRowCells && !context.rowKey) {
          console.error('No row key provided in context for expandable rows');
        }

        return (
          (context.rowKey &&
            isExpandedRow &&
            expandedRowCells &&
            expandedRowCells({
              index,
              data,
              context,
              columns,
              expanded,
              onExpanded,
            })) ||
          (rowCells &&
            rowCells({
              index,
              data,
              context,
              columns,
              expanded,
              onExpanded,
            })) ||
          // default - render value by dataKey
          columns.map(({ dataKey }) => (
            <Table.Cell key={String(dataKey)}>
              {data[String(dataKey)]}
            </Table.Cell>
          ))
        );
      }}
    />
  );
}

export { Table };
