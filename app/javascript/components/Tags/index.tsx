import {
  Box,
  FormControl,
  InputLabel,
  type InputLabelProps,
} from '@mui/material';
import { type TagData } from '@yaireo/tagify';
import TagifyTagsReact, {
  type TagifyTagsReactProps,
} from '@yaireo/tagify/dist/react.tagify';

// https://github.com/yairEO/tagify#react
import '@yaireo/tagify/dist/tagify.css';

import { useTheme } from '@mui/material';
import * as React from 'react';
import * as ReactDOMServer from 'react-dom/server';
import {
  useController,
  useFormContext,
  type FieldValues,
  type UseControllerProps,
  type UseControllerReturn,
} from 'react-hook-form';

import {
  ErrorHelperText,
  type ErrorHelperTextProps,
} from '@/components/ErrorHelperText';
import { commaNotInsideQuotes, compareRegexAsString } from '@/shared/util';

import './index.css'; // requires global styles https://github.com/yairEO/tagify#suggestions-dropdown-css-variables

interface RenderOptionProps {
  tabIndex: number;
  role: React.AriaRole;
  className: string;
  style: React.CSSProperties;
  value: string;
}

export type TagsProps<TFieldValues extends FieldValues> = {
  disabled?: boolean;
  validateOnChange?: boolean;
  label?: InputLabelProps['children'];
  onChange?: UseControllerReturn['field']['onChange'];
  /**
   * Custom render dropdown item from whitelist. 
   * **Mui sx={{..}} won't work, only inline style={...} works.**
   * Example:
   * ```jsx
    <Tags
      renderOption={(elementProps, whitelistItem) => (
        <div {...elementProps}>
          <Typography 
            component="div" 
            style={theme.typography.body1}>
            {whitelistItem.customText} 
          </Typography>
        </div>
      )}
      // whitelist of objects
      whitelist={list.map((o) => ({
        value: o.key, // `value` required by lib
        customText: o.value, // add any custom prop 
      }))}
    />
   * ```
   */
  renderOption?: (
    elementProps: RenderOptionProps,
    whitelistItem: TagData
  ) => React.ReactElement;
} & Pick<UseControllerProps<TFieldValues>, 'name' | 'control'> &
  Omit<TagifyTagsReactProps, 'tagifyRef' | 'onChange'> &
  Pick<ErrorHelperTextProps, 'helperText' | 'helperTextProps'>;

export function Tags<TFieldValues extends FieldValues>({
  name,
  control,
  onChange,
  helperText,
  helperTextProps,
  label,
  settings,
  whitelist,
  renderOption,
  validateOnChange = true,
  ...props
}: TagsProps<TFieldValues>) {
  const theme = useTheme();
  const ref: TagifyTagsReactProps['tagifyRef'] = React.useRef();
  const {
    field: { ref: fieldRef, ...field },
    formState: { errors, isSubmitting },
  } = useController({
    name,
    control,
  });

  // if available, use nested errors - e.g. name=`array.0.key`
  const { getFieldState, trigger } = useFormContext() || {};
  const hasNestedError = getFieldState?.(name)?.invalid ?? false;
  // else use normal formState.errors
  const hasNormalError = Boolean(name ? errors[name] : false);
  const hasError = hasNestedError || hasNormalError;

  // tagifyRef={fieldRef} will error. manually integrate tagifyRef with fieldRef
  React.useEffect(() => {
    if (ref.current) fieldRef(ref.current.DOM.input);
  }, [ref, fieldRef]);

  const [focus, setFocus] = React.useState(false);

  return (
    <FormControl variant="outlined" fullWidth>
      {label && (
        <InputLabel
          shrink
          sx={{
            backgroundColor: theme.palette.common.white,
            padding: '0 16px 0 6px',
            left: '-6px',
            color:
              (hasError && theme.palette.error.main) ||
              (focus && 'rgb(0 0 0 / 87%)') ||
              'rgb(0 0 0 / 60%)',
          }}
        >
          {label}
        </InputLabel>
      )}
      <Box
        // style to match Mui TextField
        sx={{
          '.tags-input .tagify': {
            width: '100%',
            minHeight: '56px',
            padding: '7px',
            display: 'flex',
            alignItems: 'flex-start',
            fontSize: '16px',
            borderRadius: '4px',
            borderColor: hasError
              ? theme.palette.error.main
              : 'rgb(0 0 0 / 23%)',
            '&:hover': {
              borderColor: hasError
                ? theme.palette.error.main
                : 'rgb(0 0 0 / 87%)',
            },
            '&.tagify--focus': {
              outline: '1px solid',
              outlineColor: hasError
                ? theme.palette.error.main
                : theme.palette.primary.main,
              borderColor: hasError
                ? theme.palette.error.main
                : theme.palette.primary.main,
              '*::before': {
                whiteSpace: 'normal',
              },
            },
            // align center loading spinner
            '&.tagify--loading .tagify__input': {
              '--loader-size': '1.1rem',
              '&:after': { margin: 0 },
            },
            '&.tagify--empty': {
              '*::before': {
                whiteSpace: 'normal',
              },
            },
            '--tag-bg': theme.palette.grey[200],
            // no "see-through" transparent hole when parent width squashed
            '--tag-inset-shadow-size': '100vh',
            tag: {
              x: {
                minWidth: 14,
              },
              // why? coz urls will overflow parent containers
              '> div > *': {
                wordBreak: 'break-all',
              },
            },
          },
        }}
      >
        <TagifyTagsReact
          key={name} // important! to avoid stale props like onChange
          {...field}
          {...props}
          whitelist={whitelist} // dynamic whitelist https://github.com/yairEO/tagify#faq
          settings={{
            hooks: {
              // bug: quickly delete last tag and paste new tag - keeps deleted tag
              // similar bug report https://github.com/yairEO/tagify/issues/1127
              // possibly related bug / fix https://github.com/yairEO/tagify/issues/1125
              // cause: the paste comes too fast after a delete, suspect library race condition
              // workaround: delay paste event with timeout
              beforePaste(_, beforePasteData) {
                return new Promise<string>((resolve) => {
                  setTimeout(() => resolve(beforePasteData.pastedText), 200);
                });
              },
            },
            editTags: { clicks: 1 },
            // if we provide originalInputValueFormat, https://github.com/yairEO/tagify#modify-original-input-value-format
            // JSON.stringify(value) will not run, `\n` is preserved https://github.com/yairEO/tagify/blob/1edeb579b18ea61693e0116281584fa5543f1b18/src/tagify.js#L1699
            // theory: `\n` becomes `\\n` after JSON.stringify, causing it to be invalid & stripped?
            originalInputValueFormat: (tagDataArray) =>
              tagDataArray.map((o) => o.value).join(','),
            dropdown: {
              enabled: 1,
            },
            templates: {
              // https://github.com/yairEO/tagify/blob/master/src/parts/templates.js#L58
              dropdownItem(item) {
                // common props, also pass to custom renderOption
                const elementProps: RenderOptionProps = {
                  tabIndex: 0,
                  role: 'option',
                  className: this.settings.classNames.dropdownItem,
                  style: { maxHeight: 'unset' }, // lib max-height: 60px;
                  value: item.value, // lib uses internally, same as this.getAttributes(item)
                };

                // compose default as jsx
                const defaultTemplate = (
                  <div {...elementProps}>{item.mappedValue || item.value}</div>
                );

                // can only return string, so convert from jsx to string
                return ReactDOMServer.renderToString(
                  renderOption
                    ? renderOption(elementProps, item)
                    : defaultTemplate
                );
              },
            },
            ...settings,
          }}
          name={name}
          readOnly={isSubmitting}
          tagifyRef={ref}
          onFocus={() => setFocus(true)}
          onBlur={() => setFocus(false)}
          onChange={(customEvent) => {
            const arrayOfTagObjects = customEvent.detail.tagify.value;

            const delimiter = compareRegexAsString(
              customEvent.detail.tagify.settings.delimiters
            );

            const commaNotInsideQuotesRegexString =
              compareRegexAsString(commaNotInsideQuotes);

            let delimiterSeparatedValues = '';

            delimiterSeparatedValues = arrayOfTagObjects
              .map((o) => o.value)
              .join(delimiter.length > 3 ? ',' : delimiter);

            if (
              delimiter !== '++' &&
              delimiter !== commaNotInsideQuotesRegexString
            ) {
              delimiterSeparatedValues = delimiterSeparatedValues
                .replace('"', '') // remove double quote "
                .replace('“', ''); // remove slanted double quote “
            }

            const syntheticEvent = {
              target: { value: delimiterSeparatedValues },
            };
            field.onChange(syntheticEvent);
            onChange?.(syntheticEvent);
            if (validateOnChange) trigger?.(name);
          }}
        />
        <ErrorHelperText
          name={name}
          helperText={helperText}
          errors={errors}
          helperTextProps={helperTextProps}
        />
      </Box>
    </FormControl>
  );
}
