/* requires global styles https://github.com/yairEO/tagify#suggestions-dropdown-css-variables */

:root {
  --tagify-dd-color-primary: rgba(0, 0, 0, 0.04);
  --tagify-dd-bg-color: white;
  --tagify-dd-text-color: black;
}

.tagify__dropdown {
  border-radius: 8px;
  box-shadow:
    0px 2px 1px -1px rgba(0, 0, 0, 0.2),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14),
    0px 1px 3px 0px rgba(0, 0, 0, 0.12);
}

.tagify__dropdown__wrapper {
  padding: 4.2px 5px;
}

.tagify__dropdown__item {
  padding: 8px;
  border-radius: 8px;
  max-height: unset;
  color: var(--tagify-dd-text-color);
}

.tagify__tag > div > [contenteditable] {
  max-width: fit-content;
}
