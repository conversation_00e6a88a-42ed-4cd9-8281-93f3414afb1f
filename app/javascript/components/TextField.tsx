import {
  CircularProgress,
  InputAdornment,
  TextField as Mui<PERSON><PERSON><PERSON><PERSON>ield,
  type BaseSelectProps,
  type TextFieldProps as MuiTextFieldProps,
} from '@mui/material';
import * as React from 'react';
import {
  useController,
  useFormContext,
  type FieldValues,
  type UseControllerProps,
} from 'react-hook-form';

import { ErrorHelperText } from '@/components/ErrorHelperText';

export interface TextFieldProps<TFieldValues extends FieldValues = FieldValues>
  extends Pick<UseControllerProps<TFieldValues>, 'name' | 'control' | 'rules'>,
    Omit<
      MuiTextFieldProps,
      | 'name'
      | 'ref'
      // omit @deprecated:
      | 'FormHelperTextProps'
      | 'InputLabelProps'
      | 'inputProps'
      | 'InputProps'
      | 'SelectProps'
    > {}

export function TextField<TFieldValues extends FieldValues>({
  name,
  control,
  rules,
  helperText,
  onChange,
  onBlur,
  defaultValue,
  sx,
  ...props
}: TextFieldProps<TFieldValues>) {
  const {
    // for focus to work, destructure ref https://github.com/react-hook-form/react-hook-form/issues/3784#issuecomment-752035020
    field: { ref: fieldRef, ...field },
    formState: controllerFormState,
  } = useController({
    name,
    control,
    rules,
  });

  // if available, use nested errors - e.g. name=`array.0.key`
  const {
    getFieldState,
    setValue,
    formState: contextFormState,
  } = useFormContext<TFieldValues>() || {};

  const { errors, isSubmitting, isValidating } =
    contextFormState || controllerFormState;

  const hasNestedError = getFieldState?.(name)?.invalid ?? false;
  // else use normal formState.errors
  const hasNormalError = Boolean(errors[name]);

  // we are using useEffect because rhf is controlling form state
  // if we update MuiTextField.value directly, out of sync with rhf form state
  // we let rhf update form state and cascade value
  // down to MuiTextField via value={field.value}
  // if `defaultValue` provided & field.value falsy, update field.value
  React.useEffect(() => {
    if (field.value) return; // already has value, abort
    if (defaultValue) setValue?.(name, defaultValue as any);
  }, [field, defaultValue, setValue, name]);

  // if `select` true & `field.value` not found in options,
  // use `defaultValue` if provided and found in options
  type SelectProps = BaseSelectProps;

  const optionValues = React.useMemo(
    () =>
      ((props.children || []) as JSX.Element[]).map(
        (child) => (child.props as SelectProps)?.value
      ),
    [props.children]
  );

  React.useEffect(() => {
    if (!props.select) return; // not select, abort
    if (!defaultValue) return; // no defaultValue, abort
    const invalidFieldValue = !optionValues.includes(field.value);
    const validDefaultValue = optionValues.includes(defaultValue);
    if (invalidFieldValue && validDefaultValue) {
      setValue(field.name, defaultValue as any);
    }
  }, [props.select, field, setValue, defaultValue, optionValues]);

  return (
    <MuiTextField
      fullWidth
      error={hasNestedError || hasNormalError}
      helperText={
        <ErrorHelperText name={name} helperText={helperText} errors={errors} />
      }
      disabled={isSubmitting}
      slotProps={{
        formHelperText: { sx: { margin: 0 } },
        htmlInput: { readOnly: isValidating },
        input: {
          sx: { opacity: isValidating ? 0.5 : 1 }, // style like disabled
          endAdornment: isValidating && (
            <InputAdornment
              sx={{ paddingRight: props.select ? '12px' : 0 }}
              position={props.select ? 'start' : 'end'}
            >
              <CircularProgress size={18} />
            </InputAdornment>
          ),
        },
      }}
      sx={{
        '.MuiInputBase-root': {
          fontSize: '16px',
        },
        // remove up/down arrow from number text field
        '& input[type=number]': {
          MozAppearance: 'textfield',
        },
        '& input[type=number]::-webkit-inner-spin-button': {
          appearance: 'none',
        },
        ...sx,
      }}
      {...props}
      name={field.name}
      value={field.value ?? ''} // always controlled
      onChange={(e) => {
        field.onChange(e);
        onChange?.(e);
      }}
      onBlur={onBlur ?? field.onBlur}
      // scrolling changes values unexpectedly, prevent by blur https://github.com/mui/material-ui/issues/7960
      onWheel={(event) => {
        if (event.target instanceof HTMLElement) {
          event.target.blur();
        }
      }}
      inputRef={fieldRef}
    />
  );
}
