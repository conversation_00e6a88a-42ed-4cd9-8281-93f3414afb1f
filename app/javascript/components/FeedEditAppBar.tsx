import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { Stack, useMediaQuery, useTheme } from '@mui/material';
import * as React from 'react';
import { type FieldValues, type SubmitHandler } from 'react-hook-form';

import { Button, type ButtonProps } from '@/components/Button';
import { CustomAppBar } from '@/components/CustomAppBar';
import { FeedNameAndType } from '@/components/FeedNameAndType';
import { Icon } from '@/components/Icon';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useFeedRenderProps } from '@/hooks/useFeedRenderProps';
import type { MutationOptions } from '@/hooks/useMutation';
import * as routes from '@/routes';

interface OnSubmitFeedProps<T extends FieldValues> extends MutationOptions<T> {
  onSkip: (props: { data: T }) => void;
}

export type OnSubmit<T extends FieldValues> = (
  props: OnSubmitFeedProps<T>
) => SubmitHandler<T>;

interface FeedEditAppBarProps
  extends Omit<
    React.ComponentPropsWithoutRef<typeof CustomAppBar>,
    'btnGroup' | 'children' | 'sx'
  > {
  backButton?: React.ReactNode;
  nextButton?: React.ReactNode;
}

function FeedEditAppBar_({
  backButton,
  nextButton,
  ...props
}: FeedEditAppBarProps) {
  const { currentFeed } = useCurrentFeed();
  const feedRenderProps = useFeedRenderProps({
    humanizeFeedType: currentFeed.humanizeFeedType,
  });

  const ButtonGroup = () => {
    return (
      <Stack
        direction="row"
        spacing="8px"
        sx={{ alignSelf: 'center', justifySelf: 'flex-end' }}
      >
        {backButton}
        {nextButton}
      </Stack>
    );
  };

  return (
    <>
      <CustomAppBar
        {...props}
        redirectUrl={routes.feedDetailRoute(currentFeed.id)}
        btnGroup={<ButtonGroup />}
        sx={{ marginBottom: 0 }}
      />
      <FeedNameAndType
        feedRenderProps={feedRenderProps}
        feedId={currentFeed.id}
        feedName={currentFeed.profileName}
      />
    </>
  );
}

interface BackButtonProps {
  children: React.ReactNode;
  onClick?: ButtonProps['onClick'];
  loading?: ButtonProps['loading'];
  variant?: ButtonProps['variant'];
}

function BackButton({ children, loading, onClick, variant }: BackButtonProps) {
  const mdAndUp = useMediaQuery(useTheme().breakpoints.up('md'));
  return (
    <Button
      variant={variant ?? 'outlined'}
      size="small"
      onClick={onClick}
      loading={loading}
      startIcon={mdAndUp && <Icon type="default" icon={faArrowLeft} />}
      sx={{ backgroundColor: '#FFFFFF' }}
    >
      {mdAndUp ? children : <Icon type="default" icon={faArrowLeft} />}
    </Button>
  );
}

interface NextButtonProps extends Pick<ButtonProps, 'type'> {
  children: React.ReactNode;
  loading?: ButtonProps['loading'];
  onClick?: ButtonProps['onClick'];
}

function NextButton({
  type = 'button',
  children,
  loading,
  onClick,
}: NextButtonProps) {
  const mdAndUp = useMediaQuery(useTheme().breakpoints.up('md'));
  return (
    <Button
      type={type}
      size="small"
      loading={loading}
      variant="contained"
      endIcon={mdAndUp && <Icon type="default" icon={faArrowRight} />}
      onClick={(e) => {
        if (onClick) {
          e.preventDefault(); // prevent default <form onSubmit>
          onClick(e); // use provided onClick behavior instead
        }
      }}
    >
      {mdAndUp ? children : <Icon type="default" icon={faArrowRight} />}
    </Button>
  );
}

const FeedEditAppBar = Object.assign(FeedEditAppBar_, {
  NextButton,
  BackButton,
});

export { FeedEditAppBar };
