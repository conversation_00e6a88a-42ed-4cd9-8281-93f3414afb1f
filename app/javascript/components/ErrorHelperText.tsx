import { ErrorMessage } from '@hookform/error-message';
import { Box } from '@mui/material';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  FormHelperText,
  type FormHelperTextProps,
} from '@/components/FormHelperText';

export interface ErrorHelperTextProps {
  name: React.ComponentProps<typeof ErrorMessage>['name'];
  helperText?: FormHelperTextProps['children'];
  helperTextProps?: Pick<React.CSSProperties, 'display'>;
  fontSize?: React.CSSProperties['fontSize'];
  errors: ReturnType<typeof useFormContext>['formState']['errors'];
}

export function ErrorHelperText({
  name,
  helperText,
  helperTextProps: { display } = {},
  errors,
  fontSize = '12px',
}: ErrorHelperTextProps) {
  const { t } = useTranslation();
  // if available, use nested errors - e.g. name=`array.0.key`
  const { getFieldState } = useFormContext() || {};
  const fieldState = getFieldState?.(name);
  const hasNestedErrorMessage =
    fieldState?.invalid && fieldState?.error?.message;

  // else use normal formState.errors
  const hasNormalErrorMessage = errors[name]?.message;

  const hasErrorMessage = hasNestedErrorMessage || hasNormalErrorMessage;

  return (
    <Box
      component="span"
      sx={{
        display: hasErrorMessage || helperText ? 'block' : 'none',
        paddingTop: '9px',
      }}
    >
      {/* ErrorMessage able to parse nested errors and render */}
      {hasErrorMessage && (
        <ErrorMessage
          errors={errors}
          name={name}
          render={({ message }) => (
            <FormHelperText
              error
              component="span"
              sx={{ display: display ?? 'block', margin: 0, fontSize }}
            >
              {t(message)}
            </FormHelperText>
          )}
        />
      )}
      <FormHelperText
        component="span"
        error={false} // non-error helperText
        sx={{ display: display ?? 'block', margin: 0, fontSize }}
      >
        {helperText}
      </FormHelperText>
    </Box>
  );
}
