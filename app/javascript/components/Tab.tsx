import {
  TabList as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TabPanel as MuiTabPanel,
  TabContext,
  type TabListProps as MuiTabListProps,
  type TabPanelProps as MuiTabPanelProps,
} from '@mui/lab';
import {
  Tab as MuiTab,
  useTheme,
  type TabProps as MuiTabProps,
} from '@mui/material';

export interface TabProps extends MuiTabProps {
  size?: 'small' | 'medium';
}

function Tab_({ size = 'medium', sx, ...props }: TabProps) {
  const theme = useTheme();
  return (
    <MuiTab
      sx={{
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        fontSize:
          size === 'small'
            ? theme.typography.body1.fontSize
            : theme.typography.h6.fontSize,
        ...sx,
      }}
      {...props}
    />
  );
}

export interface TabListProps extends MuiTabListProps {
  borderBottom?: boolean;
}

function TabList({ borderBottom = false, sx, ...props }: TabListProps) {
  return (
    <MuiTabList
      variant="scrollable"
      scrollButtons={false}
      sx={{
        borderBottom: borderBottom ? 1 : 0,
        borderColor: 'divider',
        paddingLeft: '24px',
        paddingRight: '24px',
        ...sx,
      }}
      {...props}
    />
  );
}

export type TabPanelProps = MuiTabPanelProps;

function TabPanel({ sx, ...props }: TabPanelProps) {
  return <MuiTabPanel sx={{ padding: '32px 24px', ...sx }} {...props} />;
}

const Tab = Object.assign(Tab_, {
  Context: TabContext,
  List: TabList,
  Panel: TabPanel,
});

export { Tab };
