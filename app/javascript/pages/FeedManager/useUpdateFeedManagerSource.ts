import * as React from 'react';

import { useMutation } from '@/hooks/useMutation';
import { UpdateFeedSource } from '@/queries/FeedSettings';

import { formOutSchema, type FeedManagerFormValues } from './schema';

export function useUpdateFeedManagerSource() {
  const [, updateFeedSource] = useMutation(UpdateFeedSource, {
    dataKey: 'updateFeedSource',
  });

  type UpdateFeedSourceProps = Parameters<typeof updateFeedSource>[1] & {
    values: FeedManagerFormValues;
  };

  return {
    updateFeedSource: React.useCallback(
      async ({ values, onError, onSuccess }: UpdateFeedSourceProps) => {
        console.log(`submit values:`, values);
        const parsed = formOutSchema.safeParse(values);
        if (parsed.success) {
          const parsedValues = parsed.data;
          console.log(
            `submit parsed values:`,
            JSON.stringify(parsedValues, null, 2)
          );

          // TODO: manually list below, or apiSchema pick/omit?
          await updateFeedSource(
            {
              feedId: parseInt(parsedValues.id),
              accName: parsedValues.accName,
              aliexpressAppId: parsedValues.aliexpressAppId,
              aliexpressAppSecret: parsedValues.aliexpressAppSecret,
              authType: parsedValues.authType,
              autoFileSettings: parsedValues.autoFileSettings,
              bodyRaw: parsedValues.bodyRaw,
              callParams: JSON.stringify(parsedValues.callParams),
              clearGoogleSheet: parsedValues.clearGoogleSheet,
              colSep: parsedValues.colSep,
              combinedSourceType: parsedValues.combinedSourceType,
              connectionSettings: parsedValues.connectionSettings,
              customFileName: parsedValues.customFileName,
              customLoginField: parsedValues.customLoginField,
              customLoginPasswordField: parsedValues.customLoginPasswordField,
              exportEmail: parsedValues.exportEmail,
              fileEncoding: parsedValues.fileEncoding,
              fileFormat: parsedValues.fileFormat,
              fileName: parsedValues.fileName,
              ftpRename: parsedValues.ftpRename,
              ftpWhitelist: parsedValues.ftpWhitelist,
              googleOutInsert: parsedValues.googleOutInsert,
              googleShoppingCategory: parsedValues.googleShoppingCategory,
              hasHeader: parsedValues.hasHeader,
              headerParams: JSON.stringify(parsedValues.headerParams),
              httpMethod: parsedValues.httpMethod,
              namespaceIdentifier: parsedValues.namespaceIdentifier,
              oneDriveShareUrl: parsedValues.oneDriveShareUrl,
              parentNode: parsedValues.parentNode,
              password: parsedValues.password,
              pathToFile: parsedValues.pathToFile,
              publicFile: parsedValues.publicFile,
              s3AccessKeyId: parsedValues.s3AccessKeyId,
              s3BucketName: parsedValues.s3BucketName,
              s3SecretAccessKey: parsedValues.s3SecretAccessKey,
              sheetName: parsedValues.sheetName,
              skipTotalRows: Number(parsedValues.skipTotalRows), // possibly null
              sourceAuth: parsedValues.sourceAuth,
              sourceUrl: parsedValues.sourceUrl,
              sshKey: parsedValues.sshKey,
              unleashedApiId: parsedValues.unleashedApiId,
              unleashedApiKey: parsedValues.unleashedApiKey,
              variantNode: parsedValues.variantNode,
              walmartClientId: parsedValues.walmartClientId,
              walmartClientSecret: parsedValues.walmartClientSecret,
              woocommerceApiVersion: parsedValues.woocommerceApiVersion,
              woocommerceConsumerKey: parsedValues.woocommerceConsumerKey,
              woocommerceConsumerSecret: parsedValues.woocommerceConsumerSecret,
            },
            { onSuccess, onError }
          );
        } else {
          console.log(`parse error:`, parsed.error);
        }
      },
      [updateFeedSource]
    ),
  };
}
