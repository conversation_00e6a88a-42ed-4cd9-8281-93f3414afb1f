import {
  faChevronDown,
  faChevronUp,
  faCircleQuestion,
} from '@fortawesome/pro-light-svg-icons';
import {
  InputAdornment,
  MenuItem,
  Box as MuiBox,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import { Crisp } from 'crisp-sdk-web';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useWatch } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import ReactMarkdown from 'react-markdown';
import { useQuery } from 'urql';

import { Autocomplete } from '@/components/Autocomplete';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { DatePicker } from '@/components/DatePicker';
import { FileUploader } from '@/components/FileUploader';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link, type LinkProps } from '@/components/Link';
import { Switch } from '@/components/Switch';
import { Tags } from '@/components/Tags';
import { TextField } from '@/components/TextField';
import { useAlert } from '@/hooks/useAlert';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDashboardFeeds } from '@/hooks/useDashboardFeeds';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { TestConnection } from '@/queries/FeedSettings';
import { GetGoogleShoppingCategories } from '@/queries/Users';
import {
  woocommerceApiVersions,
  type AuthType,
  type UserProfile,
} from '@/types';

import {
  cjCategoryOptions,
  ebihrOptions,
  overnightMountingOptions,
  synnexCategoryOptions,
  ukDistributorBrandOptions,
  ukDistributorCategoryOptions,
  wosActionSportsOptions,
} from '../constants';
import { formOutSchema } from '../schema';
import { useFeedManagerFormContext } from '../useFeedManagerFormContext';
import {
  AutoCompleted,
  FileSettingsByFileFormat,
  FileSettingsCard,
  HasHeader,
  ParentNode,
  SheetName,
  SkipTotalRows,
} from './FileSettings';
import { FormGrid } from './FormGrid';
import {
  Access,
  AccountName,
  AliexpressAppId,
  AliexpressAppSecret,
  BodyRaw,
  CustomFileName,
  ExportEmail,
  FtpRename,
  FtpWhitelist,
  Params,
  Password,
  PathToFile,
  S3AccessKeyId,
  S3BucketName,
  S3SecretAccessKey,
  SourceAuth,
  SourceUrl,
  UnleashedApiId,
  UnleashedApiKey,
  WalmartClientId,
  WalmartClientSecret,
  WoocommerceConsumerKey,
  WoocommerceConsumerSecret,
} from './SourceTypeFields';

/**
 * First card on page
 */
interface SourceTypesCardProps {
  children: React.ReactNode;
  helpLink?: LinkProps['to'];
  helpText?: LinkProps['children'];
}

export function SourceTypesCard({
  children,
  helpLink,
  helpText,
}: SourceTypesCardProps) {
  const { currentFeed } = useCurrentFeed();

  const matchesMd = useMatches('md');

  const { t } = useTranslation();
  const combinedSourceType = currentFeed.combinedSourceType ?? '';

  return (
    <Stack
      direction={matchesMd ? 'column' : 'row'}
      spacing={3}
      sx={{ marginTop: '24px' }}
    >
      <MuiBox sx={{ maxWidth: '250px', width: '100%' }}>
        <Typography variant="h5" sx={{ fontWeight: 500 }}>
          {t('connection_type')}
        </Typography>
        <Typography variant="body1" sx={{ fontWeight: 500, marginTop: '8px' }}>
          <span>{currentFeed.supplier?.name ?? t(combinedSourceType)}</span>
          {helpLink && (
            <Link
              to={helpLink}
              sx={{
                display: 'block',
                verticalAlign: 'text-bottom',
                whiteSpace: 'nowrap',
                marginTop: '8px',
              }}
            >
              <Button
                variant="text"
                size="small"
                sx={{ padding: 0 }}
                startIcon={<Icon type="default" icon={faCircleQuestion} />}
              >
                {helpText ?? t('learn_more')}
              </Button>
            </Link>
          )}
        </Typography>
      </MuiBox>
      <Card sx={{ padding: '32px 24px 24px', width: '100%' }}>
        <SupplierNotes />
        {children}
      </Card>
    </Stack>
  );
}

/**
 * Supplier notes
 */
function SupplierNotes() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      {currentFeed.supplier?.notes && (
        <>
          <Card sx={{ backgroundColor: '#0C7FE80F', padding: '24px' }}>
            {/* Needed to change font color */}
            <div style={{ color: '#00527C' }}>
              <ReactMarkdown>{currentFeed.supplier.notes || ''}</ReactMarkdown>
              <Typography variant="body1">
                {t('template_provided_for')} {currentFeed.supplier.name}.
                {currentFeed.supplier.url && (
                  <>
                    {' '}
                    <Link to={currentFeed.supplier.url}>
                      {t('visit_website')}
                    </Link>
                  </>
                )}
              </Typography>
            </div>
          </Card>
          <MuiBox sx={{ marginBottom: '32px' }} />
        </>
      )}
    </>
  );
}

function TestSourceType() {
  const { t } = useTranslation();
  const [testConnectionState, testConnection] = useMutation(TestConnection, {
    dataKey: 'testConnection',
  });
  const { handleSubmit } = useFeedManagerFormContext();
  const alert = useAlert();

  return (
    <>
      <Button
        loading={testConnectionState.fetching}
        variant="outlined"
        onClick={handleSubmit(async function (values) {
          const parsedValues = formOutSchema.parse(values);
          await testConnection(
            {
              accName: parsedValues.accName,
              combinedSourceType: parsedValues.combinedSourceType,
              connectionSettings: parsedValues.connectionSettings,
              feedId: parseInt(parsedValues.id),
              password: parsedValues.password,
              pathToFile: parsedValues.pathToFile,
              s3AccessKeyId: parsedValues.s3AccessKeyId,
              s3BucketName: parsedValues.s3BucketName,
              s3SecretAccessKey: parsedValues.s3SecretAccessKey,
              sourceAuth: parsedValues.sourceAuth,
              sourceUrl: parsedValues.sourceUrl,
              sshKey: parsedValues.sshKey,
              woocommerceConsumerKey: parsedValues.woocommerceConsumerKey,
              woocommerceConsumerSecret: parsedValues.woocommerceConsumerSecret,
            },
            {
              onSuccess({ enqueueSnackbar }) {
                enqueueSnackbar('Connection Successful', {
                  variant: 'success',
                });
              },
              onError({ error }) {
                alert.setMessage(t(error));
              },
            }
          );
        })}
      >
        {t('test_connection')}
      </Button>
    </>
  );
}

// Add components below here:

export function DickerData() {
  const { t } = useTranslation();

  const subscriptionTypes = {
    SUBSCRIPTION: 'CSP Subscriptions',
    AZURE: 'Azure Subscription',
    PERP: 'Perpetual software',
    RESER: 'Reserved instance',
    SOFT: 'Software subcriptions',
  };

  const region = { au: 'Australia', nz: 'New Zealand' };

  return (
    <>
      <SourceTypesCard helpLink="https://support.stock-sync.com/support/solutions/articles/***********-update-dropship-clothes-products-with-stock-sync">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('access_token')} />,
            <WoocommerceConsumerSecret label={t('client_code')} />,
            <AliexpressAppId
              select
              label={t('subscription_type')}
              defaultValue="SUBSCRIPTION"
            >
              {Object.keys(subscriptionTypes).map((type) => (
                <MenuItem key={type} value={type}>
                  {subscriptionTypes[type]}
                </MenuItem>
              ))}
            </AliexpressAppId>,
            <AliexpressAppSecret
              select
              label={t('country_api')}
              defaultValue="au"
            >
              {Object.keys(region).map((country) => (
                <MenuItem key={country} value={country}>
                  {region[country]}
                </MenuItem>
              ))}
            </AliexpressAppSecret>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function DropshipClothes() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-dear-lover-dropship-dropship-clothes-uhh4dh/'
            : 'https://help.stock-sync.com/en/article/filtering-dear-lover-dropship-clothes-products-with-category-ids-1v50gof/'
        }
      >
        <FormGrid components={[<AliexpressAppId label={t('category_id')} />]} />
        <Link to="https://help.stock-sync.com/en/article/filtering-dear-lover-dropship-clothes-products-with-category-ids-1v50gof/">
          <Icon type="default" icon={faCircleQuestion} />{' '}
          {t('learn_more_category_id')}
        </Link>
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Matterhorn() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();
  const currencies = { EUR: 'EUR', USD: 'USD' };
  const currencyCodes = Object.keys(currencies);

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-matterhorn-13uxjpz/'
            : 'https://help.stock-sync.com/en/article/update-products-from-matterhorn-pdc7fk/'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey
              select
              label={t('currency_code')}
              defaultValue="EUR"
            >
              {currencyCodes.map((code) => (
                <MenuItem key={code} value={code}>
                  {currencies[code]}
                </MenuItem>
              ))}
            </WoocommerceConsumerKey>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BtsWholesaler() {
  const { t } = useTranslation();

  const locales = [
    { label: 'English (US)', value: 'en-US' },
    { label: 'English (UK)', value: 'en-GB' },
    { label: 'French', value: 'fr-FR' },
    { label: 'Italian', value: 'it-IT' },
    { label: 'Spanish', value: 'es-ES' },
    { label: 'German', value: 'de-DE' },
  ];

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('bts_account_id')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey
              select
              label={t('label_language_code')}
              defaultValue="en-US"
            >
              {locales.map(({ label, value }) => (
                <MenuItem key={value} value={value}>
                  {label}
                </MenuItem>
              ))}
            </WoocommerceConsumerKey>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Controlport() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('api_key')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function TarsusDistribution() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('api_key')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function CopperBayDigital() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('api_key')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function DropshippingB2b() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('pid')} />,
            <WoocommerceConsumerSecret label={t('lid')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function FootballSouvenir() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('userid')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Heo() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('email')} />,
            <Password label={t('password')} />,
            <TextField
              select
              control={control}
              name="connectionSettings.language"
              label={t('language')}
              defaultValue="english"
              style={{ textTransform: 'capitalize' }}
            >
              {['english', 'spanish'].map((lang) => (
                <MenuItem
                  key={lang}
                  value={lang}
                  style={{ textTransform: 'capitalize' }}
                >
                  {lang}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function HeoApi() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              select
              control={control}
              name="connectionSettings.language"
              label={t('language')}
              defaultValue="english"
              sx={{ textTransform: 'capitalize' }}
            >
              {['english', 'germany', 'france', 'spanish'].map((lang) => (
                <MenuItem
                  key={lang}
                  value={lang}
                  sx={{ textTransform: 'capitalize' }}
                >
                  {lang}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function IngramContent() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();
  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-ingram-content-y58rdv/'
            : 'https://help.stock-sync.com/en/article/update-products-from-ingram-content-1j8zstu/'
        }
      >
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('query')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function InternetBikes() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('email')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ItalTrading() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('ital_trading_key')} />,
            <WoocommerceConsumerSecret label={t('ital_trading_id')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Logsta() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('api_token')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Matas() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();
  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-matas-i4m0pz/'
            : 'https://help.stock-sync.com/en/article/update-products-from-matas-10tt225/'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_secret')} />,
            <AccountName label={t('subscription_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Mustek() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-mustek-h5umwh/'
            : 'https://help.stock-sync.com/en/article/update-products-from-mustek-u9e33b/'
        }
      >
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('customer_token')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Naturaldispensary() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Nod() {
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label="API User" />,
            <WoocommerceConsumerSecret label="API Key" />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Nrdata() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[<WoocommerceConsumerSecret label={t('app_secret')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function PartsUnlimited() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/update-products-from-parts-unlimited-3mamyk/">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('dealer_code')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Dropshipzone() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-dropship-zone-api-hjhabp/'
            : 'https://help.stock-sync.com/en/article/update-products-from-dropship-zone-api-4a21bj/'
        }
      >
        <FormGrid
          components={[
            <AccountName label={t('email')} />,
            <Password label={t('password')} />,
            <AliexpressAppId label={t('supplier_ids')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function PuckatorDropship() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('email')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function SexToyDistributingImages() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function SexToyDistributing() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-sex-toy-distributing-xhupzg/'
            : 'https://help.stock-sync.com/en/article/update-products-from-sex-toy-distributing-abglvo/'
        }
      >
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Fulfillrite() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Smiffys() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Orso() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[<WoocommerceConsumerSecret label={t('api_token')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function SmiffysApi() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_key')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_id')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function SsActiveWear() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/ss-active-wear-connection-method-1vpdg0c/'
            : 'https://help.stock-sync.com/en/article/update-products-from-ss-active-wear-us-1fv098/'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('username')} />,
            <WoocommerceConsumerSecret label={t('api_key')} />,
            <TextField
              control={control}
              name="connectionSettings.warehouses"
              label={t('warehouses')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function StrawberryNet() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('site_id')} />,
            <WoocommerceConsumerSecret label={t('lang_id')} />,
            <AliexpressAppSecret label={t('currency_code')} />,
            <AliexpressAppId label={t('brand_id')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function StrickerEurope() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('intcomex_user_access_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Sunkys() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-sunkys-16e087t/'
            : 'https://help.stock-sync.com/en/article/update-products-from-sunkys-1ltz7yk/'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_key')} />,
            <WoocommerceConsumerSecret label={t('secret_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function SwiftStock() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function TreasureHouse() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('email')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('product_code')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Vidaxl() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/support/solutions/articles/***********-add-products-from-vidaxl'
            : 'https://help.stock-sync.com/support/solutions/articles/***********-update-products-from-vidaxl'
        }
      >
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function WesternPowerSports() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/support/solutions/articles/***********-update-products-from-western-power-sports">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('access_token')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ChattanoogaShooting() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_key')} />,
            <WoocommerceConsumerSecret label={t('api_token')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BrightpointSoap() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerSecret label={t('brightpoint_customer_no')} />,
            <WoocommerceConsumerKey label={t('brightpoint_instance')} />,
            <AccountName label={t('brightpoint_site')} />,
            <Password label={t('passwords')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function HorizonHobby() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('horizon_hobby_max_pages')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Encompass() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('customer_number')} />,
            <WoocommerceConsumerSecret label={t('customer_password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function TsigaridasBooks() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey
              label={t('tsigaridasbooks_interval_hours')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Maxevan() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Killerdeal() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-killer-deals-1mk6pps/'
            : 'https://help.stock-sync.com/en/article/update-products-from-killer-deals-pgdxrz/'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_consumer_key')} />,
            <WoocommerceConsumerSecret label={t('api_consumer_secret')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Restlet() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.consumer_key"
              label={t('consumer_key')}
            />,
            <TextField
              control={control}
              name="connectionSettings.consumer_secret"
              label={t('consumer_secret')}
            />,
            <TextField
              control={control}
              name="connectionSettings.access_token"
              label={t('access_token')}
            />,
            <TextField
              control={control}
              name="connectionSettings.token_secret"
              label={t('token_secret')}
            />,
            <TextField
              control={control}
              name="connectionSettings.realm"
              label={t('realm')}
            />,
            <TextField
              control={control}
              name="connectionSettings.class"
              label={t('class')}
            />,
            <TextField
              control={control}
              name="connectionSettings.id"
              label={t('id')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Billiet() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('client_code')} />,
            <AliexpressAppId label={t('customer_code')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BigcommerceFeed() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerSecret label={t('bigcommerce_store_hash')} />,
            <WoocommerceConsumerKey label={t('bigcommerce_access_token')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BigcommerceStore() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <SourceUrl
              label={t('public_token')}
              helperText={t('shopify_store_tooltips')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Axiz() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_secret')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Asi() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_secret')} />,
            <AliexpressAppId label={t('supplier_ids')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Amrod() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/support/solutions/folders/***********">
        <FormGrid
          components={[
            <WoocommerceConsumerSecret label={t('api_token')} />,
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <AliexpressAppId label={t('category_ids')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BoardsAndMore() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/support/solutions/articles/***********-add-products-from-boards-and-more'
            : 'https://help.stock-sync.com/en/support/solutions/articles/***********-update-products-from-boards-more'
        }
      >
        <FormGrid
          components={[
            <AccountName label={t('email')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Agis() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-agis-c4f6gb/'
            : 'https://help.stock-sync.com/en/article/update-products-from-agis-s6t0bs/'
        }
      >
        <FormGrid
          components={[<WoocommerceConsumerSecret label={t('api_token')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Cj() {
  const { t } = useTranslation();
  const { control, setValue, getValues } = useFeedManagerFormContext();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/cj-dropshipping-connection-method-1ro99kd/">
        <FormGrid
          components={[
            <Autocomplete
              control={control}
              name="_cjCategoryOption"
              label={t('category')}
              options={cjCategoryOptions}
              getOptionLabel={(option) => {
                return (
                  // if user selects
                  option.label ||
                  // API only returns category id, find matching label
                  cjCategoryOptions.find((c) => c.value === option.value)
                    ?.label ||
                  // default to empty
                  ''
                );
              }}
              isOptionEqualToValue={(option, value) =>
                option.value === value?.value
              }
              defaultValue={{
                label: '',
                value: getValues('_authType.woocommerceConsumerKey') ?? '',
              }}
              onChange={(_, option) => {
                if (!option) return;
                setValue('_authType.woocommerceConsumerKey', option.value);
              }}
            />,
            <AccountName label={t('email')} />,
            <Password label={t('cj_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Hlc() {
  const { t } = useTranslation();
  const countries = { us: 'USA', ca: 'Canada' };

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/hlc-bike-connection-method-1rt8rvc/">
        <FormGrid
          components={[
            <WoocommerceConsumerSecret label={t('api_token')} />,
            <WoocommerceConsumerKey
              select
              label={t('hlc_country')}
              defaultValue="us"
            >
              {Object.keys(countries).map((countryCode) => (
                <MenuItem key={countryCode} value={countryCode}>
                  {countries[countryCode]}
                </MenuItem>
              ))}
            </WoocommerceConsumerKey>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Qbp() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/quality-bicycle-products-connection-method-iik0mc/">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('qbp_key')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Intcomex() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/intcomex-connection-method-1wtqlwf/">
        <FormGrid
          components={[
            <AccountName label={t('api_key')} />,
            <Password label={t('intcomex_user_access_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Sysco() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/sysco-connection-method-14lb3kg/">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <WoocommerceConsumerKey label={t('customer_code')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Kotryna() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/kotryna-group-connection-method-1gei836/">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('api_key')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function SpmNetwork() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/spm-network-connection-method-ijxgnc/">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_user')} />,
            <WoocommerceConsumerSecret label={t('api_token')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function WalmartApiOut() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/walmart-api-connection-method-export-135avcy/">
        <FormGrid
          components={[
            <WalmartClientSecret label={t('walmart_client_secret')} />,
            <WalmartClientId label={t('walmart_client_id')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function MysaleApiOut() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/support/solutions/articles/4**********-mysale-api-connection-method">
        <FormGrid
          components={[<WalmartClientSecret label={t('bearer_token')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Novaengel() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/nova-engel-connection-method-zl3cg/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Trendcollection() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/trends-collection-connection-method-na84i1/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Ecomdash() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/ecomdash-connection-method-1glyzx2/">
        <FormGrid
          components={[<WoocommerceConsumerKey label={t('ecomdash_key')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function TplCentral() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/3pl-central-connection-method-1b27bif/">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_secret')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Beautyfort() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/beautyfort-eu-connection-method-h35dy3/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Coasteramer() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/coaster-co-of-america-connection-method-1a0xygu/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('api_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EbayOut() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/write-to-ebay-connection-method-export-1e403x2/">
        <FormGrid
          components={[<Access helpText={t('ebay_authorize_tooltips')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EbayV2Out() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/write-to-ebay-connection-method-export-1e403x2/">
        <FormGrid
          components={[<Access helpText={t('ebay_authorize_tooltips')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EbayIn() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/read-from-ebay-connection-method-9lcgse/">
        <FormGrid
          components={[<Access helpText={t('ebay_authorize_tooltips')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EbayV2In() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/read-from-ebay-connection-method-9lcgse/">
        <FormGrid
          components={[<Access helpText={t('ebay_authorize_tooltips')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ZohoInventoryOut() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <Access helpText={t('zoho_inventory_authorize_tooltips')} />,
          ]}
        />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Xero() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/xero-connection-method-rggl9m/">
        <FormGrid
          components={[<Access helpText={t('xero_authorize_tooltips')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Vend() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/vend-connection-method-1v0xf6f/">
        <FormGrid
          components={[<Access helpText={t('vend_authorize_tooltips')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Quickbooks() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/quickbooks-connection-method-1pzmtx3/">
        <FormGrid
          components={[
            <Access helpText={t('quickbooks_authorize_tooltips')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Unleashed() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/unleashed-connection-method-nf7rij/">
        <FormGrid
          components={[
            <UnleashedApiKey label={t('app_key')} />,
            <UnleashedApiId label={t('app_id')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Turn14() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/support/solutions/articles/44002428969-update-products-from-turn-14-distribution">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_consumer_key')} />,
            <WoocommerceConsumerSecret label={t('api_consumer_secret')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Tme() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_token')} />,
            <WoocommerceConsumerSecret label={t('aliexpress_app_secret')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Malabs() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/malabs-connection-method-1q8kf7c/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function GigaCloud() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_secret')} />,
            <AccountName label="Subscription Key" />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BsaleChile() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/bsale-chile-connection-method-11ml3z7/">
        <FormGrid
          components={[<WoocommerceConsumerSecret label={t('access_token')} />]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BrandsDistribution() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/brands-distribution-connection-method-lzhnl3/">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('api_key')} />,
            <WoocommerceConsumerSecret label={t('aliexpress_app_secret')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function AliexpressOut() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/aliexpress-marketplace-connection-method-1oa8xq5/?bust=1719893139567">
        <FormGrid
          components={[
            <AliexpressAppSecret label={t('aliexpress_app_secret')} />,
            <AliexpressAppId label={t('app_key')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
        <MuiBox sx={{ marginTop: '32px' }} />
        <FormGrid
          components={[
            <Access helpText={t('aliexpress_authorize_tooltips')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function AliexpressIn() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/aliexpress-marketplace-connection-method-1oa8xq5/?bust=1719893139567">
        <FormGrid
          components={[
            <AliexpressAppSecret label={t('aliexpress_app_secret')} />,
            <AliexpressAppId label={t('app_key')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <FormGrid
          components={[
            <Access helpText={t('aliexpress_authorize_tooltips')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function RetailEdge() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <SourceUrl label={t('source_url')} />,
            <AccountName label={t('access_code')} />,
            <Password label={t('access_password')} />,
            <WoocommerceConsumerKey label={t('clerk_id')} />,
            <WoocommerceConsumerSecret label={t('clerk_password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ZohoSheet() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/zoho-sheet-connection-method-qxl959/">
        <FormGrid
          components={[
            <SourceUrl
              label={t('zoho_public_sheet')}
              helperText={t('zoho_url_tooltips')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Woocommerce() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control } = useFeedManagerFormContext();
  const [open, setOpen] = React.useState(false);
  const languageOptions: Array<{
    key: 'en' | 'fr';
    value: string;
  }> = [
    { key: 'en', value: 'English' },
    { key: 'fr', value: 'French' },
  ];

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/woocommerce-connection-method-dx0zhp/">
        <FormGrid
          components={[
            <SourceUrl
              label={t('store_url')}
              helperText="eg. https://yourstorename.com"
            />,
            <WoocommerceConsumerKey label={t('api_consumer_key')} />,
            <WoocommerceConsumerSecret label={t('api_consumer_secret')} />,
          ]}
        />
        <FormGrid
          components={[
            <Access
              helpText={t('woocommerce_authorize_tooltips', {
                role_one: t('administrator'),
                role_two: t('shop_manager'),
              })}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <Icon
          type="default"
          onClick={() => {
            setOpen(!open);
          }}
          icon={open ? faChevronUp : faChevronDown}
          style={{
            cursor: 'pointer',
            color: theme.palette.link,
            transform: 'translateY(2px)',
            marginLeft: '8px',
          }}
        />
        <Button
          variant="text"
          size="small"
          id={
            open
              ? 'feed-manager-on-more-settings'
              : 'feed-manager-off-more-settings'
          }
          onClick={() => setOpen(!open)}
          sx={{ padding: '0px 8px' }}
        >
          {open ? t('hide_settings') : t('more_settings')}
        </Button>
        {open && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid
              components={[
                <TextField
                  select
                  name="woocommerceApiVersion"
                  control={control}
                  label={t('api_version')}
                >
                  {woocommerceApiVersions.map((version) => (
                    <MenuItem key={version} value={version}>
                      {version}
                    </MenuItem>
                  ))}
                </TextField>,
                <TextField
                  select
                  name="bodyRaw"
                  control={control}
                  label={t('store_lang')}
                  defaultValue="en"
                >
                  {languageOptions.map(({ key, value }) => (
                    <MenuItem key={key} value={key}>
                      {value}
                    </MenuItem>
                  ))}
                </TextField>,
              ]}
            />
          </>
        )}
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Wholesalesportwear() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <SourceUrl
              label={t('source_host')}
              helperText={t('rest_api_url_tooltips')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_headerParams" title={t('header_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ShopifyStore() {
  const { t } = useTranslation();
  const theme = useTheme();
  const [open, setOpen] = React.useState(false);

  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/shopify-store-require-stock-sync-app-1amdmfa/">
        <FormGrid
          components={[
            <SourceUrl
              label={t('public_token')}
              helperText={t('shopify_store_tooltips')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <Icon
          type="default"
          onClick={() => {
            setOpen(!open);
          }}
          icon={open ? faChevronUp : faChevronDown}
          style={{
            cursor: 'pointer',
            color: theme.palette.link,
            transform: 'translateY(2px)',
            marginLeft: '8px',
          }}
        />
        <Button
          variant="text"
          size="small"
          id={
            open
              ? 'feed-manager-on-more-settings'
              : 'feed-manager-off-more-settings'
          }
          onClick={() => setOpen(!open)}
          sx={{ padding: '0px 8px' }}
        >
          {open ? t('hide_settings') : t('more_settings')}
        </Button>
        {open && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <Params name="_callParams" title={t('query_params')} />
          </>
        )}
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ShopifyPublic() {
  const { control, setValue, getValues } = useFeedManagerFormContext();
  const sourceUrl = getValues('sourceUrl');

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              name="_shopifyPublicDomain"
              label="Shopify Domain"
              control={control}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Typography>https://</Typography>
                    </InputAdornment>
                  ),
                },
              }}
              defaultValue={React.useMemo(() => {
                const domainMatch = sourceUrl.match(
                  /^https:\/\/(.*?)\.myshopify\.com/
                );
                return domainMatch ? domainMatch[1] : '';
              }, [sourceUrl])}
              onChange={({ target: { value } }) => {
                const splitValue = value.split('.')[0];
                setValue(
                  'sourceUrl',
                  `https://${splitValue}.myshopify.com/collections/all/products.json?page={{page}}`
                );
              }}
            />,
            <TextField
              control={control}
              name="_shopifyPublicReadonlyUrl"
              disabled
              defaultValue={React.useMemo(() => {
                // Updated regex to capture .myshopify.com and everything after it, excluding query parameters
                const domainMatch = sourceUrl.match(
                  /(\.myshopify\.com\/[^?]*)/
                );
                return domainMatch
                  ? domainMatch[1]
                  : '.myshopify.com/collections/all/products.json?page={{page}}'; // Return the part including .myshopify.com without query parameters
              }, [sourceUrl])}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function WixStore() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <SourceUrl
              label={t('public_token')}
              helperText={t('shopify_store_tooltips')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Erply() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
            <SourceUrl
              label={t('client_code')}
              helperText={t('erply_tooltips')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Banggood() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/banggood-connection-method-1g7qghi/">
        <FormGrid
          components={[
            <AccountName label={t('app_id')} />,
            <Password label={t('app_secret')} />,
            <SourceUrl label={t('category_id')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ShopifyFeed() {
  const { t } = useTranslation();
  const { feedType } = useCurrentFeed().currentFeed;
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/shopify-custom-app-vnt1aj/">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('shopify_api_key')} />,
            <WoocommerceConsumerSecret label={t('shopify_api_secret')} />,
            feedType === 'update' ? (
              <PathToFile
                label={t('location')}
                helperText={t('shopify_feed_location')}
              />
            ) : (
              <></>
            ),
            <SourceUrl
              label={t('shopify_domain')}
              helperText="something.myshopify.com"
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Sanmar() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-sanmar-ftp-19t4gyo/'
            : 'https://help.stock-sync.com/en/article/update-products-from-sanmar-ftp-ntlvwp/'
        }
      >
        <FormGrid
          components={[
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={
                <Stack component="span">
                  <span>{t('directory_file_name_tooltip')}</span>
                  <Trans i18nKey="filename_with_timestamp">
                    <span>
                      For filename with timestamp, please click{' '}
                      <Link to="https://stocksync.crisp.help/en/article/wildcard-and-dynamic-date-14cjfcy/">
                        here
                      </Link>
                      for more info
                    </span>
                  </Trans>
                </Stack>
              }
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Prv() {
  return <CustomFtp helpLink="" />;
}

export function Maropost() {
  return <CustomFtp helpLink="" />;
}

export function IngramContentFtp() {
  return <CustomFtp helpLink="" />;
}

export function Eldorado() {
  return <CustomFtp helpLink="" />;
}

export function Twhouse() {
  return <CustomFtp helpLink="" />;
}

export function Windsor() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-windsor-1g8phgc/'
            : 'https://help.stock-sync.com/en/article/update-products-from-windsor-15up6pp/'
        }
      >
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

function CustomFtp({ helpLink }: Pick<SourceTypesCardProps, 'helpLink'>) {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink={helpLink}>
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={
                <Stack component="span">
                  <span>{t('directory_file_name_tooltip')}</span>
                  <Trans i18nKey="filename_with_timestamp">
                    <span>
                      For filename with timestamp, please click{' '}
                      <Link to="https://stocksync.crisp.help/en/article/wildcard-and-dynamic-date-14cjfcy/">
                        here
                      </Link>
                      for more info
                    </span>
                  </Trans>
                </Stack>
              }
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Airtable() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/airtable-api-connection-method-1x8wp0u/?bust=*************">
        <FormGrid
          components={[
            <AccountName label={t('app_id')} />,
            <Password label={t('api_key')} />,
            <SheetName label={t('table_name')} helperText="" />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function AirtableV2() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/airtable-api-connection-method-1x8wp0u/?bust=*************">
        <Access helpText={t('airtable_authorize_tooltips')} />
        <MuiBox sx={{ marginBottom: '24px' }} />
        <FormGrid
          components={[
            <AccountName label={t('base_id')} />,
            <SheetName label={t('table_id')} helperText="" />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Stuller() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/stuller-connection-method-hw1te3/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
            <BodyRaw />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function FtpHouzzOut() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/houzz-connection-method-export-171djcb/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <TextField
              multiline
              name="sshKey"
              label={t('ssh_key')}
              helperText={`${t('ssh_key_tooltips')} ${t('pem_format')}`}
            />,
            <SourceUrl
              label={t('source_host')}
              helperText={t('ftp_url_tooltips')}
            />,
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={
                <Stack component="span">
                  <span>{t('directory_file_name_tooltip')}</span>
                  <Trans i18nKey="filename_with_timestamp">
                    <span>
                      For filename with timestamp, please click{' '}
                      <Link to="https://stocksync.crisp.help/en/article/wildcard-and-dynamic-date-14cjfcy/">
                        here
                      </Link>
                      for more info
                    </span>
                  </Trans>
                </Stack>
              }
            />,
          ]}
        />
        <FormGrid components={[<FtpWhitelist />]} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function StileoOut() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/stileoit-poland-fashion-marketplace-connection-method-export-4onnfr/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
            <SourceUrl
              label={t('source_host')}
              helperText={t('ftp_url_tooltips')}
            />,
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={
                <Stack component="span">
                  <span>{t('directory_file_name_tooltip')}</span>
                  <Trans i18nKey="filename_with_timestamp">
                    <span>
                      For filename with timestamp, please click{' '}
                      <Link to="https://stocksync.crisp.help/en/article/wildcard-and-dynamic-date-14cjfcy/">
                        here
                      </Link>
                      for more info
                    </span>
                  </Trans>
                </Stack>
              }
            />,
          ]}
        />
        <FormGrid components={[<FtpWhitelist />]} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Amazon() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/generate-feed-to-export-as-email-to-amazon-warehouse-b13ncp/">
        <FormGrid components={[<ExportEmail />]} />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function WayfairOut() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/wayfair-inventory-connection-method-export-s183f0/">
        <FormGrid components={[<ExportEmail />]} />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EmailOut() {
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/send-out-email-connection-method-export-t5w3a3/?bust=*************">
        <FormGrid components={[<ExportEmail />]} />
      </SourceTypesCard>
      <FileSettingsCard>
        <HasHeader />
      </FileSettingsCard>
    </>
  );
}

export function Clf() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/clf-distribution-soap-connection-method-xcjb0f/">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <SourceUrl
              label={t('wsdl_url')}
              helperText={t('clf_source_url_tooltips')}
            />,
            <ParentNode label={t('parent_node')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Kevro() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/kevro-connection-method-1w0d0kj/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
            <SourceUrl
              label={t('wsdl_url')}
              helperText={t('soap_url_tooltips')}
            />,
            <TextField
              name="namespaceIdentifier"
              control={control}
              label={t('namespace_identifier')}
              helperText={t('namespace_identifier_tooltips')}
            />,
            <PathToFile
              label={t('action_name')}
              helperText={t('soap_path_tooltips')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_callParams" title={t('query_params')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat accepted={['Xml']} defaultValue="Xml" />
      </FileSettingsCard>
    </>
  );
}

export function Soap() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { watch } = useFeedManagerFormContext();
  const sourceAuth = watch('_authType.sourceAuth');
  const [open, setOpen] = React.useState(false);

  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/soap-beta-connection-method-1cd2mni/">
        <SourceAuth />
        {sourceAuth && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid
              components={[
                <AccountName label={t('login')} />,
                <Password label={t('password')} />,
              ]}
            />
          </>
        )}
        <MuiBox sx={{ marginTop: '24px' }} />
        <FormGrid
          components={[
            <SourceUrl
              label={t('wsdl_url')}
              helperText={t('soap_url_tooltips')}
            />,
            <PathToFile
              label={t('action_name')}
              helperText={t('soap_path_tooltips')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <Icon
          type="default"
          onClick={() => {
            setOpen(!open);
          }}
          icon={open ? faChevronUp : faChevronDown}
          style={{
            cursor: 'pointer',
            color: theme.palette.link,
            transform: 'translateY(2px)',
            marginLeft: '8px',
          }}
        />
        <Button
          variant="text"
          size="small"
          id={
            open
              ? 'feed-manager-on-more-settings'
              : 'feed-manager-off-more-settings'
          }
          onClick={() => setOpen(!open)}
          sx={{ padding: '0px 8px' }}
        >
          {open ? t('hide_settings') : t('more_settings')}
        </Button>
        {open && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <Params name="_headerParams" title={t('header_params')} />
            <MuiBox sx={{ marginTop: '24px' }} />
            <Params name="_callParams" title={t('query_params')} />
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid components={[<FtpWhitelist />]} />
          </>
        )}
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat accepted={['Xml']} defaultValue="Xml" />
      </FileSettingsCard>
    </>
  );
}

export function RestApi() {
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: {
      feedConstants: {
        http_methods: httpMethods,
        rest_api_types: restApiTypes,
      },
    },
    currentStore,
  } = useCurrentStore();
  const { watch } = useFeedManagerFormContext();
  const [open, setOpen] = React.useState(false);
  const authType: AuthType = watch('_authType.type');
  React.useEffect(() => {
    const FLAG = 'crisp_rest_api_help_prompt_shown';

    const alreadyShown = localStorage.getItem(FLAG);

    if (!alreadyShown && Number(currentStore.id) > 82242) {
      Crisp.chat.open();
      Crisp.message.showText(
        'Hi! Need help setting up the REST API?\nJust reply “Yes” – we’ll guide you through it!'
      );
      localStorage.setItem(FLAG, '1');
    }
  }, [currentStore.id]);

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/rest-api-connection-method-ftvljs/">
        <FormGrid
          components={[
            <SourceUrl
              label={t('endpoint')}
              helperText="https://api.example.com/inventory/all"
            />,
            <TextField select name="httpMethod" label={t('http_method')}>
              {httpMethods.map((option) => (
                <MenuItem key={option.value} value={option.key}>
                  {String(option.value).toUpperCase()}
                </MenuItem>
              ))}
            </TextField>,
            <TextField select name="_authType.type" label={t('auth_type')}>
              {restApiTypes.map((option) => (
                <MenuItem key={option.value} value={option.key}>
                  {t(option.value)}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
        <MuiBox sx={{ paddingBottom: '24px' }} />
        {React.useMemo(() => {
          const GrantType = () => (
            <AliexpressAppSecret select label={t('grant_type')}>
              {[
                { label: 'Client Credentials', value: 'client_credentials' },
                { label: 'Password', value: 'password' },
              ].map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </AliexpressAppSecret>
          );

          const componentBy: Record<AuthType, React.FunctionComponent> = {
            none: () => <></>,
            token_path_body: () => <></>,
            basic_auth: () => (
              <FormGrid
                components={[
                  <AccountName label={t('username')} />,
                  <Password label={t('password')} />,
                ]}
              />
            ),
            bearer_token: () => (
              <FormGrid
                components={[
                  <WoocommerceConsumerKey label={t('bearer_token')} />,
                ]}
              />
            ),
            bearer_token_body: () => (
              <FormGrid
                components={[
                  <WoocommerceConsumerKey label={t('bearer_token_body_url')} />,
                  <WoocommerceConsumerSecret
                    label={t('bearer_token_body_params')}
                  />,
                ]}
              />
            ),
            oauth_2: () => (
              <FormGrid
                components={[
                  <UnleashedApiId />,
                  <WoocommerceConsumerKey label={t('walmart_client_id')} />,
                  <WoocommerceConsumerSecret
                    label={t('walmart_client_secret')}
                  />,
                  <UnleashedApiKey label={t('scope')} />,
                  <GrantType />,
                ]}
              />
            ),
            oauth_2_with_password: () => (
              <FormGrid
                components={[
                  <UnleashedApiId />,
                  <WoocommerceConsumerKey label={t('walmart_client_id')} />,
                  <WoocommerceConsumerSecret
                    label={t('walmart_client_secret')}
                  />,
                  <AccountName label={t('username')} />,
                  <Password label={t('password')} />,
                  <UnleashedApiKey label={t('scope')} />,
                  <GrantType />,
                ]}
              />
            ),
            oauth_2_for_odoo: () => (
              <FormGrid
                components={[
                  <UnleashedApiId />,
                  <WoocommerceConsumerSecret label={t('database')} />,
                  <AccountName label={t('username')} />,
                  <Password label={t('password')} />,
                ]}
              />
            ),
          };

          const Component = componentBy[authType] || componentBy['none'];
          return <Component />;
        }, [authType, t])}
        <MuiBox sx={{ marginTop: '24px' }} />
        <Icon
          type="default"
          onClick={() => {
            setOpen(!open);
          }}
          icon={open ? faChevronUp : faChevronDown}
          style={{
            cursor: 'pointer',
            color: theme.palette.link,
            transform: 'translateY(2px)',
            marginLeft: '8px',
          }}
        />
        <Button
          variant="text"
          size="small"
          id={
            open
              ? 'feed-manager-on-more-settings'
              : 'feed-manager-off-more-settings'
          }
          onClick={() => setOpen(!open)}
          sx={{ padding: '0px 8px' }}
        >
          {open ? t('hide_settings') : t('more_settings')}
        </Button>
        {open && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <Params name="_headerParams" title={t('header_params')} />
            <MuiBox sx={{ marginTop: '24px' }} />
            <Params name="_callParams" title={t('query_params')} />
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid components={[<BodyRaw />]} />
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid components={[<FtpWhitelist />]} />
          </>
        )}
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function MicrosoftSharepoint() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/microsoft-sharepoint-connection-method-1ffyziz/?bust=1716363076896">
        <Access helpText={t('sharepoint_authorize_tooltips')} />
        <MuiBox sx={{ marginTop: '32px' }} />
        <SourceUrl
          label={t('shared_link')}
          helperText={t('microsoft_sharepoint_url_tooltips')}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function BackblazeB2() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <S3AccessKeyId />,
            <S3SecretAccessKey />,
            <S3BucketName label={t('bucket_name')} />,
            <PathToFile label={t('key')} helperText={t('s3_url_tooltips')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function S3() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/amazon-s3-read-file-connection-method-1afaa4n/">
        <FormGrid
          components={[
            <S3AccessKeyId />,
            <S3SecretAccessKey />,
            <S3BucketName label={t('bucket_name')} />,
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={t('s3_url_tooltips')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function CustomLogin() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/3rd-party-download-with-login-4pftet/">
        <FormGrid
          components={[
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
            <TextField name="customLoginField" label={t('login_field_name')} />,
            <TextField
              name="customLoginPasswordField"
              label={t('password_field_name')}
            />,
            <SourceUrl
              label={t('source_url')}
              helperText={t('url_tooltips')}
            />,
          ]}
        />
        <FormGrid components={[<FtpWhitelist />]} />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function DropboxApi() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/dropbox-with-password-connection-method-1lzy4rf/">
        <FormGrid
          components={[
            <SourceUrl
              label={t('shared_link')}
              helperText={t('dropbox_api_url_tooltips')}
            />,
            <Password label={t('link_password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function Dropbox() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/dropbox-connection-method-1m6l545/">
        <SourceUrl
          label={t('shared_link')}
          helperText={t('dropbox_url_tooltips')}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function Box() {
  const { t } = useTranslation();
  const { control, watch } = useFeedManagerFormContext();
  const isPublicFile = watch('publicFile');
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/box-connection-method-1emny9x/">
        {isPublicFile === false && (
          <FormGrid
            components={[<Access helpText={t('box_authorize_tooltips')} />]}
          />
        )}
        <FormGrid
          components={[
            isPublicFile ? (
              <SourceUrl
                label={t('source_url')}
                helperText={<span>{t('source_url_helptext')}</span>}
              />
            ) : (
              <PathToFile
                label={t('box_label')}
                helperText={
                  <>
                    <span>{t('box_tooltips')}</span>
                    <span>{t('box_extra_info')}</span>
                  </>
                }
              />
            ),
          ]}
        />
        <Switch
          control={control}
          name="publicFile"
          label={t('box_public_file')}
          helperText={t('public_file_helper_text')}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function OnlineFile() {
  const { t } = useTranslation();
  const { watch } = useFeedManagerFormContext();
  const sourceAuth = watch('_authType.sourceAuth');

  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/download-url-connection-method-1irchxv/">
        <SourceAuth />
        {sourceAuth && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid
              components={[
                <AccountName label={t('login')} />,
                <Password label={t('password')} />,
              ]}
            />
          </>
        )}
        <MuiBox sx={{ marginTop: '32px' }} />
        <SourceUrl label={t('source_url')} helperText={t('url_tooltips')} />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function OneDriveFile() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/one-drive-connection-method-1ggaphi/">
        <Access helpText={t('one_drive_authorize_tooltips')} />
        <MuiBox sx={{ marginTop: '32px' }} />
        <FormGrid
          components={[
            <TextField
              name="oneDriveShareUrl"
              control={control}
              label={t('shared_link')}
              helperText={t('source_url_can_obtain')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function GoogleDriveV3() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/google-drive-connection-method-1xk770b/'
            : 'https://help.stock-sync.com/en/article/google-drive-connection-method-1xk770b/'
        }
      >
        <FormGrid
          components={[
            <SourceUrl
              label={t('shared_link')}
              helperText={t('google_drive_url_tooltips')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function GoogleSheetPublished() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/google-sheet-published-connection-method-po8m8b/?bust=1744346964123">
        <MuiBox sx={{ marginTop: '32px' }} />
        <SourceUrl
          label={t('source_url')}
          helperText={t('google_url_tooltips')}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FormGrid components={[<SkipTotalRows />]} />
      </FileSettingsCard>
    </>
  );
}

export function Bewicked() {
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-bewicked-usa-1392e68/'
            : 'https://help.stock-sync.com/en/article/update-products-from-bewicked-usa-bmm1wv/'
        }
      >
        <AutoCompleted />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function XTrader() {
  return (
    <SourceTypesCard helpLink="">
      <AutoCompleted />
    </SourceTypesCard>
  );
}

export function Feed4akidShopifyStore() {
  return (
    <SourceTypesCard helpLink="">
      <AutoCompleted />
    </SourceTypesCard>
  );
}

export function OneOnOneWholeSale() {
  return (
    <SourceTypesCard helpLink="">
      <AutoCompleted />
    </SourceTypesCard>
  );
}

export function GoogleSpreadsheetOut() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/write-to-google-sheets-connection-method-export-ho17ug/">
        <FormGrid
          components={[
            <SourceUrl
              label={t('source_host')}
              helperText={t('google_url_tooltips')}
            />,
            <SheetName
              label={t('sheet_name_or_sheet_index')}
              helperText={t('help_text_sheet_name_or_sheet_index')}
            />,
          ]}
        />
        <Stack spacing="24px" sx={{ maxWidth: '500px', marginTop: '16px' }}>
          <Switch
            control={control}
            name="clearGoogleSheet"
            label={t('replace_all')}
            helperText={t('clear_google_sheet')}
          />
          <HasHeader
            label={t('include_header')}
            helperText={t('include_header_info')}
          />
          <Switch
            control={control}
            name="googleOutInsert"
            label={t('create_new_line')}
            helperText={t('google_out_insert')}
          />
        </Stack>
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function GoogleSpreadsheetIn() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/google-sheets-10rjeah/">
        <FormGrid
          components={[
            <SourceUrl
              label={t('shared_link')}
              helperText={t('google_url_tooltips')}
            />,
            <SheetName
              label={t('sheet_name_or_sheet_index')}
              helperText={t('help_text_sheet_name_or_sheet_index')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FormGrid components={[<SkipTotalRows />]} />
        <HasHeader />
      </FileSettingsCard>
    </>
  );
}

export function GoogleShopping() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  const [{ data, fetching }] = useQuery({ query: GetGoogleShoppingCategories });

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/google-shopping-feed-1bjr0f3/">
        <FormGrid
          components={[
            <AccountName
              label={t('merchant_id')}
              helperText={
                <Link to="https://help.stock-sync.com/en/article/google-shopping-feed-1bjr0f3/">
                  Before authorizing the connection, please follow this guide to
                  make sure you’re ready.
                </Link>
              }
            />,
            <Autocomplete
              name="googleShoppingCategory"
              control={control}
              label={t('google_shopping_category')}
              disabled={fetching}
              loading={fetching}
              options={data?.getGoogleShoppingCategories?.data || []}
              isOptionEqualToValue={(option, value) => option === value}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <Access helpText={t('google_shopping_authorize_tooltips')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EtsyIn() {
  return (
    <Etsy helpLink="https://stocksync.crisp.help/en/article/read-from-etsy-connection-method-2yb6jh/" />
  );
}

export function EtsyOut() {
  return (
    <Etsy helpLink="https://help.stock-sync.com/en/article/write-to-etsy-connection-method-export-uyr4o4/" />
  );
}

function Etsy({ helpLink }: Pick<SourceTypesCardProps, 'helpLink'>) {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink={helpLink}>
        <AccountName
          label={t('store_name')}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  https://www.etsy.com/shop/
                </InputAdornment>
              ),
            },
          }}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <Access helpText={t('etsy_authorize_tooltips')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function FtpOut() {
  return (
    <Ftp
      helpLink="https://help.stock-sync.com/en/article/upload-to-ftp-csv-onlyexport-1javcv8/"
      fileSettings={<HasHeader />}
    />
  );
}

export function FtpsMultiple() {
  return <FtpIn helpLink="" />;
}

export function FtpMultiple() {
  return (
    <FtpIn helpLink="https://help.stock-sync.com/en/article/ftp-multiple-files-connection-method-1dlozsr/" />
  );
}

export function FtpsImplicit() {
  return (
    <FtpIn helpLink="https://help.stock-sync.com/en/article/ftp-tls-enabled-connection-method-3h5sa4/" />
  );
}

export function Ftps() {
  return (
    <FtpIn helpLink="https://help.stock-sync.com/en/article/ftp-tls-enabled-connection-method-3h5sa4/" />
  );
}

export function FtpIn({
  helpLink = 'https://stocksync.crisp.help/en/article/ftp-download-connection-method-1vq0qxg/',
}: Pick<SourceTypesCardProps, 'helpLink'>) {
  const { watch } = useFeedManagerFormContext();
  const ftpRename: UserProfile['ftpRename'] = watch('ftpRename');
  return (
    <Ftp
      helpLink={helpLink}
      sourceTypeComponents={[
        <FtpRename />,
        ftpRename === 2 ? <CustomFileName /> : <></>,
      ]}
      fileSettings={<FileSettingsByFileFormat />}
    />
  );
}

interface FtpProps extends Pick<SourceTypesCardProps, 'helpLink'> {
  sourceTypeComponents?: JSX.Element[];
  fileSettings: React.ReactNode;
}

function Ftp({ sourceTypeComponents = [], fileSettings, helpLink }: FtpProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const [open, setOpen] = React.useState(false);

  return (
    <>
      <SourceTypesCard helpLink={helpLink}>
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <SourceUrl label={t('host')} helperText={t('ftp_url_tooltips')} />,
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={
                <Stack component="span">
                  {t('directory_file_name_tooltip')}
                  <Trans i18nKey="filename_with_timestamp">
                    <span>
                      For filename with timestamp, please click{' '}
                      <Link to="https://stocksync.crisp.help/en/article/wildcard-and-dynamic-date-14cjfcy/">
                        here
                      </Link>
                      for more info
                    </span>
                  </Trans>
                </Stack>
              }
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
        {sourceTypeComponents.length > 0 && (
          <MuiBox sx={{ marginTop: '24px' }}>
            <Icon
              type="default"
              onClick={() => {
                setOpen(!open);
              }}
              icon={open ? faChevronUp : faChevronDown}
              style={{
                cursor: 'pointer',
                color: theme.palette.link,
                transform: 'translateY(2px)',
                marginLeft: '8px',
              }}
            />
            <Button
              variant="text"
              size="small"
              id={
                open
                  ? 'feed-manager-on-more-settings'
                  : 'feed-manager-off-more-settings'
              }
              onClick={() => setOpen(!open)}
              sx={{ padding: '0px 8px' }}
            >
              {open ? t('hide_settings') : t('more_settings')}
            </Button>
          </MuiBox>
        )}
        {open && sourceTypeComponents.length > 0 && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid components={[...sourceTypeComponents]} />
          </>
        )}
      </SourceTypesCard>
      <FileSettingsCard>{fileSettings}</FileSettingsCard>
    </>
  );
}

export function SftpOut() {
  return <Sftp helpLink="" fileSettings={<HasHeader />} />;
}

export function SftpIn() {
  return (
    <Sftp
      helpLink="https://stocksync.crisp.help/en/article/sftp-connection-method-1smx8d1/"
      fileSettings={<FileSettingsByFileFormat />}
    />
  );
}

interface SftpProps extends Pick<SourceTypesCardProps, 'helpLink'> {
  fileSettings?: React.ReactNode;
}

export function Sftp({ helpLink, fileSettings }: SftpProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { watch } = useFeedManagerFormContext();
  const [open, setOpen] = React.useState(false);
  const ftpRename: UserProfile['ftpRename'] = watch('ftpRename');
  return (
    <>
      <SourceTypesCard helpLink={helpLink}>
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <SourceUrl label={t('host')} helperText={t('ftp_url_tooltips')} />,
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={
                <Stack component="span">
                  <span>{t('directory_file_name_tooltip')}</span>
                  <Trans i18nKey="filename_with_timestamp">
                    <span>
                      For filename with timestamp, please click{' '}
                      <Link to="https://stocksync.crisp.help/en/article/wildcard-and-dynamic-date-14cjfcy/">
                        here
                      </Link>
                      for more info
                    </span>
                  </Trans>
                </Stack>
              }
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
        <MuiBox sx={{ marginTop: '24px' }} />
        <Icon
          type="default"
          onClick={() => {
            setOpen(!open);
          }}
          icon={open ? faChevronUp : faChevronDown}
          style={{
            cursor: 'pointer',
            color: theme.palette.link,
            transform: 'translateY(2px)',
            marginLeft: '8px',
          }}
        />
        <Button
          variant="text"
          size="small"
          id={
            open
              ? 'feed-manager-on-more-settings'
              : 'feed-manager-off-more-settings'
          }
          onClick={() => setOpen(!open)}
          sx={{ padding: '0px 8px' }}
        >
          {open ? t('hide_settings') : t('more_settings')}
        </Button>
        {open && (
          <>
            <MuiBox sx={{ marginTop: '24px' }} />
            <FormGrid
              components={[
                <FtpRename />,
                <TextField
                  multiline
                  name="sshKey"
                  label={t('ssh_key')}
                  helperText={`${t('ssh_key_tooltips')} ${t('pem_format')}`}
                />,
                ftpRename === 2 ? <CustomFileName /> : <></>,
              ]}
            />
          </>
        )}
      </SourceTypesCard>
      <FileSettingsCard>{fileSettings}</FileSettingsCard>
    </>
  );
}

export function GoogleDrive() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/google-drive-connection-method-1xk770b/">
        <SourceUrl
          label={t('shared_link')}
          helperText={t('google_drive_url_tooltips')}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function EmailIn() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control, getValues } = useFeedManagerFormContext();
  const email = getValues('email');
  const { enqueueSnackbar } = useSnackbar();

  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/receive-email-connection-method-nz6g8n/">
        <Stack spacing="32px">
          <TextField
            name="email"
            label={t('email')}
            control={control}
            helperText={t('email_helptext')}
            slotProps={{
              htmlInput: { readOnly: true },
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => {
                        navigator.clipboard.writeText(email).then(() => {
                          enqueueSnackbar('Copied to clipboard!', {
                            variant: 'success',
                          });
                        });
                      }}
                    >
                      <i
                        className="fak fa-copy1"
                        style={{ color: theme.palette.link }}
                      />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <Link to="https://stocksync.crisp.help/en/article/receive-email-connection-method-nz6g8n/">
            <Icon type="default" icon={faCircleQuestion} />{' '}
            {t('receive_email_connection')}
          </Link>
          <Link to="https://help.stock-sync.com/en/article/how-to-get-gmail-forwarding-confirmation-code-for-stock-sync-oy18vr/">
            <Icon type="default" icon={faCircleQuestion} />{' '}
            {t('how_gmail_forwarding')}
          </Link>
          <Link to="https://help.stock-sync.com/en/article/why-email-process-only-runs-at-a-certain-time-period-1c7nmh1/?bust=1722244173808">
            <Icon type="default" icon={faCircleQuestion} />{' '}
            {t('email_process_run_certain_time')}
          </Link>
        </Stack>
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function Feed() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control } = useFeedManagerFormContext();
  const { currentStore } = useCurrentStore();
  const { feeds: userProfiles } = useDashboardFeeds();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/automatically-remove-or-archive-product-when-not-found-in-update-feed-auto-clear-discontinued-1en0o8i/">
        <FormGrid
          components={[
            <TextField
              select
              name="sourceUrl"
              label={t('update_feed_label')}
              control={control}
            >
              {React.useMemo(
                () =>
                  userProfiles
                    .filter((feed) => feed.feedType === 'update')
                    .filter((feed) => feed.profileName !== 'Snappy') // TODO: fastTrack === false better?
                    .map((feed) => (
                      <MenuItem key={feed.id} value={feed.id.toString()}>
                        {feed.profileName} {!feed.enabled && '(deleted)'}
                      </MenuItem>
                    )),
                [userProfiles]
              )}
            </TextField>,
          ]}
        />
        <Typography
          variant="body2"
          sx={{ color: theme.palette.grey[300], paddingTop: '16px' }}
        >
          {currentStore.provider === 'bigcommerce' ||
          currentStore.provider === 'wix'
            ? t('wix_feed_helptext')
            : t('remove_feed_helptext')}
        </Typography>
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function UploadedFile() {
  const { currentFeed } = useCurrentFeed();
  return (
    <>
      <SourceTypesCard helpLink="https://stocksync.crisp.help/en/article/upload-file-connection-method-b70pl3/">
        <FileUploader feed={currentFeed} runAsSample={true} />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function SupplierWithoutSetting() {
  return (
    <SourceTypesCard helpLink="">
      <AutoCompleted />
    </SourceTypesCard>
  );
}

export function Ebihr() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <Autocomplete
              name="connectionSettings.catalog"
              control={control}
              label={t('catalog')}
              options={ebihrOptions}
              isOptionEqualToValue={(option, value) => option === value}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function AmrodV2() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/support/solutions/folders/***********">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.customer_code"
              label={t('customer_code')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function Storeden() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/support/solutions/articles/44002464999-add-products-from-storeden'
            : 'https://help.stock-sync.com/support/solutions/articles/44002465006-update-products-from-storeden'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('key')} />,
            <WoocommerceConsumerSecret label={t('exchange')} />,
            <AliexpressAppSecret label={t('brand_id')} />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function RichmondInteriors() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-richmond-interiors-utm88h/'
            : 'https://help.stock-sync.com/en/article/update-products-from-richmond-interiors-xd929n/'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('Customer Code')} />,
            <WoocommerceConsumerSecret label={t('Customer Secret')} />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function AzureBlob() {
  const { t } = useTranslation();
  const {
    currentStore: {
      feedConstants: { file_rename_options: fileRenameOptions },
    },
  } = useCurrentStore();

  const customOptions = fileRenameOptions.filter(
    (val) => !['append_date_to_my_file', 'rename_my_file'].includes(val.value)
  );

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/azure-blob-connection-method-w6qgcq/">
        <FormGrid
          components={[
            <AccountName label={t('storage_account')} />,
            <SourceUrl label={t('sas_token_url')} />,
            <WoocommerceConsumerKey label={t('container')} />,
            <PathToFile label={t('blob')} helperText={t('blob_help_text')} />,
            <FtpRename customOptions={customOptions} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function Rekman() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-rekman-89onem/?bust=*************'
            : 'https://help.stock-sync.com/en/article/update-products-from-rekman-156gzlh/?bust=*************'
        }
      >
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('public_key')} />,
            <WoocommerceConsumerSecret label={t('private_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Hypercel() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <SourceUrl label={t('source_url')} />,
            <AccountName label={t('login')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Backmarket() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('email')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function UKDistributor() {
  const { control, setValue, getValues } = useFeedManagerFormContext();
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();

  const filterOptions = [
    { label: 'Category', value: 'category' },
    { label: 'Brand', value: 'brand' },
  ];

  const filter = useWatch({
    control,
    name: '_ukDistrbutorCategory',
  });

  const selectionOptions =
    filter === 'category'
      ? ukDistributorCategoryOptions
      : ukDistributorBrandOptions;

  const initialCategory =
    getValues('connectionSettings.products_url.category') ?? '';

  return (
    <>
      <SourceTypesCard
        helpLink={
          currentFeed.humanizeFeedType === 'import'
            ? 'https://help.stock-sync.com/en/article/add-products-from-uk-distributor-d36ykz/'
            : 'https://help.stock-sync.com/en/article/update-products-from-uk-distributor-1o0zs9b/'
        }
      >
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.auth.id"
              label={t('id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.auth.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              select
              name="_ukDistrbutorCategory"
              label={t('filter')}
              defaultValue={
                // e.g. "Brand&Search=CAT,Brand&Search=COMFYLUX"
                initialCategory.includes('Brand&') ? 'brand' : 'category'
              }
              onChange={() => {
                setValue('connectionSettings.products_url.category', '');
                setValue('_ukDistrbutorCategoryOptions', []);
              }}
            >
              {filterOptions.map((option) => (
                <MenuItem key={option.label} value={option.value}>
                  {t(option.label)}
                </MenuItem>
              ))}
            </TextField>,
            <Autocomplete
              multiple
              allowBlank={false}
              openOnFocus
              control={control}
              name="_ukDistrbutorCategoryOptions"
              label={t('selection')}
              defaultValue={initialCategory
                .split(',')
                .filter(Boolean)
                .map((o) => ({ value: o, label: '' }))}
              onChange={(_, selectedOptions) => {
                setValue(
                  'connectionSettings.products_url.category',
                  selectedOptions.map((o) => o.value).join(',')
                );
              }}
              options={selectionOptions}
              getOptionLabel={(option) =>
                // if user selects
                option.label ||
                // API only returns category id, find matching label
                selectionOptions.find((c) => c.value === option.value)?.label ||
                // default to empty
                ''
              }
              isOptionEqualToValue={(option, value) =>
                `${option?.value}` === `${value?.value}`
              }
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function Synnex() {
  const { control, setValue, getValues } = useFeedManagerFormContext();
  const { t } = useTranslation();
  const connectionSettings = getValues('connectionSettings');

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.auth.id"
              label={t('id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.auth.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <Autocomplete
              multiple
              allowBlank={false}
              openOnFocus
              control={control}
              name="_synnexCategoryOptions"
              label={t('category')}
              defaultValue={(connectionSettings.products_url?.category ?? '')
                .split(',')
                .filter(Boolean)
                .map((v) => ({ value: v, label: '' }))}
              onChange={(_, selectedOptions) => {
                setValue(
                  'connectionSettings.products_url.category',
                  selectedOptions.map((o) => o.value).join(',')
                );
              }}
              options={synnexCategoryOptions}
              getOptionLabel={(option) =>
                // if user selects
                option.label ||
                // API only returns category id, find matching label
                synnexCategoryOptions.find((c) => c.value === option.value)
                  ?.label ||
                // default to empty
                ''
              }
              isOptionEqualToValue={(option, value) =>
                `${option?.value}` === `${value?.value}`
              }
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function Simpro() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[<WoocommerceConsumerSecret label={t('api_token')} />]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Verifone() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/update-products-from-verifone-c75oqs/">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <AliexpressAppId label={t('chain_id')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Busyx() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AliexpressAppId label={t('api_log')} />,
            <WoocommerceConsumerKey label={t('api_key')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Combisteel() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function Aapd() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.token"
              label={t('token')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function EmailLink() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control, getValues } = useFeedManagerFormContext();
  const email = getValues('email');
  const { enqueueSnackbar } = useSnackbar();

  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/receive-email-connection-method-with-url-1jfuw0y/?bust=1719995114458">
        <Stack spacing="32px">
          <TextField
            name="email"
            label={t('email')}
            control={control}
            helperText={t('email_helptext')}
            slotProps={{
              htmlInput: { readOnly: true },
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => {
                        navigator.clipboard.writeText(email).then(() => {
                          enqueueSnackbar('Copied to clipboard!', {
                            variant: 'success',
                          });
                        });
                      }}
                    >
                      <i
                        className="fak fa-copy1"
                        style={{ color: theme.palette.link }}
                      />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
        </Stack>
        <MuiBox sx={{ marginTop: '24px' }} />
        <PathToFile label={t('url_keyword')} />
        <Stack spacing="32px" sx={{ marginTop: '32px' }}>
          <Link to="https://help.stock-sync.com/en/article/receive-email-connection-method-with-url-1jfuw0y/?bust=1719995114458">
            <Icon type="default" icon={faCircleQuestion} />{' '}
            {t('receive_url_email_connection')}
          </Link>
          <Link to="https://help.stock-sync.com/en/article/how-to-get-gmail-forwarding-confirmation-code-for-stock-sync-oy18vr/">
            <Icon type="default" icon={faCircleQuestion} />{' '}
            {t('how_gmail_forwarding')}
          </Link>
          <Link to="https://help.stock-sync.com/en/article/why-email-process-only-runs-at-a-certain-time-period-1c7nmh1/?bust=1722244173808">
            <Icon type="default" icon={faCircleQuestion} />{' '}
            {t('email_process_run_certain_time')}
          </Link>
        </Stack>
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function Midocean() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              select
              control={control}
              name="connectionSettings.type"
              label={t('type')}
              defaultValue="product"
              style={{ textTransform: 'capitalize' }}
            >
              {['product', 'price', 'stock'].map((type) => (
                <MenuItem
                  key={type}
                  value={type}
                  style={{ textTransform: 'capitalize' }}
                >
                  {type}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function WholeCell() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <AliexpressAppId label={t('inventory_status')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function QbpV2() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/quality-bicycle-products-connection-method-iik0mc/">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.warehouse_codes"
              label={t('warehouse_codes')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function ShercoNetwork() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function ProjectVerte() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.api_secret"
              label={t('api_secret')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function LikewiseFloors() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.auth.id"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              type="password"
              name="connectionSettings.auth.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function Bling() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_secret')} />,
            <Access helpText={t('bling_authorize_tooltips')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Onshopfront() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <WoocommerceConsumerKey label={t('walmart_client_id')} />,
            <WoocommerceConsumerSecret label={t('walmart_client_secret')} />,
            <Access helpText={t('onshopfront_authorize_tooltips')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function DropshippingB2bApi() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.user_id"
              label={t('userid')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.portal_id"
              label={t('pid')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.language_id"
              label={t('lid')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.identification_code"
              label={t('identification_code')}
              placeholder={t('please_fill_in')}
            />,
            <Tags
              name="connectionSettings.brand_id"
              control={control}
              label={t('brand_id')}
              placeholder={t('please_fill_in')}
              settings={{ maxTags: 5 }}
              helperText={t('brand_id_helptext')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function RectronDataFeed() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.product_line"
              label={t('product_line')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Syscom() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.client_id"
              label={t('walmart_client_id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.client_secret"
              label={t('walmart_client_secret')}
              placeholder={t('please_fill_in')}
            />,
            <Tags
              name="connectionSettings.category"
              control={control}
              label={t('categories_field')}
              placeholder={t('please_fill_in')}
              settings={{ maxTags: 5 }}
            />,
            <Tags
              name="connectionSettings.brand"
              control={control}
              label={t('brand_field')}
              placeholder={t('please_fill_in')}
              settings={{ maxTags: 5 }}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Veloconnect() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.buyers_id"
              label={t('buyers_id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function WosActionSports() {
  const { control, setValue, getValues } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <Autocomplete
              multiple
              allowBlank={false}
              openOnFocus
              control={control}
              name="_wosActionSportsOptions"
              label={t('category')}
              defaultValue={(
                getValues('_authType.woocommerceConsumerKey') ?? ''
              )
                .split(',')
                .filter(Boolean)
                .map((o) => ({ value: o, label: '' }))}
              onChange={(_, selectedOptions) => {
                setValue(
                  '_authType.woocommerceConsumerKey',
                  selectedOptions.map((o) => o.value).join(',')
                );
              }}
              options={wosActionSportsOptions}
              getOptionLabel={(option) =>
                // if user selects
                option.label ||
                // API only returns category id, find matching label
                wosActionSportsOptions.find((c) => c.value === option.value)
                  ?.label ||
                // default to empty
                ''
              }
              isOptionEqualToValue={(option, value) =>
                `${option?.value}` === `${value?.value}`
              }
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EmagMarketplace() {
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function TopTex() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function MaweenTrading() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              type="number"
              control={control}
              name="connectionSettings.days"
              label={t('days')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.grant_type"
              label={t('grant_type')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function AliexpressScraper() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/aliexpress-marketplace-connection-method-1oa8xq5/?bust=1719893139567">
        <Tags
          name="connectionSettings.url_list"
          control={control}
          label={t('url_list')}
          settings={{
            delimiters: /\+\+/g,
          }}
          helperText={t('sku_list')}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Furnicher() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Ascolour() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.email"
              label={t('key_in_email')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.subscription_key"
              label={t('subscription_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function AtelierAPI() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.usr_mkt"
              label={t('usr_mkt')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.pwd_mkt"
              label={t('pwd_mkt')}
              placeholder={t('please_fill_in')}
            />,
            <Tags
              name="connectionSettings.retailer_list"
              control={control}
              label={t('retailer_list')}
              placeholder={t('please_fill_in')}
              settings={{ maxTags: 5 }}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function MetriQuadri() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <SourceUrl label={t('source_url')} />,
            <TextField
              control={control}
              name="connectionSettings.key"
              label={t('key')}
            />,
            <TextField
              control={control}
              name="connectionSettings.db_context"
              label={t('db_context')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ShootingWarehouseInventory() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.customer_number"
              label={t('customer_number')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.db_context"
              label={t('source_host')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <DatePicker
              format="YYYY-MM-DD"
              control={control}
              name="connectionSettings.date"
              label={t('datepicker')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function HeoCsv() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Tiktokshop() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <Access helpText={t('tiktokshop_authorize_tooltips')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function AzureSQLDatabase() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <AccountName label={t('username')} />,
            <Password label={t('password')} />,
            <SourceUrl label={t('host')} />,
            <AliexpressAppId label={t('database_name')} />,
            <BodyRaw multiline label={t('custom_query')} />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function MyBertus() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.account_id"
              label={t('account_id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.ocp_key"
              label={t('ocp_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function Efashion() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.store_code"
              label={t('store_code')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Silverbene() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.start_date"
              label={t('start_date')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.end_date"
              label={t('end_date')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.token"
              label={t('api_token')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Seagull() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.email"
              label={t('key_in_email')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Marashoes() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.app_id"
              label={t('app_id')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Phorest() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.business_id"
              label={t('business_id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.branch_id"
              label={t('branch_id')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Bems() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.secret_key"
              label={t('secret_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.list_filter"
              label={t('apply_optional_filter')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Prenta() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function ShipNetwork() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.client_id"
              label={t('walmart_client_id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.user_identifier"
              label={t('api_user_identifier')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.user_secret"
              label={t('api_user_secret')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function JdmProducts() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.email"
              label={t('key_in_email')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.account_code"
              label={t('account_code')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function Mstgolf() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <DatePicker
              format="YYYY-MM-DD"
              control={control}
              name="connectionSettings.date"
              label={t('datepicker')}
            />,
            <TextField
              control={control}
              name="connectionSettings.endpoint"
              label={t('endpoint')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.region"
              label={t('data_center_region')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function HofmanAnimalCare() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.language"
              label={t('language_code')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function GrupoLogi() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.secret_client"
              label={t('walmart_client_secret')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function EngelDropship() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.email"
              label={t('key_in_email')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.language"
              label={t('language')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function BannedApparel() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <SourceUrl label={t('shared_link')} />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function TuscanyLeather() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.email"
              label={t('key_in_email')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              select
              control={control}
              name="connectionSettings.type"
              label={t('type')}
            >
              {['product', 'stock-price'].map((item) => (
                <MenuItem key={item} value={item}>
                  {t(item)}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function HyundaiNl() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.access_key"
              label={t('access_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function GatewayNwg() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.token"
              label={t('token')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              type="number"
              name="connectionSettings.max_page"
              label={t('max_page')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.assortment_id"
              label={t('assortment_id')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function OvernightMountings() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <Autocomplete
              name="connectionSettings.category"
              control={control}
              label={t('category')}
              options={overnightMountingOptions}
              isOptionEqualToValue={(option, value) => option === value}
            />,
          ]}
        />
        <Switch
          control={control}
          name="connectionSettings.use_dynamic_price"
          label={t('dynamic_price')}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function PremierWD() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <Tags
              name="connectionSettings.item_numbers"
              control={control}
              label={t('category_ids')}
              placeholder={t('please_fill_in')}
              settings={{ maxTags: 5 }}
            />,
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              select
              control={control}
              name="connectionSettings.type"
              label={t('type')}
            >
              {['inventory', 'pricing'].map((item) => (
                <MenuItem key={item} value={item}>
                  {t(item)}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function ForgeMotorsport() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function Ulefone() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.api_secret"
              label={t('api_secret')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Keno() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();

  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function DigitalOcean() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="https://help.stock-sync.com/en/article/amazon-s3-read-file-connection-method-1afaa4n/">
        <FormGrid
          components={[
            <S3AccessKeyId />,
            <S3SecretAccessKey />,
            <S3BucketName label={t('space_bucket_name')} />,
            <PathToFile
              label={t('directory_and_file_name')}
              helperText={t('s3_url_tooltips')}
            />,
            <TextField
              control={control}
              name="connectionSettings.data_center_region"
              label={t('data_center_region')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '24px' }} />
        <TestSourceType />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function SapBusinessOneSlApi() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.login_url"
              label={t('login_url')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.data_url"
              label={t('source_url')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.data_key"
              label={t('data_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.token_key"
              label={t('token_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
        <MuiBox sx={{ marginTop: '32px' }} />
        <Params name="_headerParams" title={t('login_header')} />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Siigo() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.access_key"
              label={t('access_key')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function TimcoApi() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}
export function Magento() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.host"
              label={t('host')}
              placeholder={t('please_fill_in')}
              helperText={t('host_helper')}
            />,
            <TextField
              control={control}
              name="connectionSettings.token"
              label={t('token')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function SquareStore() {
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <SourceUrl
              label={t('public_token')}
              helperText={t('shopify_store_tooltips')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function MarathonLeisure() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.consumer_key"
              label={t('consumer_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.consumer_secret"
              label={t('consumer_secret')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.access_token"
              label={t('access_token')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.access_token_secret"
              label={t('access_token_secret')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function Schake() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.client_id"
              label={t('walmart_client_id')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.client_secret"
              label={t('walmart_client_secret')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <AutoCompleted />
      </FileSettingsCard>
    </>
  );
}

export function FilesAPI() {
  const { t } = useTranslation();
  const { control } = useFeedManagerFormContext();
  return (
    <>
      <SourceTypesCard helpLink="">
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.api_key"
              label={t('api_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.file_path"
              label={t('file_path')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
      <FileSettingsCard>
        <FileSettingsByFileFormat />
      </FileSettingsCard>
    </>
  );
}

export function SvsVetchannel() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function PuckatorApi() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.username"
              label={t('username')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('password')}
              placeholder={t('please_fill_in')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}

export function SynnexFtp() {
  const { watch } = useFeedManagerFormContext();
  const ftpRename: UserProfile['ftpRename'] = watch('ftpRename');
  return (
    <>
      <Ftp
        sourceTypeComponents={[
          <FtpRename />,
          ftpRename === 2 ? <CustomFileName /> : <></>,
        ]}
        fileSettings={<AutoCompleted />}
      />
    </>
  );
}

export function DepasqualaSalonSystems() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.login"
              label={t('iws_login')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.password"
              label={t('iws_password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.customer"
              label={t('customer_no')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              select
              control={control}
              name="connectionSettings.option"
              label={t('iws_option')}
            >
              {['itemcatJSON', 'itemlocationJSON'].map((item) => (
                <MenuItem key={item} value={item}>
                  {t(item)}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}
export function GaiaSuite() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.gaia_url"
              label={t('gaia_url')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.api_account"
              label={t('api_account')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.api_password"
              label={t('api_password')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.site"
              label={t('site')}
            />,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}
export function ElegantMoments() {
  return (
    <SourceTypesCard helpLink="">
      <AutoCompleted />
    </SourceTypesCard>
  );
}
export function DresscodeCloud() {
  const { control } = useFeedManagerFormContext();
  const { t } = useTranslation();
  return (
    <>
      <SourceTypesCard>
        <FormGrid
          components={[
            <TextField
              control={control}
              name="connectionSettings.client"
              label={t('dresscode_cloud_client')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.channel_key"
              label={t('dresscode_cloud_channel_key')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              control={control}
              name="connectionSettings.subscription"
              label={t('dresscode_cloud_subscription')}
              placeholder={t('please_fill_in')}
            />,
            <TextField
              select
              control={control}
              name="connectionSettings.endpoint"
              label={t('dresscode_cloud_endpoint')}
            >
              {['products', 'stocks'].map((item) => (
                <MenuItem key={item} value={item}>
                  {t(item)}
                </MenuItem>
              ))}
            </TextField>,
          ]}
        />
      </SourceTypesCard>
    </>
  );
}
