import { faCircleQuestion, faPen } from '@fortawesome/pro-light-svg-icons';
import {
  Box,
  Divider,
  InputAdornment,
  MenuItem,
  Alert as Mui<PERSON><PERSON>t,
  Stack,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { Crisp } from 'crisp-sdk-web';
import Cookies from 'js-cookie';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { useQuery } from 'urql';

import { Alert } from '@/components/Alert';
import { useAppBridgeContext } from '@/components/AppBridge';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { CustomAppBar } from '@/components/CustomAppBar';
import { Dialog } from '@/components/Dialog';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { PopoverConfirm } from '@/components/PopoverConfirm';
import { Slider } from '@/components/Slider';
import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { Tooltip } from '@/components/Tooltip';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useFirstSnappyFeed } from '@/hooks/useFirstSnappyFeed';
import { useIsEmbeddedApp } from '@/hooks/useIsEmbeddedApp';
import { useMatches } from '@/hooks/useMatches';
import { useUpdateStore } from '@/hooks/useUpdateStore';
import i18n from '@/i18n';
import { GetStoresByPublicToken } from '@/queries/Users';
import * as routes from '@/routes';
import { DEMO_STORES, TIME_ZONES } from '@/shared/config';

import { StoreProductDialog } from './components/StoreProductDialog';

export function AdvancedPreference() {
  const { currentStore } = useCurrentStore();
  const {
    warningZeroQtyUpdate,
    warningRemoveProducts,
    timezone,
    autoGetHigherQty,
    publicToken,
    prestashopApiKey,
    locale,
  } = currentStore;

  const { t } = useTranslation();
  const theme = useTheme();
  const matchesSm = useMatches('sm');
  const [, updateStore] = useUpdateStore();
  const { isEmbeddedApp } = useIsEmbeddedApp();
  const [{ data }] = useQuery({
    query: GetStoresByPublicToken,
  });

  const aboveMd = useMediaQuery(theme.breakpoints.up('md'));
  const { enqueueSnackbar } = useSnackbar();
  const [shareablePublicTokenDialog, setShareablePublicTokenDialog] =
    useState(false);
  const handleCloseShareablePublicTokenDialog = () =>
    setShareablePublicTokenDialog(false);

  const [isStoreKeyEditable, setStoreKeyEditable] = useState(false);
  const [errorMessage, setErrorMessage] = React.useState<JSX.Element[]>([]);
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null
  );

  const { control, handleSubmit, formState, watch, reset } = useForm({
    values: {
      warningZeroQtyUpdate,
      warningRemoveProducts,
      timezone,
      autoGetHigherQty,
      publicToken,
      prestashopApiKey,
      locale,
    },
  });

  const onSubmit = handleSubmit(async (values) => {
    await updateStore(
      {
        warningZeroQtyUpdate: `${values.warningZeroQtyUpdate}`,
        warningRemoveProducts: values.warningRemoveProducts,
        autoGetHigherQty: values.autoGetHigherQty,
        timezone: values.timezone,
        publicToken: values.publicToken,
        prestashopApiKey: values.prestashopApiKey,
        locale: values.locale,
      },
      {
        onSuccess({ enqueueSnackbar: snackbar }) {
          snackbar('Advance setting updated.', {
            variant: 'success',
          });
          reset();
        },
        onError({ errors }) {
          if (errors?.length > 0) {
            const errorElements = errors.map((error, index) => (
              <p key={index}>{error}</p>
            ));
            setErrorMessage(errorElements);
          }
          setTimeout(() => {
            setErrorMessage([]);
          }, 5000);
        },
      }
    );
    i18n.changeLanguage(values.locale);
  });

  console.log('formState.isDirty', formState.isDirty);

  useAppBridgeContext({
    showSaveBar: formState.isDirty,
    onSave: () => {
      onSubmit();
    },
    onDismiss: () => {
      reset();
    },
  });

  const providerOrder = [
    'shopify',
    'wix',
    'bigcommerce',
    'woocommerce',
    'squarespace',
    'ekm',
  ];
  const providerDisplayNames: { [key: string]: string } = {
    shopify: 'Shopify',
    wix: 'Wix',
    bigcommerce: 'BigCommerce',
    woocommerce: 'WooCommerce',
    squarespace: 'SquareSpace',
    ekm: 'EKM',
  };
  interface Shop {
    shopify_domain: string;
  }

  interface Store {
    provider: string;
    shops: Shop[];
  }

  const groupByProvider = (stores: Store[]) => {
    const grouped: { [key: string]: Shop[] } = {};

    stores.forEach((store) => {
      if (!grouped[store.provider]) {
        grouped[store.provider] = [];
      }
      grouped[store.provider] = grouped[store.provider].concat(
        store.shops.map((shop) => ({
          shopify_domain: shop.shopify_domain,
        }))
      );
    });

    return grouped;
  };

  const groupedStores = data?.queryStoresByPublicToken
    ? groupByProvider(data.queryStoresByPublicToken)
    : {};
  const sortedGroupedStores = Object.keys(groupedStores).sort((a, b) => {
    return providerOrder.indexOf(a) - providerOrder.indexOf(b);
  });

  const locales = [
    { key: 'en', value: 'en' },
    { key: 'ja', value: 'ja' },
    { key: 'zh_cn', value: 'zh_cn' },
    { key: 'german', value: 'german' },
    { key: 'french', value: 'french' },
    { key: 'polish', value: 'polish' },
    { key: 'spanish', value: 'spanish' },
    { key: 'dutch', value: 'dutch' },
    { key: 'tr', value: 'tr' },
  ];

  return (
    <form onSubmit={onSubmit}>
      <CustomAppBar
        title={t('advance')}
        redirectUrl={isEmbeddedApp ? '' : routes.home}
        btnGroup={
          <Button
            size="small"
            type="submit"
            variant="contained"
            loading={formState.isSubmitting}
          >
            {t('save')}
          </Button>
        }
      />
      {errorMessage.length > 0 && <Alert>{errorMessage}</Alert>}
      <Card sx={{ paddingBottom: theme.spacing(4) }}>
        <Stack spacing="32px">
          <Typography variant="h5" sx={{ fontWeight: 500 }}>
            {t('public_token')}
          </Typography>
          <Stack
            direction={matchesSm ? 'column' : 'row'}
            sx={{
              justifyContent: 'center',
              alignItems: 'baseline',
            }}
            spacing={2}
          >
            <TextField
              control={control}
              name="publicToken"
              label="Public Token"
              helperText={t('help_text_public_token')}
              slotProps={{
                htmlInput: { readOnly: true },
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <Tooltip variant="default" placement="right" title="copy">
                        <IconButton
                          onClick={() => {
                            navigator.clipboard
                              .writeText(publicToken)
                              .then(() => {
                                enqueueSnackbar('Copied to clipboard!', {
                                  variant: 'success',
                                });
                              });
                          }}
                        >
                          <i
                            className="fak fa-copy1"
                            style={{ color: theme.palette.link }}
                          />
                        </IconButton>
                      </Tooltip>
                    </InputAdornment>
                  ),
                },
              }}
            />
            {(data?.queryStoresByPublicToken || []).length > 0 && (
              <Button
                variant="text"
                size="small"
                onClick={() => setShareablePublicTokenDialog(true)}
              >
                View seller(s)
              </Button>
            )}
          </Stack>
          <Dialog
            open={shareablePublicTokenDialog}
            onClose={handleCloseShareablePublicTokenDialog}
          >
            <Dialog.Title>
              {t('other_store_using_same_public_token')}
              <Divider sx={{ margin: '10px 0px 0px 0px' }} />
            </Dialog.Title>

            <Dialog.Content sx={{ paddingTop: 0 }}>
              <Box sx={{ marginTop: '8px' }}>
                {sortedGroupedStores.map((provider) => (
                  <Box key={provider}>
                    <Typography
                      variant="h5"
                      sx={{ fontWeight: 'bold', marginBottom: '8px' }}
                    >
                      {providerDisplayNames[provider]}
                    </Typography>
                    {groupedStores[provider].map((shop, index) => (
                      <Box key={index} sx={{ marginBottom: '8px' }}>
                        <Typography variant="h6">
                          {shop.shopify_domain}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                ))}
              </Box>
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                variant="outlined"
                size="small"
                onClick={handleCloseShareablePublicTokenDialog}
              >
                {t('cancel')}
              </Button>
            </Dialog.Actions>
          </Dialog>
          {['prestashop'].includes(currentStore.provider) && (
            <TextField
              control={control}
              name="prestashopApiKey"
              label="API Key"
              slotProps={{
                htmlInput: { readOnly: !isStoreKeyEditable },
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={(event) => {
                          if (isStoreKeyEditable) {
                            setStoreKeyEditable(false);
                          } else {
                            setAnchorEl(event.currentTarget);
                          }
                        }}
                      >
                        <Icon type="default" icon={faPen} />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
          )}
        </Stack>
        <StoreFilters />
      </Card>
      <Box sx={{ marginTop: '32px' }} />
      <Card>
        <Stack spacing={4}>
          <Typography variant="h5" sx={{ fontWeight: 500 }}>
            {t('language_timezone')}
          </Typography>{' '}
          <TextField
            control={control}
            name="locale"
            select
            label="Preferred language"
          >
            {locales.map((l) => {
              return (
                <MenuItem key={l.key} value={l.key}>
                  {t(l.value)}
                </MenuItem>
              );
            })}
          </TextField>
          <TextField control={control} name="timezone" select label="Time zone">
            {TIME_ZONES.map((tz) => {
              const key = Object.keys(tz)[0];
              return (
                <MenuItem key={key} value={key}>
                  {tz[key]}
                </MenuItem>
              );
            })}
          </TextField>
        </Stack>
      </Card>
      <Box sx={{ marginTop: '32px' }} />
      <Card>
        {['shopify', 'wix', 'bigcommerce'].includes(currentStore.provider) && (
          <Stack spacing={3}>
            <Typography variant="h5" sx={{ fontWeight: 500 }}>
              {t('alert_setting')}
            </Typography>{' '}
            <Stack spacing="40px" sx={{ padding: '32px 24px 24px 24px' }}>
              <Slider
                control={control}
                name="warningZeroQtyUpdate"
                sx={{ maxWidth: '800px', paddingBottom: '32px' }}
                label={
                  <Box>
                    <Typography variant="h6">
                      {t('zero_qty_update_alert')}
                    </Typography>
                    {watch('warningZeroQtyUpdate') < 40 && (
                      <MuiAlert severity="warning" sx={{ marginTop: '24px' }}>
                        {t('quantity_zero_alert_helptext')}
                      </MuiAlert>
                    )}
                  </Box>
                }
                min={10}
                helperText={
                  <Typography
                    sx={{
                      fontSize: '14px',
                      paddingLeft: { sm: theme.spacing(3) },
                    }}
                  >
                    {/* https://react.i18next.com/misc/using-with-icu-format */}
                    <Trans
                      i18nKey="quantity_zero_alert_helpertext"
                      defaults="Halt process when <0>0%</0> of products
                        quantity set to zero. Once the  selected above is reached,
                        the update stops and a warning message is displayed" // fallback to this message when no translation defined and will display as sample text when debug
                      components={[
                        // style the component interpolation
                        <span style={{ fontWeight: 600, fontSize: '20px' }}>
                          {warningZeroQtyUpdate}%
                        </span>,
                      ]}
                      values={{
                        warningZeroQtyUpdate: watch('warningZeroQtyUpdate'),
                      }}
                    />
                  </Typography>
                }
              />
              <Slider
                control={control}
                name="warningRemoveProducts"
                label={
                  <Box>
                    <Typography variant="h6">
                      {t('remove_product_alert')}
                    </Typography>
                  </Box>
                }
                sx={{ maxWidth: '800px' }}
                helperText={
                  <Typography
                    sx={{
                      fontSize: '14px',
                      paddingLeft: { sm: theme.spacing(3) },
                    }}
                  >
                    <Trans
                      i18nKey="remove_zero_alert_helpetext"
                      defaults="Halt process when <0>0%</0> of products being removed. Once the threshold selected
                        above is reached, removing products will stop and
                        warning message is displayed." // fallback to this message when no translation defined and will display as sample text when debug
                      components={[
                        // style the component interpolation
                        <span style={{ fontWeight: 600, fontSize: '20px' }}>
                          {warningRemoveProducts}%
                        </span>,
                      ]}
                      values={{
                        warningRemoveProducts: watch('warningRemoveProducts'),
                      }}
                    />
                  </Typography>
                }
              />
            </Stack>
          </Stack>
        )}
        {currentStore.provider === 'woocommerce' && ( // Only need the warningRemoveProducts
          <Box sx={{ display: 'flex' }}>
            <Stack sx={{ maxWidth: '605px', paddingLeft: '24px' }}>
              <Slider
                control={control}
                name="warningRemoveProducts"
                label={
                  <Box>
                    <Typography variant="h6">
                      {t('remove_product_alert')}
                    </Typography>
                  </Box>
                }
                helperText={
                  <Typography
                    sx={{
                      fontSize: '14px',
                      maxWidth: '605px',
                      paddingLeft: { sm: theme.spacing(3) },
                    }}
                  >
                    <Trans
                      i18nKey="remove_zero_alert_helpetext"
                      defaults="Halt process when <0>0%</0> of products being removed. Once the threshold selected
                                above is reached, removing products will stop and
                                warning message is displayed." // fallback to this message when no translation defined and will display as sample text when debug
                      components={[
                        // style the component interpolation
                        <span style={{ fontWeight: 600, fontSize: '20px' }}>
                          {warningRemoveProducts}%
                        </span>,
                      ]}
                      values={{
                        warningRemoveProducts: watch('warningRemoveProducts'),
                      }}
                    />
                  </Typography>
                }
              />
            </Stack>
          </Box>
        )}
        <Stack
          spacing="8px"
          sx={{ marginTop: theme.spacing(5), paddingLeft: '24px' }}
        >
          <Switch
            control={control}
            name="autoGetHigherQty"
            label={t('use_highest_quantity_value')}
            disabled
          />

          <Box
            component="ul"
            sx={{
              listStyleType: 'none',
              display: 'flex',
              ...(aboveMd
                ? { paddingLeft: '58px', flexDirection: 'row' }
                : { paddingLeft: '47px', flexDirection: 'column' }),
            }}
          >
            <Box
              component="li"
              sx={{
                position: 'relative',
                paddingLeft: 'calc(20px + 4px)',
              }}
            >
              <Link to="https://help.stock-sync.com/en/article/use-highest-quantity-value-when-the-same-sku-detected-from-multiple-feeds-in-stock-sync-1tgknp4/?bust=1717045613851">
                <Box sx={{ position: 'absolute', left: 0 }}>
                  <Icon type="default" icon={faCircleQuestion} fontSize={20} />
                </Box>
                {t('learn_more')}
              </Link>
            </Box>
            {DEMO_STORES.indexOf(
              Cookies.get('stocksync_current_store') ?? ''
            ) === -1 && (
              <Box
                component="li"
                sx={{
                  position: 'relative',
                  ...(aboveMd ? { marginLeft: '49px' } : { marginTop: '8px' }),
                }}
              >
                <Button
                  variant="text"
                  onClick={() => Crisp.chat.open()}
                  sx={{ padding: 0 }}
                >
                  <Typography variant="body1">Contact support</Typography>
                </Button>
                <Typography
                  sx={{
                    display: 'inline',
                    color: theme.palette.grey[600],
                  }}
                >
                  {' to enable this option'}
                </Typography>
              </Box>
            )}
          </Box>
        </Stack>
      </Card>
      <EditablePopover
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        onConfirm={() => {
          setStoreKeyEditable(true);
          setAnchorEl(null);
        }}
      />
    </form>
  );
}

function EditablePopover({ onClose, onConfirm, anchorEl }) {
  const { t } = useTranslation();
  return (
    <PopoverConfirm
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      cancelButtonProps={{ children: t('no'), onClick: onClose }}
      okButtonProps={{ children: t('yes'), onClick: onConfirm }}
    >
      {t('confirm_edit_store_key')}
    </PopoverConfirm>
  );
}

function StoreFilters() {
  const { t } = useTranslation();
  const matchesSm = useMatches('sm');
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [query] = useFirstSnappyFeed();
  const snappyFeed = query.data?.firstSnappyFeed;

  if (!snappyFeed) return <></>;

  return (
    <Stack
      direction={matchesSm ? 'column' : 'row'}
      spacing={2}
      sx={{ alignItems: 'baseline', marginTop: '32px' }}
    >
      <Button variant="contained" onClick={handleOpen}>
        {t('apply_filter')}
      </Button>
      <Typography variant="body1">{t('apply_filter_label')}</Typography>
      <StoreProductDialog
        key={String(open)}
        open={open}
        onClose={handleClose}
        snappyFeed={snappyFeed}
      />
    </Stack>
  );
}
