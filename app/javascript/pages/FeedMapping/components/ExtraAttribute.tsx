import {
  faArrowLeft,
  faCircleQuestion,
} from '@fortawesome/pro-light-svg-icons';
import {
  Al<PERSON>,
  Box,
  Drawer,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import * as React from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { Trans, Translation, useTranslation } from 'react-i18next';
import { match, P } from 'ts-pattern';

import { Button } from '@/components/Button';
import { Icon } from '@/components/Icon';
import { Link } from '@/components/Link';
import { Loading } from '@/components/Loading';
import { RadioGroup } from '@/components/RadioGroup';
import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDisplayLanguages } from '@/hooks/useDisplayLanguages';
import { useMatches } from '@/hooks/useMatches';

import { useFeedMappingFormContext } from '../useFeedMappingFormContext';
import { AllowBackOrder } from './fields/AllowBackOrder';
import { CaseConvert } from './fields/CaseConvert';
import { CompareAtPrice } from './fields/CompareAtPrice';
import { CompareAtPriceRegion } from './fields/CompareAtPriceRegion';
import { Cost } from './fields/Cost';
import { Depth } from './fields/Depth';
import { Description } from './fields/Description';
import { FindField } from './fields/FindField';
import { HarmonizedSystemCode } from './fields/HarmonizedSystemCode';
import { Height } from './fields/Height';
import { IgnoreWords } from './fields/IgnoreWords';
import { Image } from './fields/Image';
import { Metafield } from './fields/Metafield';
import { Option } from './fields/Option';
import { Policy } from './fields/Policy';
import { Price } from './fields/Price';
import { PriceRegion } from './fields/PriceRegion';
import { ProductIdentifier } from './fields/ProductIdentifier';
import { ProductTitle } from './fields/ProductTitle';
import { ProductType } from './fields/ProductType';
import { Published } from './fields/Published';
import { Quantity } from './fields/Quantity';
import { ReplaceField } from './fields/ReplaceField';
import { Status } from './fields/Status';
import { Tag } from './fields/Tag';
import { Taxable } from './fields/Taxable';
import { TaxCode } from './fields/TaxCode';
import { TrackQuantity } from './fields/TrackQuantity';
import { Url } from './fields/Url';
import { VideoLink } from './fields/VideoLink';
import { Weight } from './fields/Weight';
import { Width } from './fields/Width';
import { FormDivider } from './FormDivider';

const componentByTitle = {
  product_id: ProductIdentifier,
  images: Image,
  video_links: VideoLink,
  weight: Weight,
  product_title: ProductTitle,
  body_html: Description,
  tags: Tag,
  published: Published,
  policy: Policy,
  harmonized_system_code: HarmonizedSystemCode,
  handle: Url,
  taxable: Taxable,
  tax_code: TaxCode,
  vendor: Vendor,
  standardized_product_type: StandardizedProductType,
  status: Status,
  requires_shipping: RequiresShipping,
  quantity: Quantity,
  price: Price,
  compare_price_at: CompareAtPrice,
  cost: Cost,
  option1: Option,
  option2: Option,
  option3: Option,
  variant_group: VariantGroup,
  track_quantity: TrackQuantity,
  discount_amount: DiscountAmount,
  visible: Visible,
  metafields_global_title_tag: Seo,
  metafields_global_description_tag: Seo,
  sale_price: Price,
  categories: Categories,
  backorders: AllowBackOrder,
  stock_status: StockStatus,
  retail_price: Price,
  free_shipping: FreeShipping,
  preorder_release_date: PreorderReleaseDate,
  category: Categories,
  purchase_availability: PurchaseAvailability,
  quantity_quality_control: Quantity,
  quantity_safety_stock: Quantity,
  quantity_damaged: Quantity,
  quantity_incoming: Quantity,
  quantity_reserved: Quantity,
  price_region: PriceRegion,
  compare_price_at_region: CompareAtPriceRegion,
  short_description: Description,
  condition: ConditionField,
  show_condition: ShowConditionField,
  product_type: ProductType,
  height: Height,
  depth: Depth,
  width: Width,
  brand: Brand,
};

interface ExtraAttributeProps {
  extraAttributeDrawer: string;
  index?: number;
  mappingTitle: string;
  onSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
  setExtraAttributeDrawer: React.Dispatch<React.SetStateAction<string>>;
}

export function ExtraAttribute(props: ExtraAttributeProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const matchesSm = useMatches('sm');
  const { trigger } = useFormContext();
  const { currentStore } = useCurrentStore();

  // trigger rhf validation, show red dot on settings button if error
  const triggerValidation = React.useCallback(
    async () => trigger(undefined, { shouldFocus: false }),
    [trigger]
  );

  const onClose = () => {
    // triggering immediately causes render jank for large nested fields, delay
    setTimeout(() => {
      triggerValidation();
    }, 1500);
    props.onSubmit();
    props.setExtraAttributeDrawer('');
  };

  const openDrawer =
    Boolean(props.mappingTitle) &&
    props.extraAttributeDrawer === props.mappingTitle;
  const isMetafield = props.mappingTitle?.includes('metafield_');
  const isCustomField = props.mappingTitle?.includes('custom_field_');
  const isProductAttribute = props.mappingTitle?.includes('product_attribute_');
  const isMetadata = props.mappingTitle?.includes('metadata_');
  const isAdditionalInfoSection = props.mappingTitle?.includes(
    'additional_info_section_'
  );
  const isSyncField = Boolean(componentByTitle[props.mappingTitle]);

  const SyncFieldExtraAttribute = React.useMemo(() => {
    return componentByTitle[props.mappingTitle];
  }, [props.mappingTitle]);
  return (
    <Drawer
      anchor="right"
      open={openDrawer}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: matchesSm ? '100%' : theme.spacing(54),
          p: theme.spacing(2),
          pl: theme.spacing(3),
          pb: theme.spacing(10),
        },
      }}
    >
      <React.Suspense fallback={<Loading />}>
        <Stack
          direction="row"
          spacing={2}
          sx={{
            mt: theme.spacing(4),
            mb: theme.spacing(2),
            alignItems: 'center',
          }}
        >
          <Button
            variant="outlined"
            size="small"
            onClick={onClose}
            id="close-extra-setting"
          >
            <Icon type="default" icon={faArrowLeft} size="lg" />
          </Button>
          <Typography variant="h5">
            <span>
              {t(
                match({
                  isMetafield,
                  isCustomField,
                  isProductAttribute,
                  isMetadata,
                  isAdditionalInfoSection,
                  provider: currentStore.provider,
                  name: props.mappingTitle,
                })
                  .with({ isMetafield: true }, () => 'metafield')
                  .with({ isCustomField: true }, () => 'custom_field')
                  .with({ isProductAttribute: true }, () => 'product_attribute')
                  .with(
                    { isAdditionalInfoSection: true },
                    () => 'additional_info_section'
                  )
                  .with({ isMetadata: true }, () => 'metadata')
                  .with(
                    {
                      provider: 'shopify',
                      name: 'quantity',
                    },
                    () => t('product_quantity')
                  )
                  .with({ provider: 'bigcommerce', name: 'quantity' }, () =>
                    t('bigcommerce_quantity')
                  )
                  .with({ provider: 'woocommerce', name: 'quantity' }, () =>
                    t('stock_quantity')
                  )
                  .otherwise(() => props.mappingTitle)
              )}
            </span>
          </Typography>
        </Stack>
        {isMetafield && (
          <Link to="https://help.stock-sync.com/en/article/metafields-ptbnev/">
            <Icon
              type="default"
              icon={faCircleQuestion}
              style={{
                fontSize: '20px',
                marginRight: '4px',
                marginLeft: '4px',
              }}
            />
            {t('learn_more')}
          </Link>
        )}
        {currentStore.provider === 'woocommerce' && (
          <Link
            to={match({ isMetadata, isProductAttribute })
              .with(
                { isMetadata: true },
                () =>
                  'https://help.stock-sync.com/en/article/metadata-field-woocommerce-18efvi6/'
              )
              .with(
                { isProductAttribute: true },
                () =>
                  'https://help.stock-sync.com/en/article/product-attributes-field-woocommerce-1mgl6mq/'
              )
              .otherwise(() => mappingLinksForWC[props.mappingTitle] ?? '')}
          >
            <Icon
              type="default"
              icon={faCircleQuestion}
              style={{
                fontSize: '20px',
                marginRight: '4px',
                marginLeft: '4px',
              }}
            />
            {t('learn_more')}
          </Link>
        )}
        {mappingLinks[props.mappingTitle] &&
          currentStore.provider !== 'woocommerce' && (
            <Link to={mappingLinks[props.mappingTitle]}>
              <Icon
                type="default"
                icon={faCircleQuestion}
                style={{
                  fontSize: '20px',
                  marginRight: '4px',
                  marginLeft: '4px',
                }}
              />
              {t('learn_more')}
            </Link>
          )}
        <Box sx={{ mt: theme.spacing(4) }} />

        {match({
          isMetafield,
          isCustomField,
          isProductAttribute,
          isMetadata,
          isAdditionalInfoSection,
          isSyncField,
        })
          .with({ isMetafield: true }, () => <Metafield index={props.index} />)
          .with({ isCustomField: true }, () => (
            <CustomField index={props.index} />
          ))
          .with({ isProductAttribute: true }, () => (
            <ProductAttribute index={props.index} />
          ))
          .with({ isMetadata: true }, () => <Metadata index={props.index} />)
          .with({ isAdditionalInfoSection: true }, () => (
            <AdditionalInfoSection index={props.index} />
          ))
          .with({ isSyncField: true }, () => (
            <SyncFieldExtraAttribute
              index={props.index}
              title={props.mappingTitle}
            />
          ))
          .otherwise(() => (
            <Typography
              variant="body1"
              sx={{
                textAlign: 'start',
              }}
            >
              {t('no_extra_settings')}
            </Typography>
          ))}
      </React.Suspense>
    </Drawer>
  );
}

const languageCode = [
  {
    code: 'zh-CN',
    language: 'Chinese (Simplified)',
  },
  {
    code: 'zh-TW',
    language: 'Chinese (Traditional)',
  },
  {
    code: 'hr-HR',
    language: 'Croatian',
  },
  {
    code: 'cs',
    language: 'Czech',
  },
  {
    code: 'da',
    language: 'Danish',
  },
  {
    code: 'nl',
    language: 'Dutch',
  },
  {
    code: 'en',
    language: 'English',
  },
  {
    code: 'fi',
    language: 'Finnish',
  },
  {
    code: 'fr',
    language: 'French',
  },
  {
    code: 'de',
    language: 'German',
  },
  {
    code: 'el',
    language: 'Greek',
  },
  {
    code: 'hu',
    language: 'Hungarian',
  },
  {
    code: 'id-ID',
    language: 'Indonesian',
  },
  {
    code: 'it',
    language: 'Italian',
  },
  {
    code: 'ja',
    language: 'Japanese',
  },
  {
    code: 'ko',
    language: 'Korean',
  },
  {
    code: 'lt-LT',
    language: 'Lithuanian',
  },
  {
    code: 'nb',
    language: 'Norwegian',
  },
  {
    code: 'pl',
    language: 'Polish',
  },
  {
    code: 'pt-BR',
    language: 'Portuguese (Brazil)',
  },
  {
    code: 'pt-PT',
    language: 'Portuguese (Portugal)',
  },
  {
    code: 'ro-RO',
    language: 'Romanian',
  },
  {
    code: 'ru',
    language: 'Russian',
  },
  {
    code: 'sk-SK',
    language: 'Slovak',
  },
  {
    code: 'sl-SI',
    language: 'Slovenian',
  },
  {
    code: 'es',
    language: 'Spanish',
  },
  {
    code: 'sv',
    language: 'Swedish',
  },
  {
    code: 'th',
    language: 'Thai',
  },
  {
    code: 'tr',
    language: 'Turkish',
  },
  {
    code: 'vi',
    language: 'Vietnamese',
  },
];

function StandardizedProductType({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  const { currentFeed } = useCurrentFeed();

  return (
    <>
      <TextField
        select
        name={`syncFieldSettings.${index}.extra_attributes.language_code`}
        control={control}
        label={t('label_language_code')}
        helperText={t('language_code_help_text')}
      >
        {languageCode.map((option) => (
          <MenuItem key={option.code} value={option.code}>
            {option.language}
          </MenuItem>
        ))}
      </TextField>
      <FormDivider />
      {currentFeed.humanizeFeedType !== 'export' && (
        <Switch
          control={control}
          name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
          label={t('skip_if_empty')}
        />
      )}
    </>
  );
}

function VariantGroup({ index }: { index: number }) {
  const { t } = useTranslation();
  const { control, watch, setValue } = useFeedMappingFormContext();
  const { currentStore } = useCurrentStore();
  const hasExistingProductIdentifier = watch(
    `productOptionsSyncField.${index}.extra_attributes._hasExistingProductIdentifier`
  );

  return (
    <>
      {currentStore.provider === 'shopify' && (
        <>
          <Switch
            control={control}
            name="importSort"
            label={t('import_sort_label')}
            helperText={t('help_text_import_sort')}
          />
          <FormDivider />
          <Switch
            control={control}
            name={`productOptionsSyncField.${index}.extra_attributes._hasExistingProductIdentifier`}
            label={t('merge_variants')}
            onChange={(_, checked) => {
              if (checked === false) {
                setValue(
                  `productOptionsSyncField.${index}.extra_attributes.existing_product_identifier`,
                  ''
                );
              }
            }}
          />
          {hasExistingProductIdentifier && (
            <>
              <Alert severity="info">
                <Typography variant="button" sx={{ fontWeight: 400 }}>
                  {t('merge_variants_helptext')}
                </Typography>
              </Alert>
              <Box sx={{ padding: '24px' }}>
                <RadioGroup
                  name={`productOptionsSyncField.${index}.extra_attributes.existing_product_identifier`}
                  control={control}
                  options={[
                    {
                      value: 'barcode',
                      label: t('barcode'),
                    },
                    { value: 'handle', label: t('handle_option') },
                    { value: 'product_title', label: t('product_title') },
                  ]}
                />
              </Box>
              <TextField
                name={`productOptionsSyncField.${index}.extra_attributes.existing_product_tag`}
                control={control}
                label={t('merge_variant_will_tagged_with')}
              />
            </>
          )}
        </>
      )}
    </>
  );
}

function RequiresShipping({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  const { currentFeed } = useCurrentFeed();
  return (
    <>
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.false_values`}
        control={control}
        label={t('label_false_values')}
        helperText={t('help_text_false_values')}
      />
      <FormDivider />
      {currentFeed.humanizeFeedType !== 'export' && (
        <Switch
          control={control}
          name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
          label={t('skip_if_empty')}
        />
      )}
    </>
  );
}

function DiscountAmount({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <Switch
      control={control}
      name={`syncFieldSettings.${index}.extra_attributes.discount_type`}
      label={t('label_discount_type')}
      helperText={t('help_text_discount_type_percentage')}
    />
  );
}

function Visible({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.is_visible_value`}
        control={control}
        label={t('label_is_visible')}
        helperText={t('help_text_is_visible')}
      />
      <FormDivider />
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.is_not_visible_value`}
        control={control}
        label={t('label_is_not_visible')}
        helperText={t('help_text_is_not_visible')}
      />
    </>
  );
}

function Seo({ index }) {
  const { t } = useTranslation();
  const { control } = useFeedMappingFormContext();
  return (
    <>
      <FindField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.find`}
      />
      <FormDivider />
      <ReplaceField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.replace`}
      />
      <FormDivider />
      <Switch
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
        label={t('skip_if_empty')}
      />
    </>
  );
}

function Metadata({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <Switch
        control={control}
        name={`metafields.${index}.extra_attributes.create_key_if_not_found`}
        label={t('label_create_key_if_not_found')}
        helperText={t('help_text_create_key_if_not_found')}
      />
      <FormDivider />
      <TextField
        name={`metafields.${index}.extra_attributes.metadata_key`}
        control={control}
        label={t('label_metadata_key')}
        helperText={t('help_text_metadata_key')}
      />
    </>
  );
}

function Categories({ index }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control } = useFeedMappingFormContext();
  const { currentFeed } = useCurrentFeed();
  const { currentStore } = useCurrentStore();
  const { languages } = useDisplayLanguages();
  const watchOriginalLanguage = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.original_language`,
  });
  return (
    <>
      {currentStore.provider !== 'wix' && (
        <>
          <TextField
            name={`syncFieldSettings.${index}.extra_attributes.category_name`}
            control={control}
            label={t('category_name')}
            helperText={t('category_name_helptext')}
          />
          <FormDivider />
        </>
      )}
      <FindField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.find`}
      />
      <FormDivider />
      <ReplaceField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.replace`}
      />
      {currentFeed.feedType !== 'import' && (
        <>
          <FormDivider />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.override_categories`}
            label={t('label_categories')}
            helperText={t('help_text_categories')}
          />
        </>
      )}
      {currentStore.provider === 'bigcommerce' && (
        <>
          <FormDivider />
          <TextField
            name={`syncFieldSettings.${index}.extra_attributes.parent_category_id`}
            control={control}
            label={t('parent_category_id')}
            helperText={t('parent_category_helptext')}
          />
        </>
      )}
      {match({
        provider: currentStore.provider,
        enableTranslationFeature: currentStore.enableTranslationFeature,
      })
        .with(
          {
            provider: P.when((type) => type === 'wix'),
          },
          {
            enableTranslationFeature: true,
          },
          () => (
            <>
              <FormDivider />
              <Box sx={{ mb: theme.spacing(6) }} />
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.original_language`}
                label={t('translate_from')}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>
              <Box sx={{ mb: theme.spacing(6) }} />
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.returned_language`}
                label={t('translate_to')}
                disabled={watchOriginalLanguage === 'no change'}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>
            </>
          )
        )
        .otherwise(() => (
          <></>
        ))}
    </>
  );
}

function StockStatus({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.in_stock_flag`}
        control={control}
        label={t('label_instock_flag')}
        helperText={t('help_text_is_in_stock')}
      />
      <FormDivider />
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.out_stock_flag`}
        control={control}
        label={t('label_outstock_flag')}
        helperText={t('help_text_is_out_stock')}
      />
    </>
  );
}

function Vendor({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  const { currentFeed } = useCurrentFeed();
  return (
    <>
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <IgnoreWords index={index} />
          <FormDivider />
        </>
      )}

      <CaseConvert
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.case_convert`}
      />
      <FormDivider />
      {currentFeed.humanizeFeedType !== 'export' && (
        <Switch
          control={control}
          name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
          label={t('skip_if_empty')}
        />
      )}
    </>
  );
}

function FreeShipping({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.with_free_shipping_value`}
        control={control}
        label={t('label_with_free_shipping')}
        helperText={t('help_text_with_free_shipping')}
      />
      <FormDivider />
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.without_free_shipping_value`}
        control={control}
        label={t('label_without_free_shipping')}
        helperText={t('help_text_without_free_shipping')}
      />
    </>
  );
}

function PreorderReleaseDate({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <Switch
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.remove_preoder_on_date`}
        label={t('label_remove_preorder')}
        helperText={t('help_text_remove_preorder')}
      />
      <FormDivider smallGapBetweenComponent />
      <Switch
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.set_date_if_zero_qty`}
        label={t('label_set_date_if_zero_qty')}
        helperText={t('help_text_set_date_if_zero_qty')}
      />
      <FormDivider />
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.date_format`}
        control={control}
        label={t('label_date_format')}
        helperText={
          <Translation ns="translations">
            {() => (
              <Trans i18nKey="help_text_date_formats">
                <Box component="span">
                  Insert a date format which fits the date value in the feed.
                  E.g. %d %B %Y{' '}
                  <Link to="https://help.stock-sync.com/en/article/wildcard-and-dynamic-date-14cjfcy/">
                    <Icon
                      type="default"
                      icon={faCircleQuestion}
                      style={{
                        fontSize: '20px',
                        marginRight: '4px',
                        marginLeft: '4px',
                      }}
                    />
                    {t('learn_more')}
                  </Link>
                </Box>
              </Trans>
            )}
          </Translation>
        }
      />
      <FormDivider />
      <TextField
        name={`syncFieldSettings.${index}.extra_attributes.custom_preorder_message`}
        control={control}
        label={t('label_custom_preorder_message')}
        helperText={t('help_text_custom_preorder_message')}
      />
    </>
  );
}

function CustomField({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <TextField
        name={`metafields.${index}.extra_attributes.custom_field_key`}
        control={control}
        label={t('label_custom_field_name')}
      />
      <FormDivider />
      <Switch
        control={control}
        name={`metafields.${index}.extra_attributes.create_key_if_not_found`}
        label={t('label_create_key_if_not_found_bc')}
        helperText={t('help_text_create_key_if_not_found_bc')}
      />
    </>
  );
}

function ProductAttribute({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <Switch
        control={control}
        name={`woocommerceProductAttributes.${index}.extra_attributes.create_key_if_not_found`}
        label={t('label_create_product_attribute_if_not_found')}
        helperText={t('help_text_product_attribute_if_not_found')}
      />
      <FormDivider />
      <TextField
        name={`woocommerceProductAttributes.${index}.extra_attributes.custom_field_key`}
        control={control}
        label={t('product_attribute')}
        helperText={t('help_text_product_attribute_key')}
      />
    </>
  );
}

function PurchaseAvailability({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.purchase_available_flag`}
        label={t('label_purchase_available_flag')}
        helperText={t('help_text_purchase_available_flag')}
      />
      <FormDivider />
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.purchase_preorder_flag`}
        label={t('label_purchase_preorder_flag')}
        helperText={t('help_text_purchase_preorder_flag')}
      />
      <FormDivider />
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.purchase_disabled_flag`}
        label={t('label_purchase_disabled_flag')}
        helperText={t('help_text_purchase_disabled_flag')}
      />
    </>
  );
}

function ConditionField({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.condition_is_new`}
        label={t('new')}
      />
      <FormDivider />
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.condition_is_used`}
        label={t('used')}
      />
      <FormDivider />
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.condition_is_refurbished`}
        label={t('refurbished')}
      />
      <FormDivider />
      <Switch
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
        label={t('skip_if_empty')}
      />

      <FormDivider />
    </>
  );
}

function ShowConditionField({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.enable_condition`}
        label={t('enabled_indicator')}
      />
      <FormDivider />
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.disable_condition`}
        label={t('disabled_indicator')}
      />
      <FormDivider />
    </>
  );
}

function Brand({ index }) {
  const { control } = useFeedMappingFormContext();
  return (
    <>
      <FindField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.find`}
      />
      <FormDivider />
      <ReplaceField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.replace`}
      />
    </>
  );
}

function AdditionalInfoSection({ index }) {
  const { t } = useTranslation();
  const { control } = useFormContext();
  return (
    <>
      <Switch
        control={control}
        name={`metafields.${index}.extra_attributes.create_key_if_not_found`}
        label={t('label_additional_info_section_create_key_if_not_found')}
        helperText={t(
          'help_text_additional_info_section_create_key_if_not_found'
        )}
      />
      <FormDivider />
      <TextField
        name={`metafields.${index}.extra_attributes.section_title`}
        control={control}
        label={t('label_metadata_key')}
        helperText={t('help_text_metadata_key')}
      />
    </>
  );
}

const mappingLinks = {
  images:
    'https://help.stock-sync.com/en/article/product-images-field-13itu51/?bust=1715156533444',
  video_links:
    'https://help.stock-sync.com/en/article/product-video-links-field-rv8spl/?bust=1721897166148',
  taxable: 'https://stocksync.crisp.help/en/article/taxable-field-15day2j/',
  vendor: 'https://help.stock-sync.com/en/article/vendor-field-g5xsd4/',
  quantity: 'https://help.stock-sync.com/en/article/available-quantity-20bsw8/',
  price: 'https://stocksync.crisp.help/en/article/price-field-966ydg/',
  cost: 'https://help.stock-sync.com/en/article/cost-per-item-field-1a3ayu4/',
  compare_price_at:
    'https://help.stock-sync.com/en/article/compare-at-price-field-eyq8co/',
  product_title: 'https://help.stock-sync.com/en/article/title-field-4h88lw/',
  weight: 'https://stocksync.crisp.help/en/article/weight-field-sdrdqp/',
  body_html:
    'https://help.stock-sync.com/en/article/description-field-1qida1w/?bust=1708680445292',
  status: 'https://stocksync.crisp.help/en/article/status-field-1d0wdus/',
  published: 'https://help.stock-sync.com/en/article/published-field-775t6y/',

  standardized_product_type:
    'https://stocksync.crisp.help/en/article/product-category-field-1df0eeq/',
  policy:
    'https://help.stock-sync.com/en/article/inventory-policy-continue-selling-field-qge0e3/#1-1-enable-continue-selling-when-out-of-stockhttpshelpshopifycomenmanualproductsinventorygetting-started-with-inventoryselling-when-out-of-stock',
  tags: 'https://help.stock-sync.com/en/article/tags-field-tz7x6l/',
  product_id:
    'https://help.stock-sync.com/en/article/product-identifier-field-z71wxk/',
  variant_group:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  option1:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  option2:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  option3:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  harmonized_system_code:
    'https://help.stock-sync.com/en/article/harmonized-system-code-field-6lcmm0/',
  requires_shipping:
    'https://help.stock-sync.com/en/article/shipping-physical-field-10tk9s9/',
  metafields_global_description_tag:
    'https://help.stock-sync.com/en/article/update-seo-page-title-and-description-using-metafields-h6cwvq/',
  metafields_global_title_tag:
    'https://help.stock-sync.com/en/article/update-seo-page-title-and-description-using-metafields-h6cwvq/',
  template_suffix:
    'https://help.stock-sync.com/en/article/theme-template-field-1lf7qjz/',
  barcode: 'https://stocksync.crisp.help/en/article/barcode-field-1vkxn98/',
  product_url:
    'https://help.stock-sync.com/support/solutions/articles/44002412830-product-url-field',
  handle: 'https://stocksync.crisp.help/en/article/url-handle-field-c0h37f/',
  product_type:
    'https://help.stock-sync.com/en/article/product-type-field-e3hwlq/',
  track_quantity:
    'https://help.stock-sync.com/en/article/track-inventory-field-vtkw2f/',
  shopify_product_id:
    'https://help.stock-sync.com/en/article/shopify-product-id-field-1r80wlr/?bust=1739778092120',
  product_created_at:
    'https://help.stock-sync.com/en/article/product-date-creation-field-9wdnxd/?bust=1739779884278',
  variant_created_at:
    'https://help.stock-sync.com/en/article/variant-date-creation-field-2rp6lb/',
  price_region:
    'https://help.stock-sync.com/en/article/markets-price-field-s7zedz/',
  compare_price_at_region:
    'https://help.stock-sync.com/en/article/markets-compare-at-price-field-116ts84/',
};

const mappingLinksForWC: Record<string, string> = {
  images:
    'https://help.stock-sync.com/en/article/image-field-woocommerce-emajn7/',
  taxable: 'https://stocksync.crisp.help/en/article/taxable-field-15day2j/',
  vendor: 'https://help.stock-sync.com/en/article/vendor-field-g5xsd4/',
  quantity: 'https://help.stock-sync.com/en/article/available-quantity-20bsw8/',
  price: 'https://stocksync.crisp.help/en/article/price-field-966ydg/',
  cost: 'https://help.stock-sync.com/en/article/cost-per-item-field-1a3ayu4/',
  compare_price_at:
    'https://help.stock-sync.com/en/article/compare-at-price-field-eyq8co/',
  product_title: 'https://help.stock-sync.com/en/article/title-field-4h88lw/',
  weight: 'https://stocksync.crisp.help/en/article/weight-field-sdrdqp/',
  body_html:
    'https://help.stock-sync.com/en/article/description-field-1qida1w/?bust=1708680445292',
  status: 'https://stocksync.crisp.help/en/article/status-field-1d0wdus/',
  published: 'https://help.stock-sync.com/en/article/published-field-775t6y/',

  standardized_product_type:
    'https://stocksync.crisp.help/en/article/product-category-field-1df0eeq/',
  policy:
    'https://help.stock-sync.com/en/article/inventory-policy-continue-selling-field-qge0e3/#1-1-enable-continue-selling-when-out-of-stockhttpshelpshopifycomenmanualproductsinventorygetting-started-with-inventoryselling-when-out-of-stock',
  tags: 'https://help.stock-sync.com/en/article/tags-field-tz7x6l/',
  product_id:
    'https://help.stock-sync.com/en/article/product-identifier-field-z71wxk/',
  variant_group:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  option1:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  option2:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  option3:
    'https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246',
  harmonized_system_code:
    'https://help.stock-sync.com/en/article/harmonized-system-code-field-6lcmm0/',
  requires_shipping:
    'https://help.stock-sync.com/en/article/shipping-physical-field-10tk9s9/',
  metafields_global_description_tag:
    'https://help.stock-sync.com/en/article/update-seo-page-title-and-description-using-metafields-h6cwvq/',
  metafields_global_title_tag:
    'https://help.stock-sync.com/en/article/update-seo-page-title-and-description-using-metafields-h6cwvq/',
  template_suffix:
    'https://help.stock-sync.com/en/article/theme-template-field-1lf7qjz/',
  barcode: 'https://stocksync.crisp.help/en/article/barcode-field-1vkxn98/',
  product_url:
    'https://help.stock-sync.com/support/solutions/articles/44002412830-product-url-field',
  handle: 'https://stocksync.crisp.help/en/article/url-handle-field-c0h37f/',
  product_type:
    'https://help.stock-sync.com/en/article/product-type-field-e3hwlq/',
  track_quantity:
    'https://help.stock-sync.com/en/article/track-inventory-field-vtkw2f/',
  backorders:
    'https://help.stock-sync.com/en/article/allow-backorder-field-e0pbmp/',
  width:
    'https://help.stock-sync.com/en/article/length-width-and-height-fields-1cn4qtk/',
  length:
    'https://help.stock-sync.com/en/article/length-width-and-height-fields-1cn4qtk/',
  height:
    'https://help.stock-sync.com/en/article/length-width-and-height-fields-1cn4qtk/',
  stock_status:
    'https://help.stock-sync.com/en/article/stock-status-field-woocommerce-13e918e/',
  sale_price:
    'https://help.stock-sync.com/en/article/sale-price-field-1rwy874/?bust=1749197183232',
};
