import { faCircleQuestion } from '@fortawesome/pro-light-svg-icons';
import { Divider, MenuItem, useTheme } from '@mui/material';
import { useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { match, P } from 'ts-pattern';

import { Button } from '@/components/Button';
import { Icon } from '@/components/Icon';
import { Link } from '@/components/Link';
import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDisplayLanguages } from '@/hooks/useDisplayLanguages';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';
import { FormDivider } from '../FormDivider';
import { CaseConvert } from './CaseConvert';
import { FindField } from './FindField';
import { ReplaceField } from './ReplaceField';

interface DescriptionProps {
  index: number;
}

export function Description({ index }: DescriptionProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: {
      feedConstants: { wrap_tag_options: wrapTagOptions },
    },
    currentStore,
  } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { control, getValues } = useFeedMappingFormContext();
  const { languages } = useDisplayLanguages();
  const [feedType, ioMode] = getValues(['feedType', 'ioMode']);
  const watchDescription = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.force_override_description`,
  });
  const watchSkipIfBlank = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.skip_if_blank`,
  });
  const watchOriginalLanguage = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.original_language`,
  });
  const watchFieldName = useWatch({
    control,
    name: `syncFieldSettings.${index}.field_name`,
  });

  return (
    <>
      <CaseConvert
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.case_convert`}
      />
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <FindField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.find`}
          />
          <FormDivider smallGapBetweenComponent />
          <ReplaceField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.replace`}
          />
        </>
      )}
      {currentStore.provider === 'shopify' &&
        feedType === 'update' &&
        ioMode === 'in' && (
          <>
            <FormDivider smallGapBetweenComponent />
            <Switch
              control={control}
              name={`syncFieldSettings.${index}.extra_attributes.update_only_if_nil`}
              label={t('update_only_if_nil')}
            />
          </>
        )}
      {match({
        humanizeFeedType: currentFeed.humanizeFeedType,
        enableTranslationFeature: currentStore.enableTranslationFeature,
        originalLanguage: watchOriginalLanguage,
      })
        .with(
          {
            humanizeFeedType: P.when((type) => type !== 'export'),
            enableTranslationFeature: true,
            originalLanguage: P.when((l) => l !== 'no change'),
          },
          () => (
            <>
              <FormDivider />
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.original_language`}
                label={t('translate_from')}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>
              <FormDivider />
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.returned_language`}
                label={t('translate_to')}
                disabled={watchOriginalLanguage === 'no change'}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>
            </>
          )
        )
        .otherwise(() => (
          <></>
        ))}
      <Divider sx={{ m: theme.spacing(3, 0, 4, 0) }} />
      {watchFieldName !== 'meta_description' && (
        <>
          <TextField
            select
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.wrap_tag`}
            label={t('wrap_tag')}
          >
            {wrapTagOptions.map((option) => (
              <MenuItem key={option.key} value={option.key}>
                {option.value}
              </MenuItem>
            ))}
          </TextField>
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.convert_line_break`}
            label={t('label_convert_line_break')}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
        </>
      )}
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.labels`}
        label={t('label_body_labels')}
        helperText={t('help_text_body_labels')}
      />
      <Link to="https://help.stock-sync.com/en/article/custom-text-formatting-in-description-vendor-title-sku-fields-1m4fjwl/">
        <Button
          variant="text"
          size="small"
          sx={{ padding: 0 }}
          startIcon={<Icon type="default" icon={faCircleQuestion} />}
        >
          {t('learn_how_to_make_custom_text')}
        </Button>
      </Link>
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.skip_zero_blank_labels`}
            label={t('label_skip_zero_blank_labels')}
          />
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.force_override_description`}
            label={t('label_force_override_description')}
            disabled={watchSkipIfBlank}
          />
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
            label={t('skip_if_empty')}
            disabled={watchDescription}
          />
        </>
      )}
    </>
  );
}
