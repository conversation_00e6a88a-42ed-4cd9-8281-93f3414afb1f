import { Box, useTheme } from '@mui/material';
import * as React from 'react';
import { useWatch, type UseFieldArrayReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { match } from 'ts-pattern';

import { Autocomplete } from '@/components/Autocomplete';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';

import type { FeedMappingFormValues } from '..';
import { useFeedMappingFormContext } from '../useFeedMappingFormContext';
import { initQuantityDerivedAttributes } from '../util';

const DEPRECATED_MAPPINGS = {
  shopify: {
    fulfillment_service: true,
  },
  bigcommerce: {},
  woocommerce: {},
  square: {},
  wix: {},
  ekm: {},
  squarespace: {},
  quickbooks: {},
  prestashop: {},
};

const mappingFieldByPlatform = {
  shopify: {
    remove: [],
    update: [
      'quantity',
      'price',
      'compare_price_at',
      'cost',
      'tags',
      'body_html',
      'product_title',
      'images',
      'video_links',
      'standardized_product_type',
      'weight',
      'sku',
      'barcode',
      'published',
      'policy',
      'vendor',
      'product_type',
      'taxable',
      'fulfillment_service',
      'metafields_global_title_tag',
      'metafields_global_description_tag',
      'country_code_of_origin',
      'harmonized_system_code',
      'requires_shipping',
      'template_suffix',
      'status',
      'track_quantity',
      'quantity_quality_control',
      'quantity_safety_stock',
      'quantity_damaged',
      'quantity_reserved',
      'quantity_incoming',
      'price_region',
      'compare_price_at_region',
      'tax_code',
      'handle',
    ],
    import: [
      'quantity',
      'price',
      'compare_price_at',
      'cost',
      'tags',
      'body_html',
      'product_title',
      'images',
      'standardized_product_type',
      'weight',
      'sku',
      'barcode',
      'published',
      'policy',
      'vendor',
      'product_type',
      'handle',
      'taxable',
      'fulfillment_service',
      'metafields_global_title_tag',
      'metafields_global_description_tag',
      'requires_shipping',
      'template_suffix',
      'status',
      'video_links',
      'country_code_of_origin',
      'harmonized_system_code',
    ],
    export: [
      'quantity',
      'price',
      'compare_price_at',
      'cost',
      'tags',
      'body_html',
      'product_title',
      'images',
      'standardized_product_type',
      'weight',
      'sku',
      'barcode',
      'published',
      'policy',
      'vendor',
      'product_type',
      'handle',
      'product_url',
      'taxable',
      'fulfillment_service',
      'country_code_of_origin',
      'harmonized_system_code',
      'requires_shipping',
      'template_suffix',
      'status',
      'track_quantity',
      'shopify_product_id',
      'shopify_variant_id',
      'product_created_at',
      'variant_created_at',
    ],
  },
  wix: {
    remove: [],
    update: [
      'quantity',
      'price',
      'cost',
      'body_html',
      'product_title',
      'images',
      'weight',
      'ribbon',
      'discount_amount',
      'exported_at',
      'visible',
      'brand',
      'collection',
    ],
    import: [
      'quantity',
      'price',
      'cost',
      'body_html',
      'product_title',
      'images',
      'weight',
      'ribbon',
      'discount_amount',
      'brand',
      'category',
      'collection',
    ],
    export: [
      'quantity',
      'price',
      'cost',
      'body_html',
      'product_title',
      'images',
      'weight',
      'ribbon',
      'discount_amount',
      'exported_at',
      'visible',
      'brand',
      'collection',
    ],
  },
  woocommerce: {
    remove: [],
    update: [
      'quantity',
      'price',
      'body_html',
      'product_title',
      'images',
      'barcode',
      'weight',
      'tags',
      'sale_price',
      'categories',
      'width',
      'height',
      'stock_status',
      'length',
      'backorders',
      'short_description',
      'brand',
    ],
    import: [
      'quantity',
      'price',
      'body_html',
      'product_title',
      'images',
      'barcode',
      'weight',
      'tags',
      'sale_price',
      'categories',
      'width',
      'height',
      'length',
      'backorders',
      'short_description',
      'brand',
    ],
    export: [
      'quantity',
      'price',
      'body_html',
      'product_title',
      'images',
      'barcode',
      'weight',
      'tags',
      'sale_price',
      'categories',
      'width',
      'height',
      'length',
      'stock_status',
      'backorders',
      'short_description',
      'brand',
    ],
  },
  bigcommerce: {
    remove: [],
    update: [
      'quantity',
      'price',
      'cost',
      'sale_price',
      'retail_price',
      'body_html',
      'product_title',
      'images',
      'product_sku',
      'barcode',
      'weight',
      'availability_description',
      'width',
      'height',
      'depth',
      'shipping_cost',
      'free_shipping',
      'preorder_message',
      'preorder_release_date',
      'brand',
      'mpn',
      'bin_picking_number',
      'category',
      'visible',
      'purchase_availability',
      'condition',
      'show_condition',
      'track_quantity',
      'gtin',
      'search_keywords',
      'page_title',
      'warranty',
      'meta_description',
    ],
    import: [
      'quantity',
      'price',
      'cost',
      'sale_price',
      'retail_price',
      'body_html',
      'product_title',
      'images',
      'product_sku',
      'barcode',
      'weight',
      'availability_description',
      'width',
      'height',
      'depth',
      'shipping_cost',
      'free_shipping',
      'preorder_message',
      'preorder_release_date',
      'brand',
      'category',
      'mpn',
      'bin_picking_number',
      'visible',
      'condition',
      'show_condition',
      'gtin',
      'search_keywords',
      'page_title',
      'warranty',
      'meta_description',
    ],
    export: [
      'quantity',
      'cost',
      'price',
      'sale_price',
      'retail_price',
      'body_html',
      'product_title',
      'images',
      'product_sku',
      'barcode',
      'weight',
      'availability_description',
      'depth',
      'shipping_cost',
      'free_shipping',
      'preorder_release_date',
      'mpn',
      'bin_picking_number',
      'category',
      'preorder_message',
      'brand',
      'width',
      'height',
      'visible',
      'condition',
      'show_condition',
      'gtin',
      'search_keywords',
      'page_title',
      'warranty',
      'meta_description',
    ],
  },
  squarespace: {
    remove: [],
    update: ['quantity', 'price', 'body_html', 'product_title', 'weight'],
    import: [],
    export: [],
  },
  quickbooks: {
    remove: [],
    update: ['quantity', 'price', 'body_html', 'product_title', 'weight'],
    import: [],
    export: [],
  },
  ekm: {
    remove: [],
    update: ['quantity', 'price', 'body_html', 'product_title', 'weight'],
    import: ['quantity', 'price', 'product_title', 'weight'],
    export: [],
  },
  square: {
    remove: [],
    update: ['quantity', 'price'],
    import: ['quantity', 'price', 'body_html', 'product_title'],
    export: [],
  },
  prestashop: {
    remove: [],
    update: ['quantity', 'price', 'body_html', 'product_title'],
    import: ['quantity', 'price', 'body_html', 'product_title'],
    export: [],
  },
};

const fieldsToHideBySourceType = {
  shopify_store: [
    'metafields_global_title_tag',
    'metafields_global_description_tag',
  ],
  google_spreadsheet_out: [
    'country_code_of_origin',
    'metafields_global_title_tag',
    'metafields_global_description_tag',
  ],
  email_out: [
    'shopify_product_id',
    'shopify_variant_id',
    'product_created_at',
    'variant_created_at',
  ],
  ftp_out: [
    'shopify_product_id',
    'shopify_variant_id',
    'product_created_at',
    'variant_created_at',
  ],
  zoho_sheet: [
    'shopify_product_id',
    'shopify_variant_id',
    'product_created_at',
    'variant_created_at',
  ],
  etsy_out: [
    'shopify_product_id',
    'shopify_variant_id',
    'product_created_at',
    'variant_created_at',
  ],
  sftp_out: [
    'shopify_product_id',
    'shopify_variant_id',
    'product_created_at',
    'variant_created_at',
  ],
  ebay_out: [
    'shopify_product_id',
    'shopify_variant_id',
    'product_created_at',
    'variant_created_at',
  ],
  google_shopping_out: [
    'shopify_product_id',
    'shopify_variant_id',
    'product_created_at',
    'variant_created_at',
  ],
};

const webHookModeFieldsToHide = ['fulfillment_service'];

const nonWebHookModeFieldsToHide = [
  'standardized_product_type',
  'price_region',
  'compare_price_at_region',
];

interface NonProductIdentifierProps {
  mapping: string;
  index: number;
  mappingEditable: boolean;
  fieldArrayProps: UseFieldArrayReturn<
    FeedMappingFormValues,
    'syncFieldSettings'
  >;
}

export function NonProductIdentifier({
  index,
  mapping,
  mappingEditable,
  fieldArrayProps,
}: NonProductIdentifierProps) {
  const {
    currentStore: { syncFieldConstants, provider },
  } = useCurrentStore();
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentFeed } = useCurrentFeed();
  const { control, setValue, getValues, trigger } = useFeedMappingFormContext();
  const productIdentifier = useWatch({ control, name: 'shopifyProductKey' });
  const syncFieldSettings = useWatch({
    control,
    name: 'syncFieldSettings',
  });
  const deprecatedMappings = DEPRECATED_MAPPINGS[provider];

  const quantityField = React.useMemo(
    () => syncFieldSettings.find((a) => a.field_name === 'quantity'),
    [syncFieldSettings]
  );

  const quantityUseOnHand =
    quantityField?.extra_attributes.quantity_use_on_hand;

  const options = React.useMemo(() => {
    const currentField = syncFieldSettings?.[index]?.field_name;
    const existingFields = (syncFieldSettings || []).map((o) => o.field_name);
    const fields =
      mappingFieldByPlatform[provider][currentFeed.humanizeFeedType];
    const fieldsToHide =
      fieldsToHideBySourceType[currentFeed.combinedSourceType];
    return fields
      .filter(
        (field) =>
          !existingFields
            // current field should be included in options
            .filter((existingField) => existingField !== currentField)
            .includes(field)
      )
      .filter((field) => !fieldsToHide?.includes(field))
      .filter((field) => !Object.keys(deprecatedMappings).includes(field))
      .filter((field) => field !== productIdentifier)
      .filter((field) =>
        currentFeed.runWebhookMode
          ? !webHookModeFieldsToHide.includes(field)
          : !nonWebHookModeFieldsToHide.includes(field)
      );
  }, [
    provider,
    currentFeed,
    productIdentifier,
    deprecatedMappings,
    syncFieldSettings,
    index,
  ]);

  const isDeprecatedField = Boolean(deprecatedMappings[mapping]);
  const lockedField = useWatch({
    control,
    name: `syncFieldSettings.${index}.is_locked`,
  });

  return (
    <Autocomplete
      name={`syncFieldSettings.${index}.field_name`}
      control={control}
      readOnly={!mappingEditable}
      openOnFocus
      allowBlank={false}
      options={options}
      getOptionLabel={(option) =>
        match({
          provider: provider,
          name: option,
        })
          .with({ provider: 'bigcommerce', name: 'quantity' }, () =>
            t('bigcommerce_quantity')
          )
          .with({ provider: 'woocommerce', name: 'quantity' }, () =>
            t('stock_quantity')
          )
          .with(
            {
              provider: 'shopify',
              name: 'quantity',
            },
            () => t('product_quantity')
          )

          .otherwise(() => t(option as string))
      }
      variant="standard"
      label={
        isDeprecatedField ? (
          <Box sx={{ color: theme.palette.danger.main }}>
            {t('field_deprecated')}
          </Box>
        ) : (
          t('store_field')
        )
      }
      getOptionDisabled={(option) =>
        (quantityUseOnHand === true || quantityUseOnHand === 'true') &&
        option.includes('quantity_')
          ? true
          : false
      }
      helperText={
        isDeprecatedField && (
          <Box component="span" sx={{ color: theme.palette.danger.main }}>
            {t('help_text_deprecated')}
          </Box>
        )
      }
      onChange={(_, fieldName) => {
        const defaultAttributes = syncFieldConstants[fieldName as string];
        const extraAttributes = {
          ...defaultAttributes,
          ...(fieldName === 'quantity'
            ? initQuantityDerivedAttributes({
                lowStockLevel: getValues('lowStockLevel'),
                location: { id: getValues('locationId') },
                quantityOption: {
                  only_deduct_quantity: defaultAttributes.only_deduct_quantity,
                  add_to_init_quantity: defaultAttributes.add_to_init_quantity,
                },
              })
            : {}),
        };

        // using parent field array update auto rerenders for us
        // https://react-hook-form.com/docs/usefieldarray
        fieldArrayProps.update(index, {
          ...fieldArrayProps.fields[index],
          ...syncFieldSettings?.[index],
          ...(fieldName ? { field_name: fieldName } : {}),
          extra_attributes: extraAttributes,
        });

        trigger(undefined, { shouldFocus: false }); // trigger rhf validation

        // these related props outside extra_attributes, reset too
        if (fieldName === 'images') {
          setValue('assignVariantsToFirstImage', false);
          setValue('variantImageLink', true);
        }

        if (
          fieldName === 'compare_price_at_region' ||
          fieldName === 'price_region'
        ) {
          setValue(`syncFieldSettings.${index}.field_mapping`, '1');
        }
        if (
          fieldName !== 'quantity' &&
          fieldName !== 'product_title' &&
          fieldName !== 'price'
        ) {
          trigger(`syncFieldSettings.${index}.field_name`, {
            shouldFocus: false,
          });

          trigger(`syncFieldSettings.${index}.field_mapping`, {
            shouldFocus: true,
          });
        }
      }}
      disabled={lockedField}
      sx={{ maxWidth: '400px' }}
    />
  );
}
