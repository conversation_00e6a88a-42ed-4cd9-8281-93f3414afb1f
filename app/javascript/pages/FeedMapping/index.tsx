import {
  faArrowUpRightAndArrowDownLeftFromCenter,
  faCircleQuestion,
  faPlus,
  faXmark,
} from '@fortawesome/pro-light-svg-icons';
import { faStars } from '@fortawesome/pro-solid-svg-icons';
import { yupResolver } from '@hookform/resolvers/yup';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Box,
  MenuItem,
  Stack,
  Typography,
  useTheme,
  type StackProps,
} from '@mui/material';
import debounce from 'lodash/debounce';
import merge from 'lodash/merge';
import omit from 'lodash/omit';
import * as React from 'react';
import {
  FormProvider,
  useFieldArray,
  useForm,
  useFormContext,
  type Resolver,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { match, P } from 'ts-pattern';
import { useQuery } from 'urql';

import { Alert } from '@/components/Alert';
import { Autocomplete } from '@/components/Autocomplete';
import { Button } from '@/components/Button';
import { Dialog } from '@/components/Dialog';
import { ErrorHelperText } from '@/components/ErrorHelperText';
import { FeedEditAppBar, type OnSubmit } from '@/components/FeedEditAppBar';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { Paper } from '@/components/Paper';
import { PopoverConfirm } from '@/components/PopoverConfirm';
import { QuickEditDialog } from '@/components/QuickEditDialog';
import { Switch } from '@/components/Switch';
import { Tags } from '@/components/Tags';
import { TextField } from '@/components/TextField';
import { Tooltip } from '@/components/Tooltip';
import { useAlert } from '@/hooks/useAlert';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useFieldMappingPrediction } from '@/hooks/useFieldMappingPrediction';
import {
  useFormSalesChannels,
  type FormSalesChannels,
} from '@/hooks/useFormSalesChannels';
import { useIntroJs } from '@/hooks/useIntroJs';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { useOnboardingStep } from '@/hooks/useOnboardingStep';
import { useSampleData } from '@/hooks/useSampleData';
import { ResetToTemplate, UpdateFeedSettings } from '@/queries/FeedSettings';
import { GetMatchedMetafields } from '@/queries/Users';
import * as routes from '@/routes';
import { productVariation } from '@/shared/images';
import {
  commaNotInsideQuotes,
  compareRegexAsString,
  truncate,
} from '@/shared/util';
import type {
  DefaultKeyValue,
  Metafield,
  ProductOptionsSyncField,
  Store,
  SyncFieldSetting,
  UserProfile,
} from '@/types';

import { ExtraAttribute } from './components/ExtraAttribute';
import { ExtraAttributeStaticFlag } from './components/ExtraAttributeStaticFlag';
import { MappingSection } from './components/MappingSection';
import { NonProductIdentifier } from './components/NonProductIdentifier';
import { NonProductIdentifierForOption } from './components/NonProductIdentifierForOption';
import { ShopifyProductKey } from './components/ShopifyProductKey';
import { formSchema } from './schema';
import { useFeedMappingFormContext } from './useFeedMappingFormContext';
import { useMetafieldDefinitions } from './useMetafieldDefinitions';
import { initQuantityDerivedAttributes } from './util';
import { getMappingSchema } from './validation';

interface FormMetafield extends Metafield {
  /**
   * `useFieldArray` auto generates own `id` and overwrites `UserProfile.id`, preserve our id as `_id`
   */
  _id: Metafield['id'];
}

type SyncFieldSettingExtraAttributes = SyncFieldSetting['extra_attributes'];
interface FormSyncFieldSettingExtraAttributes
  extends SyncFieldSettingExtraAttributes {
  _variantImageFallback?: string; // only appear when is image field
}

interface FormSyncFieldSetting extends Omit<SyncFieldSetting, 'id'> {
  id?: SyncFieldSetting['id'];
  /**
   * `useFieldArray` auto generates own `id` and overwrites `UserProfile.id`, preserve our id as `_id`
   */
  _id?: SyncFieldSetting['id'];
  extra_attributes: FormSyncFieldSettingExtraAttributes;
}

type ProductOptionsSyncFieldExtraAttributes =
  ProductOptionsSyncField['extra_attributes'];
interface FormProductOptionsSyncFieldExtraAttributes
  extends ProductOptionsSyncFieldExtraAttributes {
  _hasExistingProductIdentifier: boolean;
}

interface FormProductOptionsSyncField
  extends Omit<ProductOptionsSyncField, 'id' | 'extra_attributes'> {
  id?: ProductOptionsSyncField['id'];
  /**
   * `useFieldArray` auto generates own `id` and overwrites `UserProfile.id`, preserve our id as `_id`
   */
  _id?: ProductOptionsSyncField['id'];
  extra_attributes: FormProductOptionsSyncFieldExtraAttributes;
}

// TODO: z.infer
export interface FeedMappingFormValues
  extends Pick<
      UserProfile,
      | 'assignVariantsToFirstImage'
      | 'caseSensitive'
      | 'extraOptions'
      | 'feedType'
      | 'id'
      | 'importSort'
      | 'ioMode'
      | 'isAutoGenerateMetafieldDefinitions'
      | 'locationId'
      | 'locationName'
      | 'lowStockLevel'
      | 'postfix'
      | 'prefix'
      | 'productIdentifierSyncField'
      | 'productKeySeparator'
      | 'profileName'
      | 'runWebhookMode'
      | 'salesChannelIds'
      | 'shopifyProductKey'
      | 'shopifyTrackInventory'
      | 'skipImportWithZeroQty'
      | 'storePrefix'
      | 'updateDuplicateProductKey'
      | 'variantImageLink'
    >,
    FormSalesChannels {
  _shopifyProductKey: DefaultKeyValue;
  metafields: Array<FormMetafield>;
  productOptionsSyncField: Array<FormProductOptionsSyncField>;
  syncFieldSettings: Array<FormSyncFieldSetting>;
  woocommerceProductAttributes: Array<FormMetafield>;
}

export function FeedMapping() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const matchesSm = useMatches('sm');
  const { currentFeed } = useCurrentFeed();
  const step = useOnboardingStep();
  const {
    currentStore,
    currentStore: {
      feedConstants: {
        tags_col_sep_options: tagsColSepOptions,
        shopify_product_key: shopifyProductKeys,
        import_product_identifiers: importProductIdentifiers,
        export_shopify_product_key: exportShopifyProductKeys,
      },
    },
  } = useCurrentStore();
  const { data: metafieldDefinitions } = useMetafieldDefinitions();

  const schema = React.useMemo(
    () =>
      getMappingSchema({
        currentStore,
        currentFeed,
        t,
        metafieldDefinitions,
        shopifyProductKeys,
        importProductIdentifiers,
        exportShopifyProductKeys,
      }),
    [
      currentStore,
      currentFeed,
      t,
      metafieldDefinitions,
      shopifyProductKeys,
      importProductIdentifiers,
      exportShopifyProductKeys,
    ]
  );
  const params = useParams();
  const formSalesChannels = useFormSalesChannels();
  const [errorMessage, setErrorMessage] = React.useState<JSX.Element[]>([]);

  const [, updateFeedSettings] = useMutation(UpdateFeedSettings, {
    dataKey: 'updateFeedSettings',
  });

  const maxDropdownItemDisplay = 50;
  const mandatoryAttributes =
    mandatoryImportMappings[currentStore.provider](currentFeed);

  const newMapping = {
    field_mapping: undefined,
    field_name: '',
    user_profile_id: Number(params.id),
    extra_attributes: {},
  };

  const defaultSyncFieldSettings = React.useMemo(() => {
    if (currentFeed.syncFieldSettings?.length === 0) {
      const settings =
        currentFeed.feedType === 'import' ? mandatoryAttributes : [];
      return settings;
    } else {
      const settings = (currentFeed.syncFieldSettings || []).filter(
        (item) =>
          !item.field_name.includes('metafield_') &&
          !item.field_name.includes('custom_field') &&
          !item.field_name.includes('metadata') &&
          !item.field_name.includes('product_attribute') &&
          !item.field_name.includes('additional_info_section') &&
          !filterFromSyncFieldSettings.includes(item.field_name)
      );
      return settings;
    }
  }, [currentFeed, mandatoryAttributes]);

  const showDeleteFieldMapping =
    [
      'shopify',
      'bigcommerce',
      'wix',
      'woocommerce',
      'ekm',
      'prestashop',
      'square',
    ].includes(currentStore.provider) &&
    mandatoryImportMappings[currentStore.provider](currentFeed).map((a) => {
      return a.field_name;
    });

  const formProps = useForm<FeedMappingFormValues>({
    // TODO: WIP yup to zod
    // merge { values, errors} from both yup + zod
    resolver: React.useCallback<Resolver<FeedMappingFormValues>>(
      async (data, context, options) => {
        const yupValuesErrors = omit(
          await yupResolver(schema)(data, context, options),
          // TODO: WIP. omit from yup after moved to zod
          ['values.productIdentifierSyncField']
        );
        const zodValuesErrors = await zodResolver(formSchema(currentFeed))(
          data,
          context,
          options
        );
        console.log(`resolver yup:`, yupValuesErrors);
        console.log(`resolver zod:`, zodValuesErrors);
        const mergedValuesErrors = merge({}, yupValuesErrors, zodValuesErrors);
        console.log(`resolver merged:`, mergedValuesErrors);
        return mergedValuesErrors;
      },
      [schema, currentFeed]
    ),
    values: {
      assignVariantsToFirstImage: currentFeed.assignVariantsToFirstImage,
      caseSensitive: currentFeed.caseSensitive,
      extraOptions: currentFeed.extraOptions,
      feedType: currentFeed.feedType,
      id: currentFeed.id,
      profileName: currentFeed.profileName,
      importSort: currentFeed.importSort,
      ioMode: currentFeed.ioMode,
      isAutoGenerateMetafieldDefinitions:
        currentFeed.isAutoGenerateMetafieldDefinitions,
      locationId: currentFeed.locationId,
      locationName: !currentFeed.locationName
        ? t('primary_location')
        : currentFeed.locationName,
      lowStockLevel: currentFeed.lowStockLevel,
      metafields: React.useMemo(
        () =>
          (currentFeed.metafields ?? []).map((mf) => ({
            ...mf,
            _id: mf.id, // useFieldArray generates and overwrites id, preserve our id as _id
            _namespaceAndKey /* _derived */: `${mf.extra_attributes.metafield_namespace}.${mf.extra_attributes.metafield_key}`,
            _fieldName /* _derived */:
              currentStore.provider === 'bigcommerce'
                ? `${t('custom_field')} ${mf.field_name.substring(13)}`
                : currentStore.provider === 'woocommerce'
                  ? `${t('metadata')} ${mf.field_name.substring(9)}`
                  : currentStore.provider === 'wix'
                    ? `${t('additional_info_section')} ${mf.field_name.substring(24)}`
                    : undefined,
            extra_attributes: {
              ...mf.extra_attributes,
              ...(currentStore.provider === 'shopify'
                ? {
                    metafield_type:
                      metafieldDefinitions?.find(
                        (o) =>
                          o.key === mf.extra_attributes.metafield_key &&
                          o.namespace ===
                            mf.extra_attributes.metafield_namespace &&
                          o.ownerType === mf.extra_attributes.metafield_owner
                      )?.type.name ?? mf.extra_attributes.metafield_type,
                    metafield_owner:
                      metafieldDefinitions?.find(
                        (o) =>
                          o.key === mf.extra_attributes.metafield_key &&
                          o.namespace ===
                            mf.extra_attributes.metafield_namespace &&
                          o.ownerType === mf.extra_attributes.metafield_owner
                      )?.ownerType ?? mf.extra_attributes.metafield_owner,
                  }
                : {}),
            },
          })),
        [t, currentStore, currentFeed, metafieldDefinitions]
      ),
      postfix: currentFeed.postfix,
      prefix: currentFeed.prefix,
      productIdentifierSyncField: currentFeed.productIdentifierSyncField,
      productKeySeparator: currentFeed.productKeySeparator,
      productOptionsSyncField: React.useMemo(
        () =>
          (currentFeed.productOptionsSyncField ?? []).map((posf) => ({
            ...posf,
            extra_attributes: {
              ...posf.extra_attributes,
              _hasExistingProductIdentifier: posf.extra_attributes
                .existing_product_identifier
                ? true
                : false,
              existing_product_identifier:
                posf.extra_attributes.existing_product_identifier ?? '',
            },
            _id: posf.id, // useFieldArray generates and overwrites id, preserve our id as _id
          })),
        [currentFeed]
      ),
      shopifyProductKey: currentFeed.shopifyProductKey,
      _shopifyProductKey /* _derived as option */: {
        key: currentFeed.shopifyProductKey ?? '',
        value: currentFeed.shopifyProductKey ?? '',
      },
      shopifyTrackInventory: currentFeed.shopifyTrackInventory,
      skipImportWithZeroQty: currentFeed.skipImportWithZeroQty,
      storePrefix: currentFeed.storePrefix,
      syncFieldSettings: React.useMemo(
        () =>
          Array.isArray(defaultSyncFieldSettings)
            ? defaultSyncFieldSettings.map((sfs) => ({
                ...sfs,
                is_locked: sfs.is_locked ?? false,
                _id: sfs.id,
                extra_attributes: {
                  ...sfs.extra_attributes,
                  ...(sfs.field_name === 'tags'
                    ? {
                        _col_sep /* _derived */:
                          tagsColSepOptions.find(
                            ({ key }) => key === sfs.extra_attributes.col_sep
                          )?.value ?? sfs.extra_attributes.col_sep,
                      }
                    : {}),
                  ...(sfs.field_name === 'quantity'
                    ? initQuantityDerivedAttributes({
                        lowStockLevel: currentFeed.lowStockLevel,
                        location: {
                          id: currentFeed.locationId,
                        },
                        quantityOption: {
                          only_deduct_quantity:
                            sfs.extra_attributes.only_deduct_quantity,
                          add_to_init_quantity:
                            sfs.extra_attributes.add_to_init_quantity,
                        },
                      })
                    : {}),
                  ...(sfs.field_name === 'images'
                    ? {
                        url_unescape:
                          typeof sfs.extra_attributes.url_unescape !== 'boolean'
                            ? false
                            : sfs.extra_attributes.url_unescape,
                        force_override:
                          typeof sfs.extra_attributes.force_override !==
                          'boolean'
                            ? false
                            : sfs.extra_attributes.force_override,
                        _variantImageFallback: sfs.extra_attributes
                          .variant_image_fallback
                          ? sfs.extra_attributes.variant_image_fallback
                          : 'all',
                      }
                    : {}),
                  ...(['product_title', 'body_html'].includes(sfs.field_name)
                    ? {
                        original_language: sfs.extra_attributes
                          .original_language
                          ? sfs.extra_attributes.original_language.length === 2 // to reset existing profile value with previous translation options eg: "zh" to no change
                            ? 'no change'
                            : sfs.extra_attributes.original_language
                          : 'no change',
                        returned_language: sfs.extra_attributes
                          .returned_language
                          ? sfs.extra_attributes.returned_language.length === 2
                            ? 'no change'
                            : sfs.extra_attributes.returned_language
                          : 'no change',
                      }
                    : {}),
                  ...(sfs.field_name === 'category' &&
                  currentStore.provider === 'wix'
                    ? {
                        original_language: sfs.extra_attributes
                          .original_language
                          ? sfs.extra_attributes.original_language.length === 2
                            ? 'no change'
                            : sfs.extra_attributes.original_language
                          : 'no change',
                        returned_language: sfs.extra_attributes
                          .returned_language
                          ? sfs.extra_attributes.returned_language.length === 2
                            ? 'no change'
                            : sfs.extra_attributes.returned_language
                          : 'no change',
                      }
                    : {}),
                  ...(sfs.field_name === 'short_description' &&
                  currentStore.provider === 'woocommerce'
                    ? {
                        original_language: sfs.extra_attributes
                          .original_language
                          ? sfs.extra_attributes.original_language.length === 2
                            ? 'no change'
                            : sfs.extra_attributes.original_language
                          : 'no change',
                        returned_language: sfs.extra_attributes
                          .returned_language
                          ? sfs.extra_attributes.returned_language.length === 2
                            ? 'no change'
                            : sfs.extra_attributes.returned_language
                          : 'no change',
                      }
                    : {}),
                  ...([
                    'price',
                    'compare_price_at',
                    'cost',
                    'sale_price',
                    'retail_price',
                  ].includes(sfs.field_name)
                    ? {
                        currency_converter: sfs.extra_attributes
                          .currency_converter
                          ? sfs.extra_attributes.currency_converter
                          : false,
                        default_currency: sfs.extra_attributes.default_currency
                          ? sfs.extra_attributes.default_currency
                          : 'USD',
                        new_currency: sfs.extra_attributes.new_currency
                          ? sfs.extra_attributes.new_currency
                          : 'USD',
                      }
                    : {}),
                  static_flag:
                    typeof sfs.extra_attributes.static_flag !== 'boolean'
                      ? false
                      : sfs.extra_attributes.static_flag,
                },
              }))
            : [],
        [currentStore, currentFeed, defaultSyncFieldSettings, tagsColSepOptions]
      ),
      updateDuplicateProductKey: currentFeed.updateDuplicateProductKey,
      woocommerceProductAttributes: React.useMemo(
        () =>
          (currentFeed.woocommerceProductAttributes ?? []).map((wpa) => ({
            ...wpa,
            _id: wpa.id, // useFieldArray generates and overwrites id, preserve our id as _id
            _fieldName /* _derived */: `${t('product_attribute')} ${wpa.field_name.substring(18)}`,
          })),
        [currentFeed, t]
      ),
      variantImageLink: currentFeed.variantImageLink,
      runWebhookMode: currentFeed.runWebhookMode,
      salesChannelIds: currentFeed.salesChannelIds,
      ...formSalesChannels,
    },
    mode: 'onSubmit', // before form submit
    reValidateMode: 'onSubmit', // after form submit
    shouldFocusError: false, // if true, will break form in drawer, can't focus on error
  });

  // https://react-hook-form.com/docs/useform/formstate
  // ❌ formProps.formState.isDirty is accessed conditionally, does not subscribe to changes of that state
  // ✅ read formState values to subscribe to changes:
  const { isDirty } = formProps.formState;

  const submitHandler: OnSubmit<FeedMappingFormValues> =
    ({ onSuccess, onError, onSkip }) =>
    async (values) => {
      console.log(`submit values:`, values);
      if (isDirty === false) {
        return onSkip({ data: values });
      }
      return updateFeedSettings(
        {
          feedId: +values.id,
          extraOptions: values.extraOptions,
          shopifyProductKey: values.shopifyProductKey,
          storePrefix: values.storePrefix,
          prefix: values.prefix,
          postfix: values.postfix,
          productKeySeparator: values.productKeySeparator,
          caseSensitive: values.caseSensitive,
          updateDuplicateProductKey: values.updateDuplicateProductKey,
          assignVariantsToFirstImage: values.assignVariantsToFirstImage,
          isAutoGenerateMetafieldDefinitions:
            values.isAutoGenerateMetafieldDefinitions,
          variantImageLink: values.variantImageLink,
          shopifyTrackInventory: values.shopifyTrackInventory,
          locationId: values.locationId,
          locationName: values.locationName,
          lowStockLevel: values.lowStockLevel,
          skipImportWithZeroQty: values.skipImportWithZeroQty,
          productIdentifierSyncField: values.productIdentifierSyncField,
          syncFieldSettings: values.syncFieldSettings.map((sfs) => ({
            // useFieldArray generates own id overwriting our id
            // preserve our id as _id, and restore before API call
            ...omit(sfs, '_id'),
            id: sfs._id ?? undefined, // undefined = new item
            extra_attributes: {
              ...sfs.extra_attributes,
              ...(sfs.field_name === 'tags'
                ? {
                    _col_sep: undefined, // remove _derived
                  }
                : {}),
              ...(sfs.field_name === 'quantity'
                ? {
                    _quantity_option: undefined, // remove _derived
                    _low_stock_level: undefined, // remove _derived
                  }
                : {}),
              ...(sfs.field_name === 'images'
                ? {
                    _variantImageFallback: undefined, // remove _derived
                  }
                : {}),
              ...(sfs.field_name === 'weight'
                ? {
                    weight_unit:
                      sfs.extra_attributes.weight_unit === ' '
                        ? 'g'
                        : sfs.extra_attributes.weight_unit,
                  }
                : {}),
            },
          })),
          productOptionsSyncField: values.productOptionsSyncField.map(
            (posf) => ({
              // useFieldArray generates own id overwriting our id
              // preserve our id as _id, and restore before API call
              ...omit(posf, '_id'),
              id: posf._id ?? undefined, // undefined = new item
              extra_attributes: {
                ...omit(posf.extra_attributes, '_hasExistingProductIdentifier'),
              },
            })
          ),
          metafields: values.metafields?.map((mf) => ({
            ...omit(mf, [
              '_id',
              '_namespaceAndKey',
              '_fieldName',
              '_fieldNameWC',
              'key',
              'name',
              'ownerType',
              'type',
              'namespace',
            ]),
            id: mf._id ?? undefined, // undefined = new item
          })),
          importSort: values.importSort,
          woocommerceProductAttributes: values.woocommerceProductAttributes.map(
            (wpa) => ({
              // useFieldArray generates own id overwriting our id
              // preserve our id as _id, and restore before API call
              ...omit(wpa, ['_id', '_fieldName']),
              id: wpa._id ?? undefined, // undefined = new item
            })
          ),
          salesChannelIds: values.salesChannelIds,
        },
        { onSuccess, onError }
      );
    };

  // on load trigger validation in background to avoid bottleneck on submit
  React.useEffect(() => {
    formProps.trigger();
  }, [formProps]);

  React.useEffect(() => {
    setTimeout(() => {
      setErrorMessage([]);
    }, 10000);
  });
  console.log('formProps.formState.errors', formProps.formState.errors);

  const onSubmit = formProps.handleSubmit(
    submitHandler({
      onSuccess({ data: feed, navigate, enqueueSnackbar }) {
        currentStore.isShowQuickGuide && step.setCurrentStep(4);
        enqueueSnackbar(`${feed.profileName} settings updated`, {
          variant: 'success',
        });
        navigate(routes.filterRoute(feed.id));
      },
      onError({ errors }) {
        if (errors?.length > 0) {
          const errorElements = errors.map((error, index) => (
            <p key={index}>{error}</p>
          ));
          setErrorMessage(errorElements);
        }
      },
      onSkip({ data: feed }) {
        navigate(routes.filterRoute(feed.id));
      },
    }),
    function onInvalid() {
      setErrorMessage([<span key="1">Check field errors below</span>]);
    }
  );
  return (
    <FormProvider {...formProps}>
      <form onSubmit={onSubmit}>
        <FeedEditAppBar
          title={t('feed_mapping_title')}
          redirectUrl={routes.feedDetailRoute(currentFeed.id)}
          backButton={
            <FeedEditAppBar.BackButton
              loading={formProps.formState.isSubmitting}
              onClick={formProps.handleSubmit(
                submitHandler({
                  onSuccess({ data: feed, navigate, enqueueSnackbar }) {
                    enqueueSnackbar(`${feed.profileName} settings updated`, {
                      variant: 'success',
                    });
                    navigate(routes.feedManagerRoute(currentFeed.id));
                  },
                  onError({ errors }) {
                    if (errors?.length > 0) {
                      const errorElements = errors.map((error, index) => (
                        <p key={index}>{error}</p>
                      ));
                      setErrorMessage(errorElements);
                    }
                  },
                  onSkip() {
                    navigate(routes.feedManagerRoute(currentFeed.id));
                  },
                }),
                function onInvalid() {
                  setErrorMessage([
                    <span key="1">Check field errors below</span>,
                  ]);
                }
              )}
            >
              {t('back')}
            </FeedEditAppBar.BackButton>
          }
          nextButton={
            <FeedEditAppBar.NextButton
              type="submit"
              loading={formProps.formState.isSubmitting}
            >
              {t('next')}
            </FeedEditAppBar.NextButton>
          }
        />
        <Box
          sx={{
            margin: matchesSm ? '16px 0px' : '16px 0 16px 0px',
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: 500 }}>
            {t(
              {
                update: 'select_field_to_update',
                import: 'select_field_to_add',
                export: 'select_field_to_export',
                remove: 'match_field_to_identify_product',
              }[currentFeed.humanizeFeedType]
            )}
            &nbsp;&nbsp;
            <ResetButtonAndDialog />
          </Typography>
        </Box>
        {errorMessage.length > 0 && <Alert>{errorMessage}</Alert>}

        <ProductIdentifier
          maxDropdownItemDisplay={maxDropdownItemDisplay}
          onSubmit={submitHandler}
        />

        {currentFeed.feedType !== 'remove' && (
          <StoreProductFields
            showDeleteFieldMapping={showDeleteFieldMapping}
            newMapping={newMapping}
            maxDropdownItemDisplay={maxDropdownItemDisplay}
            onSubmit={submitHandler}
          />
        )}

        {productOptionsVariationsDisplayBasePlatform[
          currentStore.provider
        ].includes(currentFeed.humanizeFeedType) && (
          <ProductOptionsVariations
            showDeleteFieldMapping={showDeleteFieldMapping}
            newMapping={newMapping}
            maxDropdownItemDisplay={maxDropdownItemDisplay}
            onSubmit={submitHandler}
          />
        )}

        {currentFeed.feedType !== 'remove' &&
          currentStore.provider === 'shopify' && (
            <Metafields
              maxDropdownItemDisplay={maxDropdownItemDisplay}
              onSubmit={submitHandler}
            />
          )}

        {['update', 'import'].includes(currentFeed.feedType) &&
          currentStore.provider === 'bigcommerce' && (
            <CustomField
              maxDropdownItemDisplay={maxDropdownItemDisplay}
              onSubmit={submitHandler}
            />
          )}
        {['update', 'import'].includes(currentFeed.feedType) &&
          currentStore.provider === 'woocommerce' && (
            <>
              <ProductAttribute
                maxDropdownItemDisplay={maxDropdownItemDisplay}
                onSubmit={submitHandler}
              />
              <Metadata
                maxDropdownItemDisplay={maxDropdownItemDisplay}
                onSubmit={submitHandler}
              />
            </>
          )}

        {['update'].includes(currentFeed.feedType) &&
          currentStore.provider === 'wix' && (
            <AdditionalInfoSection
              maxDropdownItemDisplay={maxDropdownItemDisplay}
              onSubmit={submitHandler}
            />
          )}

        <Stack
          direction="row"
          spacing="8px"
          sx={{
            alignSelf: 'center',
            justifyContent: 'flex-end',
            marginTop: '16px',
            paddingBottom: '60px',
          }}
        >
          <FeedEditAppBar.BackButton
            loading={formProps.formState.isSubmitting}
            onClick={formProps.handleSubmit(
              submitHandler({
                onSuccess({ data: feed, navigate, enqueueSnackbar }) {
                  enqueueSnackbar(`${feed.profileName} settings updated`, {
                    variant: 'success',
                  });
                  navigate(routes.feedManagerRoute(currentFeed.id));
                },
                onError({ errors }) {
                  if (errors?.length > 0) {
                    const errorElements = errors.map((error, index) => (
                      <p key={index}>{error}</p>
                    ));
                    setErrorMessage(errorElements);
                  }
                },
                onSkip() {
                  navigate(routes.feedManagerRoute(currentFeed.id));
                },
              }),
              function onInvalid() {
                setErrorMessage([
                  <span key="1">Check field errors below</span>,
                ]);
              }
            )}
          >
            {t('back')}
          </FeedEditAppBar.BackButton>
          <FeedEditAppBar.NextButton
            loading={formProps.formState.isSubmitting}
            onClick={onSubmit}
          >
            {t('next')}
          </FeedEditAppBar.NextButton>
        </Stack>
      </form>
    </FormProvider>
  );
}

function ProductIdentifier({ maxDropdownItemDisplay, onSubmit }) {
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const { data: sampleData, loading } = useSampleData();
  const { data: aiMapping, loadingAiMapping } = useFieldMappingPrediction();
  const theme = useTheme();
  const formProps = useFormContext();
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();
  const { currentStore } = useCurrentStore();
  const matchesLg = useMatches('lg');

  useIntroJs({ key: 'intro_step_two', enabled: currentStore.isShowQuickGuide });

  return (
    <>
      <MappingSection
        title={t('product_id')}
        helpText={t('match_identifier_from_feed_to_store')}
        fields={[
          {
            name: 'productIdentifierSyncField.extra_attributes',
            nameNode: (
              <ShopifyProductKey
                onChange={(_, fieldName) => {
                  if (fieldName) {
                    formProps.setValue(`shopifyProductKey`, fieldName.key);
                  }

                  formProps.trigger(
                    `productIdentifierSyncField.field_mapping`,
                    { shouldFocus: true }
                  );
                }}
              />
            ),
            mappingNode: (
              <>
                <MappingStack>
                  <Stack
                    sx={{ width: '100%' }}
                    data-hint="Select which field from your feed corresponds to your store's product identifier (e.g SKU.). This ensures accurate product matching."
                  >
                    <Tags
                      placeholder={
                        currentFeed.ioMode === 'in'
                          ? t('mapping_placeholder')
                          : t('export_mapping_placeholder')
                      }
                      whitelist={
                        currentFeed.ioMode === 'in'
                          ? sampleData?.map((data, index) => ({
                              value: data.key
                                ? currentFeed.hasHeader
                                  ? compareRegexAsString(data.key)
                                  : String(index + 1)
                                : '-',
                              text: data.value
                                ? truncate(data.value, 120)
                                : '-',
                            }))
                          : []
                      }
                      renderOption={(props, item) => (
                        <Box
                          {...props}
                          sx={{
                            pointerEvents:
                              props.value === '-' ? 'none' : 'initial',
                          }}
                        >
                          <Typography component="div" variant="body1">
                            {item.value}
                          </Typography>
                          <Typography
                            component="div"
                            variant="body2"
                            sx={{
                              wordBreak: 'break-all',
                              color: theme.palette.grey[300],
                            }}
                          >
                            {item.text}
                          </Typography>
                        </Box>
                      )}
                      name="productIdentifierSyncField.field_mapping"
                      control={formProps.control}
                      loading={loading}
                      disabled={formProps.watch(
                        `productIdentifierSyncField.is_locked`
                      )}
                      settings={{
                        delimiters: /\+\+/,
                        dropdown: {
                          enabled: 0, // a;ways show suggestions dropdown
                          maxItems: maxDropdownItemDisplay,
                          caseSensitive: true,
                          placeAbove: false,
                        },
                      }}
                      helperTextProps={{ display: 'inline' }}
                      helperText={
                        formProps.getFieldState?.(
                          'productIdentifierSyncField.field_mapping'
                        )?.invalid &&
                        formProps.getFieldState?.(
                          'productIdentifierSyncField.field_mapping'
                        )?.error?.message !== 'mapping_field_index_only' && (
                          <Link to="https://stocksync.crisp.help/en/article/what-is-matching-column-mkzcny/">
                            <Button
                              variant="text"
                              size="small"
                              sx={{ padding: 0, marginLeft: '8px' }}
                              startIcon={
                                <Icon type="default" icon={faCircleQuestion} />
                              }
                            />
                          </Link>
                        )
                      }
                    />
                    {match({
                      supplierType: currentFeed.supplier?.supplierType,
                      humanizeFeedType: currentFeed.humanizeFeedType,
                      matchedAiMappings: (aiMapping ?? []).filter(
                        (mapping) =>
                          mapping.field ===
                            formProps.getValues(
                              'productIdentifierSyncField.field_name'
                            ) &&
                          !formProps
                            .getValues(
                              'productIdentifierSyncField.field_mapping'
                            )
                            ?.includes(mapping.column)
                      ),
                      hasHeader: currentFeed.hasHeader,
                    })
                      .with(
                        {
                          supplierType: 'connection',
                          humanizeFeedType: P.when(
                            (feedType) =>
                              feedType === 'update' || feedType === 'import'
                          ),
                          matchedAiMappings: P.when(
                            (mappings) => mappings.length > 0
                          ),
                          hasHeader: true,
                        },
                        ({ matchedAiMappings }) => (
                          <Box
                            sx={{
                              backgroundColor: '#6E6D7A0D',
                              margin: '16px 0',
                              padding: '8px',
                            }}
                          >
                            <Stack
                              direction={matchesLg ? 'column' : 'row'}
                              justifyContent="space-between"
                              alignItems={matchesLg ? 'unset' : 'center'}
                              spacing={matchesLg ? 2 : 0}
                            >
                              <Stack
                                direction={matchesLg ? 'column' : 'row'}
                                spacing={1}
                              >
                                {matchedAiMappings.map((mapping, index) => (
                                  <Button
                                    key={index}
                                    variant="outlined"
                                    loading={loadingAiMapping}
                                    startIcon={
                                      <Icon
                                        type="default"
                                        icon={faPlus}
                                        fontSize={12}
                                      />
                                    }
                                    onClick={() => {
                                      const existingValue = (
                                        formProps.getValues(
                                          'productIdentifierSyncField.field_mapping'
                                        ) || ''
                                      )
                                        .split(',')
                                        .filter(Boolean);

                                      const updatedValue = [
                                        ...new Set([
                                          ...existingValue,
                                          mapping.column,
                                        ]),
                                      ].join('++');

                                      formProps.setValue(
                                        'productIdentifierSyncField.field_mapping',
                                        updatedValue
                                      );
                                    }}
                                    sx={{
                                      backgroundColor: 'white',
                                      borderColor: '#6E6D7A26',
                                      '&:hover': {
                                        border: '1px solid #8357F5',
                                        backgroundColor: 'white',
                                      },
                                    }}
                                  >
                                    <Typography sx={{ color: '#8357F5' }}>
                                      {mapping.column}
                                    </Typography>
                                  </Button>
                                ))}
                              </Stack>
                              <Typography sx={{ color: '#8357F5' }}>
                                {t('suggested_by_ai')} &nbsp;
                                <Icon type="default" icon={faStars} />
                              </Typography>
                            </Stack>
                          </Box>
                        )
                      )
                      .otherwise(() => null)}
                  </Stack>
                </MappingStack>
              </>
            ),
            settingsNode: (
              <div data-hint="Extra setting for the particular fields">
                <ExtraAttribute
                  extraAttributeDrawer={extraAttributeDrawer}
                  setExtraAttributeDrawer={setExtraAttributeDrawer}
                  mappingTitle={
                    formProps.formState.defaultValues
                      ?.productIdentifierSyncField.field_name
                  }
                  onSubmit={formProps.handleSubmit(
                    onSubmit({
                      onSuccess() {},
                      onError() {},
                      onSkip() {},
                    })
                  )}
                />
              </div>
            ),
            settingsOnClick: () => {
              setExtraAttributeDrawer(
                formProps.formState.defaultValues?.productIdentifierSyncField
                  .field_name
              );
            },
          },
        ]}
      />
      <Box sx={{ marginBottom: '32px' }} />
    </>
  );
}

function StoreProductFields({
  showDeleteFieldMapping,
  newMapping,
  maxDropdownItemDisplay,
  onSubmit,
}) {
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { data: sampleData, loading } = useSampleData();
  const { data: aiMapping, loadingAiMapping } = useFieldMappingPrediction();
  const matchesLg = useMatches('lg');
  const formProps = useFeedMappingFormContext();
  const syncFieldSettingsProps = useFieldArray({
    control: formProps.control,
    name: 'syncFieldSettings',
  });

  // watching array syncFieldSettings slows render, watch as subscription
  // React.useEffect(() => {
  //   // watch also accepts a callback, doesn't rerender
  //   const subscription = formProps.watch(({ syncFieldSettings }) => {
  //     console.log(
  //       `syncFieldSettings:`,
  //       JSON.stringify(syncFieldSettings, null, 2)
  //     );
  //   });
  //   return () => subscription.unsubscribe();
  // }, [formProps]);
  const syncFieldSettings = syncFieldSettingsProps.fields.map(
    (field, index) => ({
      ...field,
      ...formProps.watch('syncFieldSettings')[index],
    })
  );
  const fields = React.useMemo(() => {
    return syncFieldSettings.map((field, index) => {
      return {
        field,
        name: `syncFieldSettings.${index}.extra_attributes`,
        nameNode: (
          <NonProductIdentifier
            mapping={field.field_name}
            index={index}
            mappingEditable={
              currentFeed.feedType !== 'import' ||
              (currentFeed.feedType === 'import' &&
                !showDeleteFieldMapping.includes(field.field_name))
            }
            fieldArrayProps={syncFieldSettingsProps}
          />
        ),
        mappingNode: (
          <MappingStack
            icon={
              !field.is_locked &&
              !['compare_price_at_region', 'price_region'].includes(
                field.field_name
              ) &&
              (!onlySelectionForSyncFieldSettings.includes(field.field_name) ||
                field.extra_attributes.static_flag === false ||
                field.extra_attributes.static_flag === 'false') && (
                <QuickEditButtonAndDialog
                  name={`syncFieldSettings.${index}.field_mapping`}
                />
              )
            }
          >
            <Stack sx={{ width: '100%' }}>
              {onlySelectionForSyncFieldSettings.includes(field.field_name) &&
              (field.extra_attributes.static_flag === true ||
                field.extra_attributes.static_flag === 'true') ? (
                <Autocomplete
                  control={formProps.control}
                  name={`syncFieldSettings.${index}.field_mapping`}
                  options={dropdownListByMapping[field.field_name].map(
                    (o) => o.value
                  )}
                  getOptionLabel={(option) => t(option)}
                />
              ) : ['compare_price_at_region', 'price_region'].includes(
                  field.field_name
                ) ? (
                <Paper
                  sx={{
                    backgroundColor: '#f1f1f1',
                    borderRadius: '4px',
                    border: '1px solid rgba(0, 0, 0, 0.23)',
                    height: '100%',
                    minHeight: '56px',
                    padding: '16px 7px 7px 14px',
                    fontSize: '14px',
                  }}
                >
                  {t('price_region_placeholder')}
                </Paper>
              ) : (
                <Tags
                  placeholder={
                    currentFeed.ioMode === 'in'
                      ? t('mapping_placeholder')
                      : t('export_mapping_placeholder')
                  }
                  name={`syncFieldSettings.${index}.field_mapping`}
                  control={formProps.control}
                  loading={loading}
                  disabled={formProps.watch(
                    `syncFieldSettings.${index}.is_locked`
                  )}
                  whitelist={match({
                    ioMode: currentFeed.ioMode,
                    staticFlag: field.extra_attributes.static_flag,
                  })
                    .with(
                      {
                        ioMode: 'in',
                        staticFlag: P.when(
                          (staticFlag) =>
                            staticFlag === false ||
                            staticFlag === 'false' ||
                            !staticFlag
                        ),
                      },
                      () =>
                        sampleData?.map((data, index) => ({
                          value: data.key
                            ? currentFeed.hasHeader
                              ? compareRegexAsString(data.key)
                              : String(index + 1)
                            : '-',
                          text: data.value ? truncate(data.value, 120) : '-',
                        }))
                    )
                    .otherwise(() => [])}
                  renderOption={(props, item) => (
                    <Box
                      {...props}
                      sx={{
                        pointerEvents: props.value === '-' ? 'none' : 'initial',
                      }}
                    >
                      <Typography component="div" variant="body1">
                        {item.value}
                      </Typography>
                      <Typography
                        component="div"
                        variant="body2"
                        sx={{
                          wordBreak: 'break-all',
                          color: theme.palette.grey[300],
                        }}
                      >
                        {item.text}
                      </Typography>
                    </Box>
                  )}
                  settings={{
                    delimiters:
                      formProps.watch(
                        `syncFieldSettings.${index}.field_name`
                      ) === 'product_type'
                        ? '++'
                        : commaNotInsideQuotes,
                    dropdown: {
                      enabled: 0,
                      maxItems: maxDropdownItemDisplay,
                      caseSensitive: true,
                      placeAbove: false,
                    },
                  }}
                  helperTextProps={{ display: 'inline' }}
                  helperText={
                    formProps.getFieldState?.(
                      `syncFieldSettings.${index}.field_mapping`
                    )?.invalid &&
                    formProps.getFieldState?.(
                      `syncFieldSettings.${index}.field_mapping`
                    )?.error?.message !== 'mapping_field_index_only' && (
                      <Link to="https://stocksync.crisp.help/en/article/what-is-matching-column-mkzcny/">
                        <Button
                          variant="text"
                          size="small"
                          sx={{ padding: 0, marginLeft: '8px' }}
                          startIcon={
                            <Icon type="default" icon={faCircleQuestion} />
                          }
                        />
                      </Link>
                    )
                  }
                />
              )}
              {match({
                supplierType: currentFeed.supplier?.supplierType,
                matchedAiMappings: (aiMapping ?? []).filter(
                  (mapping) =>
                    mapping.field === field.field_name &&
                    !field.field_mapping?.includes(mapping.column)
                ),
                humanizeFeedType: currentFeed.humanizeFeedType,
                fieldName: field.field_name,
                hasHeader: currentFeed.hasHeader,
              })
                .with(
                  {
                    supplierType: 'connection',
                    humanizeFeedType: P.when(
                      (feedType) =>
                        feedType === 'update' || feedType === 'import'
                    ),
                    fieldName: P.when(
                      (name) =>
                        name === 'quantity' ||
                        name === 'price' ||
                        name === 'product_title'
                    ),
                    matchedAiMappings: P.when(
                      (mappings) => mappings.length > 0
                    ),
                    hasHeader: true,
                  },
                  ({ matchedAiMappings }) => (
                    <Box
                      sx={{
                        backgroundColor: '#6E6D7A0D',
                        margin: '16px 0',
                        padding: '8px',
                      }}
                    >
                      <Stack
                        direction={matchesLg ? 'column' : 'row'}
                        justifyContent="space-between"
                        alignItems={matchesLg ? 'unset' : 'center'}
                        spacing={matchesLg ? 2 : 0}
                      >
                        <Stack
                          direction={matchesLg ? 'column' : 'row'}
                          spacing={1}
                        >
                          {matchedAiMappings.map((mapping, index) => (
                            <Button
                              key={index}
                              variant="outlined"
                              loading={loadingAiMapping}
                              startIcon={
                                <Icon
                                  type="default"
                                  icon={faPlus}
                                  fontSize={12}
                                />
                              }
                              onClick={() => {
                                const existingValue = (
                                  formProps.getValues(
                                    `syncFieldSettings.${index}.field_mapping`
                                  ) || ''
                                )
                                  .split(',')
                                  .filter(Boolean);

                                const updatedValue = [
                                  ...new Set([
                                    ...existingValue,
                                    mapping.column,
                                  ]),
                                ].join(',');

                                formProps.setValue(
                                  `syncFieldSettings.${index}.field_mapping`,
                                  updatedValue
                                );
                              }}
                              sx={{
                                backgroundColor: 'white',
                                borderColor: '#6E6D7A26',
                                '&:hover': {
                                  border: '1px solid #8357F5',
                                  backgroundColor: 'white',
                                },
                              }}
                            >
                              <Typography sx={{ color: '#8357F5' }}>
                                {mapping.column}
                              </Typography>
                            </Button>
                          ))}
                        </Stack>
                        <Typography sx={{ color: '#8357F5' }}>
                          {t('suggested_by_ai')} &nbsp;
                          <Icon type="default" icon={faStars} />
                        </Typography>
                      </Stack>
                    </Box>
                  )
                )
                .otherwise(() => null)}
              {staticValueBasePlatform[currentStore.provider][
                currentFeed.humanizeFeedType
              ].includes(field.field_name) && (
                <ExtraAttributeStaticFlag
                  name={`syncFieldSettings.${index}.extra_attributes.static_flag`}
                  disabled={formProps.watch(
                    `syncFieldSettings.${index}.is_locked`
                  )}
                  onChange={(_, value) => {
                    syncFieldSettingsProps.update(index, {
                      ...syncFieldSettingsProps.fields[index],
                      ...field,
                      // ...(fieldName ? { field_name: fieldName } : {}),
                      extra_attributes: {
                        ...field.extra_attributes,
                        static_flag: value === 'true' ? true : false,
                      },
                    });
                  }}
                />
              )}
              <ErrorHelperText
                name={`syncFieldSettings.${index}`}
                errors={formProps.formState.errors}
              />
            </Stack>
          </MappingStack>
        ),
        settingsNode: (
          <ExtraAttribute
            index={index}
            extraAttributeDrawer={extraAttributeDrawer}
            setExtraAttributeDrawer={setExtraAttributeDrawer}
            mappingTitle={field.field_name}
            onSubmit={formProps.handleSubmit(
              onSubmit({
                onSuccess() {},
                onError() {},
                onSkip() {},
              })
            )}
          />
        ),
        settingsOnClick: () => setExtraAttributeDrawer(field.field_name),
        deleteProps: {
          canDelete:
            currentFeed.feedType !== 'import' ||
            (currentFeed.feedType === 'import' &&
              !showDeleteFieldMapping.includes(field.field_name)),
          onDelete: () => {
            // useFieldArray.update auto rerenders for us
            syncFieldSettingsProps.update(index, {
              ...field,
              _destroy: '1',
              field_name: '',
            });
          },
        },
      };
    });
  }, [
    aiMapping,
    currentFeed,
    currentStore.provider,
    extraAttributeDrawer,
    formProps,
    loading,
    loadingAiMapping,
    maxDropdownItemDisplay,
    matchesLg,
    onSubmit,
    sampleData,
    setExtraAttributeDrawer,
    showDeleteFieldMapping,
    syncFieldSettingsProps,
    syncFieldSettings,
    t,
    theme,
  ]);

  return (
    <>
      <MappingSection
        title={t('store_product_field')}
        helpText={t(
          {
            update: 'only_matching_field_will_update_to_store',
            import: 'only_matching_field_will_add_to_store',
            export: 'only_matching_field_will_export_to_destination',
            remove: 'only_matching_field_will_update_to_store',
          }[currentFeed.humanizeFeedType]
        )}
        learnMoreHref="https://help.stock-sync.com/en/category/matching-column-hc3vlq/"
        fields={fields}
        addMoreButtonProps={{
          children: t('add_more_field'),
          onClick: () => syncFieldSettingsProps.append(newMapping),
        }}
      />
    </>
  );
}

function ProductOptionsVariations({
  showDeleteFieldMapping,
  newMapping,
  maxDropdownItemDisplay,
  onSubmit,
}) {
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const [productOptionDialog, setProductOptionDialog] = React.useState(false);
  const { currentFeed } = useCurrentFeed();
  const { data: sampleData, loading } = useSampleData();
  const { t } = useTranslation();
  const theme = useTheme();
  const formProps = useFormContext();
  const productOptionsSyncFieldProps = useFieldArray({
    control: formProps.control,
    name: 'productOptionsSyncField',
  });
  // merge latest values from watch
  const productOptionsSyncField = productOptionsSyncFieldProps.fields.map(
    (field, index) => ({
      ...field,
      ...formProps.watch('productOptionsSyncField')[index],
    })
  );

  // // watching array syncFieldSettings slows render, watch as subscription
  // React.useEffect(() => {
  //   // watch also accepts a callback, doesn't rerender
  //   const subscription = formProps.watch(({ productOptionsSyncField }) => {
  //     console.log(
  //       `productOptionsSyncField:`,
  //       JSON.stringify(productOptionsSyncField, null, 2)
  //     );
  //   });
  //   return () => subscription.unsubscribe();
  // }, [formProps]);

  return (
    <MappingSection
      title={t('product_options')}
      learnMoreHref="https://help.stock-sync.com/en/article/variant-group-option-field-product-options-cnrse9/?bust=1708681468246"
      helpText={t('group_together_under_product')}
      fields={productOptionsSyncField.map((field, index) => ({
        field,
        name: `productOptionsSyncField.${index}.extra_attributes`,
        nameNode: (
          <NonProductIdentifierForOption
            mapping={field.field_name}
            index={index}
            mappingEditable={
              currentFeed.feedType !== 'import' ||
              (currentFeed.feedType === 'import' &&
                !showDeleteFieldMapping.includes(field.field_name))
            }
          />
        ),
        settingsNode: (
          <ExtraAttribute
            index={index}
            extraAttributeDrawer={extraAttributeDrawer}
            setExtraAttributeDrawer={setExtraAttributeDrawer}
            mappingTitle={field.field_name}
            onSubmit={formProps.handleSubmit(
              onSubmit({
                onSuccess() {},
                onError() {},
                onSkip() {},
              })
            )}
          />
        ),
        settingsOnClick: () => setExtraAttributeDrawer(field.field_name),
        mappingNode: (
          <MappingStack
            icon={
              !field.is_locked && (
                <QuickEditButtonAndDialog
                  name={`productOptionsSyncField.${index}.field_mapping`}
                />
              )
            }
          >
            <Stack sx={{ width: '100%' }}>
              <Tags
                placeholder={
                  currentFeed.ioMode === 'in'
                    ? t('mapping_placeholder')
                    : t('export_mapping_placeholder')
                }
                name={`productOptionsSyncField.${index}.field_mapping`}
                control={formProps.control}
                loading={loading}
                disabled={formProps.watch(
                  `productOptionsSyncField.${index}.is_locked`
                )}
                onChange={(event) => {
                  formProps.setValue(
                    `productOptionsSyncField.${index}.field_mapping`,
                    event.target.value
                  );
                  if (
                    !formProps.watch(
                      `productOptionsSyncField.${index}.extra_attributes.${formProps.watch(
                        `productOptionsSyncField.${index}.field_name`
                      )}_name`
                    )
                  ) {
                    formProps.setValue(
                      `productOptionsSyncField.${index}.extra_attributes.${formProps.watch(
                        `productOptionsSyncField.${index}.field_name`
                      )}_name`,
                      event.target.value
                    );
                  }
                }}
                whitelist={
                  currentFeed.ioMode === 'in'
                    ? sampleData?.map((data, index) => ({
                        value: data.key
                          ? currentFeed.hasHeader
                            ? compareRegexAsString(data.key)
                            : String(index + 1)
                          : '-',
                        text: data.value ? truncate(data.value, 120) : '-',
                      }))
                    : []
                }
                renderOption={(props, item) => (
                  <Box
                    {...props}
                    sx={{
                      pointerEvents: props.value === '-' ? 'none' : 'initial',
                    }}
                  >
                    <Typography component="div" variant="body1">
                      {item.value}
                    </Typography>
                    <Typography
                      component="div"
                      variant="body2"
                      sx={{
                        wordBreak: 'break-all',
                        color: theme.palette.grey[300],
                      }}
                    >
                      {item.text}
                    </Typography>
                  </Box>
                )}
                settings={{
                  delimiters: commaNotInsideQuotes,
                  dropdown: {
                    enabled: 0, // a;ways show suggestions dropdown
                    maxItems: maxDropdownItemDisplay,
                    caseSensitive: true,
                    placeAbove: false,
                  },
                }}
                helperTextProps={{ display: 'inline' }}
                helperText={
                  formProps.getFieldState?.(
                    `productOptionsSyncField.${index}.field_mapping`
                  )?.invalid &&
                  formProps.getFieldState?.(
                    `productOptionsSyncField.${index}.field_mapping`
                  )?.error?.message !== 'mapping_field_index_only' && (
                    <Link to="https://stocksync.crisp.help/en/article/what-is-matching-column-mkzcny/">
                      <Button
                        variant="text"
                        size="small"
                        sx={{ padding: 0, marginLeft: '8px' }}
                        startIcon={
                          <Icon type="default" icon={faCircleQuestion} />
                        }
                      />
                    </Link>
                  )
                }
              />
              <ErrorHelperText
                name={`productOptionsSyncField.${index}`}
                errors={formProps.formState.errors}
              />
            </Stack>
          </MappingStack>
        ),
        deleteProps: {
          canDelete: true,
          onDelete: () => {
            // useFieldArray.update auto rerenders for us
            productOptionsSyncFieldProps.update(index, {
              ...field,
              _destroy: '1',
              field_name: '',
            });
          },
        },
      }))}
      extraButton={
        currentFeed.feedType === 'import' && (
          <>
            <Button
              variant="text"
              onClick={() => setProductOptionDialog(true)}
              sx={{ padding: '8px 0px 0px' }}
            >
              <Typography variant="body1" sx={{ fontWeight: 400 }}>
                {t('product_variations')}
              </Typography>
            </Button>
            <Dialog
              open={productOptionDialog}
              onClose={() => setProductOptionDialog(false)}
            >
              <Dialog.Title sx={{ fontSize: '16px', fontWeight: 600 }}>
                {t('product_variations')}
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: 5,
                    right: 5,
                  }}
                  onClick={() => setProductOptionDialog(false)}
                >
                  <Icon type="default" icon={faXmark} fontSize={21} />
                </IconButton>
              </Dialog.Title>
              <Dialog.Content sx={{ padding: '16px' }}>
                <img
                  src={productVariation}
                  width={500}
                  style={{
                    display: 'block',
                    marginLeft: 'auto',
                    marginRight: 'auto',
                  }}
                />
              </Dialog.Content>
            </Dialog>
          </>
        )
      }
      addMoreButtonProps={{
        children: t('add_option'),
        onClick: () => productOptionsSyncFieldProps.append(newMapping),
      }}
    />
  );
}

function Metafields({ maxDropdownItemDisplay, onSubmit }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: {
      provider,
      feedConstants: { metafield_owners: metafieldOwners },
      syncFieldConstants,
    },
  } = useCurrentStore();
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
  const { currentFeed } = useCurrentFeed();
  const { data: sampleData, loading } = useSampleData();
  const formProps = useFormContext();
  // first call metafield is to get the main store field
  // 2nd call metafield is to get the target store field to use to for overwrite
  const { data: metafieldDefinitions, fetching } = useMetafieldDefinitions();

  const [{ data: matchedMetafield, fetching: metafieldLoading }] = useQuery({
    query: GetMatchedMetafields,
    variables: {
      // for store to store only need variable
      publicToken:
        currentFeed.combinedSourceType === 'shopify_store'
          ? currentFeed.sourceUrl
          : undefined,
    },
    pause:
      currentFeed.combinedSourceType !== 'shopify_store' &&
      provider === 'shopify',
  });

  const matchedMetafieldDefinitions = React.useMemo(
    () =>
      matchedMetafield?.getMatchedMetafields
        ? matchedMetafield.getMatchedMetafields.map((o) => ({
            ...o,
            ownerType: {
              PRODUCT: 'product',
              PRODUCTVARIANT: 'variant',
            }[o.ownerType] as 'product' | 'variant',
          }))
        : [],
    [matchedMetafield]
  );

  const onClose = () => setAnchorEl(null);

  const metafieldCounters = (
    formProps.formState.defaultValues?.metafields || []
  )
    .map((o) => o.field_name.split('_')[1])
    .filter(Boolean);
  const maxCounter = Math.max(0, ...metafieldCounters);
  const listAllMetafieldForStoreToStore = React.useMemo(
    () =>
      matchedMetafieldDefinitions.map((metafield, i) =>
        Object.assign(metafield, {
          field_name: `metafield_${(maxCounter > 0 ? maxCounter : 0) + i + 1}`,
          field_mapping: '',
          extra_attributes: {
            metafield_key: metafield.key,
            metafield_namespace: metafield.namespace,
            metafield_owner: metafield.ownerType,
            metafield_type: metafield.type.name,
            blank_val: '-',
            metafield_date_format: 'auto',
            metafield_weight_unit: 'auto',
            metafield_volume_unit: 'ml',
            is_true_indicator: 'true',
            is_false_indicator: 'false',
            static_flag: false,
            find: '',
            replace: '',
            metafield_dimension_unit: 'cm',
            skip_if_blank: true,
            remove_if_blank: false,
          },
          _namespaceAndKey: `${metafield.namespace}.${metafield.key}`,
        })
      ),
    [matchedMetafieldDefinitions, maxCounter]
  );

  const metafieldsProps = useFieldArray({
    control: formProps.control,
    name: 'metafields',
  });
  // merge latest values from watch
  const metafields = metafieldsProps.fields.map((field, index) => ({
    ...field,
    ...formProps.watch('metafields')[index],
  }));

  // watching array syncFieldSettings slows render, watch as subscription
  // React.useEffect(() => {
  //   // watch also accepts a callback, doesn't rerender
  //   const subscription = formProps.watch(({ metafields }) => {
  //     console.log(`metafields:`, JSON.stringify(metafields, null, 2));
  //   });
  //   return () => subscription.unsubscribe();
  // }, [formProps]);

  return (
    <MappingSection
      title={t('metafield')}
      learnMoreHref="https://help.stock-sync.com/en/article/metafields-ptbnev/"
      helpText={t('metafield_helptext')}
      extraButton={
        !metafieldLoading &&
        matchedMetafieldDefinitions.length > 0 &&
        provider === 'shopify' &&
        currentFeed.combinedSourceType === 'shopify_store' ? (
          <>
            <Button
              variant="text"
              sx={{ padding: '7px 0px' }}
              startIcon={<Icon type="kit" className="fak fa-resetmapping1" />}
              onClick={(event) => {
                setAnchorEl(event.currentTarget);
              }}
            >
              {t('reset_metafield')}
            </Button>
            <PopoverConfirm
              slotProps={{ paper: { sx: { width: 230 } } }}
              open={Boolean(anchorEl)}
              anchorEl={anchorEl}
              onClose={onClose}
              cancelButtonProps={{
                children: t('no'),
                onClick: onClose,
              }}
              okButtonProps={{
                children: t('yes'),
                onClick: () => {
                  onClose();
                  metafieldsProps.append(listAllMetafieldForStoreToStore);
                  metafieldCounters.map((a, index) => {
                    if (Number(a) <= maxCounter) {
                      // to add _destroy based on the max counter
                      formProps.setValue(`metafields.${index}._destroy`, '1');
                      formProps.setValue(`metafields.${index}.field_name`, '');
                    }
                  });
                },
              }}
            >
              {t('confirm_to_reset_metafield')}
            </PopoverConfirm>
          </>
        ) : (
          <></>
        )
      }
      cardContent={
        <>
          {currentFeed.combinedSourceType === 'shopify_store' && (
            <Switch
              control={formProps.control}
              name="isAutoGenerateMetafieldDefinitions"
              label={t('create_metafield_not_found')}
              sx={{ margin: '0px 0px 24px 24px' }}
            />
          )}
        </>
      }
      fields={metafields.map((field, index) => {
        const { watch } = formProps;
        const extraAttributes = `metafields.${index}.extra_attributes`;
        const key = watch(`${extraAttributes}.metafield_key`);
        const namespace = watch(`${extraAttributes}.metafield_namespace`);
        const owner = watch(`${extraAttributes}.metafield_owner`);

        const disableMetafieldOwner = Boolean(
          metafieldDefinitions.find(
            (o) =>
              o.key === key &&
              o.namespace === namespace &&
              o.ownerType === owner
          )
        );

        return {
          field,
          name: `metafields.${index}.extra_attributes`,
          nameNode: (
            <Autocomplete
              allowBlank={false} // no blank for renderOption
              blurOnSelect // trigger validation
              // freeSolo
              openOnFocus
              forcePopupIcon
              control={formProps.control}
              noOptionsText={t('metafield_not_found')}
              name={`metafields.${index}._namespaceAndKey`}
              variant="standard"
              label={t('metafield_namespace_and_key')}
              options={metafieldDefinitions}
              getOptionLabel={(option) =>
                typeof option === 'string'
                  ? option
                  : option.namespace && option.key
                    ? `${option.namespace}.${option.key}`
                    : ''
              }
              isOptionEqualToValue={(option, value) => {
                const isObject = typeof value === 'object' && value !== null;
                return (
                  `${option.namespace}.${option.key}` ===
                    (isObject ? `${value.namespace}.${value.key}` : value) &&
                  option.ownerType === owner
                );
              }}
              disabled={
                formProps.watch(`metafields.${index}.is_locked`) ?? fetching
              }
              getOptionDisabled={(option) =>
                option.type.name.includes('reference')
              }
              loading={fetching}
              onChange={(_, value) => {
                const defaultMetafield = syncFieldConstants.metafield;

                // due to freeSolo + autoSelect, on blur - value will be string (user text)
                // https://mui.com/material-ui/api/autocomplete/#Autocomplete-prop-autoSelect
                const valueIsObject =
                  typeof value === 'object' && value !== null;

                if (valueIsObject) {
                  const { namespace, key, ownerType } = value;

                  formProps.setValue(
                    `${extraAttributes}.metafield_namespace`,
                    namespace ?? ''
                  );
                  formProps.setValue(
                    `${extraAttributes}.metafield_key`,
                    key ?? ''
                  );

                  formProps.setValue(
                    `${extraAttributes}.metafield_owner`,
                    ownerType ?? ''
                  );

                  const metafieldDefinition = metafieldDefinitions.find(
                    (mfd) =>
                      mfd.key === value.key &&
                      mfd.namespace === value.namespace &&
                      mfd.ownerType === value.ownerType
                  );

                  if (metafieldDefinition) {
                    formProps.setValue(
                      `${extraAttributes}.metafield_type`,
                      metafieldDefinition.type.name
                    );
                    formProps.setValue(
                      `${extraAttributes}.blank_val`,
                      metafieldDefinition.type.name.includes('list.')
                        ? '[]'
                        : defaultMetafield.blank_val
                    );
                  }
                }

                formProps.trigger(`metafields.${index}.field_mapping`, {
                  shouldFocus: true,
                });

                formProps.setValue(
                  `${extraAttributes}.metafield_date_format`,
                  defaultMetafield.metafield_date_format
                );
                formProps.setValue(
                  `${extraAttributes}.find`,
                  defaultMetafield.find
                );
                formProps.setValue(
                  `${extraAttributes}.replace`,
                  defaultMetafield.replace
                );
              }}
              renderOption={(props, option) => {
                const isDisabled = option.type.name.includes('reference');
                return (
                  <Tooltip
                    key={option.key} // recommand just li tag else will get A props object containing a "key" prop is being spread into JSX error on console:
                    variant="default"
                    placement="right"
                    title={
                      isDisabled
                        ? 'This metafield not supported to be updated'
                        : ''
                    }
                  >
                    <li {...props}>
                      <Stack sx={{ width: '100%', overflowWrap: 'break-word' }}>
                        <Box component="span">{`${option.namespace}.${option.key}`}</Box>
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.palette.text.secondary,
                          }}
                        >
                          {t('owner')} - {t(option.ownerType)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.palette.text.secondary,
                          }}
                        >
                          {t('content_type')} - {t(option.type.name)}
                        </Typography>
                      </Stack>
                    </li>
                  </Tooltip>
                );
              }}
              sx={{ maxWidth: '400px' }}
            />
          ),
          settingsNode: (
            <ExtraAttribute
              index={index}
              extraAttributeDrawer={extraAttributeDrawer}
              setExtraAttributeDrawer={setExtraAttributeDrawer}
              mappingTitle={field.field_name}
              onSubmit={formProps.handleSubmit(
                onSubmit({
                  onSuccess() {},
                  onError() {},
                  onSkip() {},
                })
              )}
            />
          ),
          settingsOnClick: () => setExtraAttributeDrawer(field.field_name),
          mappingNode: (
            <>
              <MappingStack>
                <TextField
                  select
                  control={formProps.control}
                  name={`metafields.${index}.extra_attributes.metafield_owner`}
                  label={t('label_metafield_owner')}
                  disabled={disableMetafieldOwner}
                >
                  {metafieldOwners.map((option) => (
                    <MenuItem key={option.key} value={option.key}>
                      {option.value}
                    </MenuItem>
                  ))}
                </TextField>
              </MappingStack>
              <MappingStack
                icon={
                  !field.is_locked && (
                    <QuickEditButtonAndDialog
                      name={`metafields.${index}.field_mapping`}
                    />
                  )
                }
              >
                <Stack sx={{ width: '100%' }}>
                  <Tags
                    placeholder={
                      currentFeed.ioMode === 'in'
                        ? t('mapping_placeholder')
                        : t('export_mapping_placeholder')
                    }
                    name={`metafields.${index}.field_mapping`}
                    control={formProps.control}
                    loading={loading}
                    validateOnChange={false}
                    onChange={debounce(() => {
                      formProps.trigger(`metafields.${index}.field_mapping`);
                    }, 1500)}
                    disabled={formProps.watch(`metafields.${index}.is_locked`)}
                    whitelist={match({
                      ioMode: currentFeed.ioMode,
                      staticFlag: field.extra_attributes.static_flag,
                    })
                      .with(
                        {
                          ioMode: 'in',
                          staticFlag: P.when(
                            (staticFlag) =>
                              staticFlag === false ||
                              staticFlag === 'false' ||
                              !staticFlag
                          ),
                        },
                        () =>
                          sampleData?.map((data, index) => ({
                            value: data.key
                              ? currentFeed.hasHeader
                                ? compareRegexAsString(data.key)
                                : String(index + 1)
                              : '-',
                            text: data.value ? truncate(data.value, 120) : '-',
                          }))
                      )
                      .otherwise(() => [])}
                    renderOption={(props, item) => (
                      <Box
                        {...props}
                        sx={{
                          pointerEvents:
                            props.value === '-' ? 'none' : 'initial',
                        }}
                      >
                        <Typography component="div" variant="body1">
                          {item.value}
                        </Typography>
                        <Typography
                          component="div"
                          variant="body2"
                          sx={{
                            wordBreak: 'break-all',
                            color: theme.palette.grey[300],
                          }}
                        >
                          {item.text}
                        </Typography>
                      </Box>
                    )}
                    settings={{
                      delimiters: commaNotInsideQuotes,
                      dropdown: {
                        enabled: 0,
                        maxItems: maxDropdownItemDisplay,
                        caseSensitive: true,
                        placeAbove: false,
                      },
                    }}
                    helperTextProps={{ display: 'inline' }}
                    helperText={
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.invalid &&
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.error?.message !== 'mapping_field_index_only' && (
                        <Link to="https://stocksync.crisp.help/en/article/what-is-matching-column-mkzcny/">
                          <Button
                            variant="text"
                            size="small"
                            sx={{ padding: 0, marginLeft: '8px' }}
                            startIcon={
                              <Icon type="default" icon={faCircleQuestion} />
                            }
                          />
                        </Link>
                      )
                    }
                  />
                  {currentFeed.humanizeFeedType !== 'export' && (
                    <ExtraAttributeStaticFlag
                      name={`metafields.${index}.extra_attributes.static_flag`}
                      disabled={formProps.watch(
                        `metafields.${index}.is_locked`
                      )}
                      onChange={(_, value) => {
                        metafieldsProps.update(index, {
                          ...metafieldsProps.fields[index],
                          ...field,
                          // ...(fieldName ? { field_name: fieldName } : {}),
                          extra_attributes: {
                            ...field.extra_attributes,
                            static_flag: value === 'true' ? true : false,
                          },
                        });
                      }}
                    />
                  )}
                </Stack>
              </MappingStack>
            </>
          ),
          deleteProps: {
            canDelete: true,
            onDelete: () => {
              // useFieldArray.update auto rerenders for us
              metafieldsProps.update(index, {
                ...field,
                _destroy: '1',
                field_name: '',
              });
            },
          },
        };
      })}
      addMoreButtonProps={{
        children: t('add_metafield'),
        onClick: () => {
          const metafieldCounters = (
            formProps.formState.defaultValues?.metafields || []
          )
            .concat(metafields)
            .map((o) => o.field_name.split('_')[1])
            .filter(Boolean);
          const maxCounter = Math.max(0, ...metafieldCounters);

          const newMetafield = {
            field_mapping: undefined,
            field_name: `metafield_${
              (maxCounter > 0 ? maxCounter : metafields.length) + 1
            }`,
            user_profile_id: +currentFeed.id,
            extra_attributes: syncFieldConstants['metafield'],
          };

          metafieldsProps.append(newMetafield);
        },
      }}
    />
  );
}

function CustomField({ maxDropdownItemDisplay, onSubmit }) {
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: { syncFieldConstants },
  } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { data: sampleData, loading } = useSampleData();
  const formProps = useFormContext();

  const customFieldProps = useFieldArray({
    control: formProps.control,
    name: 'metafields',
  });
  // merge latest values from watch
  const customField = customFieldProps.fields.map((field, index) => ({
    ...field,
    ...formProps.watch('metafields')[index],
  }));
  console.log(`merged metafields:`, JSON.stringify(customField, null, 2));

  return (
    <MappingSection
      title={t('custom_field')}
      fields={customField.map((field, index) => {
        return {
          field,
          name: `metafields.${index}.extra_attributes`,
          nameNode: (
            <TextField
              name={`metafields.${index}._fieldName`}
              control={formProps.control}
              label="Custom Field"
              variant="standard"
              slotProps={{ htmlInput: { readOnly: true } }}
            />
          ),
          settingsNode: (
            <ExtraAttribute
              index={index}
              extraAttributeDrawer={extraAttributeDrawer}
              setExtraAttributeDrawer={setExtraAttributeDrawer}
              mappingTitle={field.field_name}
              onSubmit={formProps.handleSubmit(
                onSubmit({
                  onSuccess() {},
                  onError() {},
                  onSkip() {},
                })
              )}
            />
          ),
          settingsOnClick: () => setExtraAttributeDrawer(field.field_name),
          mappingNode: (
            <>
              <MappingStack
                icon={
                  !field.is_locked && (
                    <QuickEditButtonAndDialog
                      name={`metafields.${index}.field_mapping`}
                    />
                  )
                }
              >
                <Stack sx={{ width: '100%' }}>
                  <Tags
                    placeholder={
                      currentFeed.ioMode === 'in'
                        ? t('mapping_placeholder')
                        : t('export_mapping_placeholder')
                    }
                    name={`metafields.${index}.field_mapping`}
                    control={formProps.control}
                    loading={loading}
                    disabled={formProps.watch(`metafields.${index}.is_locked`)}
                    whitelist={
                      currentFeed.ioMode === 'in'
                        ? sampleData?.map((data, index) => ({
                            value: data.key
                              ? currentFeed.hasHeader
                                ? compareRegexAsString(data.key)
                                : String(index + 1)
                              : '-',
                            text: data.value ? truncate(data.value, 120) : '-',
                          }))
                        : []
                    }
                    renderOption={(props, item) => (
                      <Box
                        {...props}
                        sx={{
                          pointerEvents:
                            props.value === '-' ? 'none' : 'initial',
                        }}
                      >
                        <Typography component="div" variant="body1">
                          {item.value}
                        </Typography>
                        <Typography
                          component="div"
                          variant="body2"
                          sx={{
                            wordBreak: 'break-all',
                            color: theme.palette.grey[300],
                          }}
                        >
                          {item.text}
                        </Typography>
                      </Box>
                    )}
                    settings={{
                      delimiters: commaNotInsideQuotes,
                      dropdown: {
                        enabled: 0,
                        maxItems: maxDropdownItemDisplay,
                        caseSensitive: true,
                        placeAbove: false,
                      },
                    }}
                    helperTextProps={{ display: 'inline' }}
                    helperText={
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.invalid &&
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.error?.message !== 'mapping_field_index_only' && (
                        <Link to="https://stocksync.crisp.help/en/article/what-is-matching-column-mkzcny/">
                          <Button
                            variant="text"
                            size="small"
                            sx={{ padding: 0, marginLeft: '8px' }}
                            startIcon={
                              <Icon type="default" icon={faCircleQuestion} />
                            }
                          />
                        </Link>
                      )
                    }
                  />
                </Stack>
              </MappingStack>
            </>
          ),
          deleteProps: {
            canDelete: true,
            onDelete: () => {
              // useFieldArray.update auto rerenders for us
              customFieldProps.update(index, {
                ...field,
                _destroy: '1',
                field_name: '',
              });
            },
          },
        };
      })}
      addMoreButtonProps={{
        children: t('add_custom_field'),
        onClick: () => {
          const customFieldCounters = (
            formProps.formState.defaultValues?.metafields || []
          )
            .concat(customField)
            .map((o) => o.field_name.split('_')[1])
            .filter(Boolean);
          const maxCounter = Math.max(0, ...customFieldCounters);

          const newCustomField = {
            field_mapping: undefined,
            field_name: `custom_field_${
              (maxCounter > 0 ? maxCounter : customField.length) + 1
            }`,
            _fieldName: `Custom Field ${
              (maxCounter > 0 ? maxCounter : customField.length) + 1
            }`,
            user_profile_id: +currentFeed.id,
            extra_attributes:
              currentFeed.feedType === 'import'
                ? { custom_field_key: null, create_key_if_not_found: true }
                : syncFieldConstants['custom_field'],
          };

          customFieldProps.append(newCustomField);
        },
      }}
    />
  );
}

function ProductAttribute({ maxDropdownItemDisplay, onSubmit }) {
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: { syncFieldConstants },
  } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { data: sampleData, loading } = useSampleData();
  const formProps = useFormContext();

  const productAttributeProps = useFieldArray({
    control: formProps.control,
    name: 'woocommerceProductAttributes',
  });
  // merge latest values from watch
  const productAttribute = productAttributeProps.fields.map((field, index) => ({
    ...field,
    ...formProps.watch('woocommerceProductAttributes')[index],
  }));
  console.log(
    `merged product attribute:`,
    JSON.stringify(productAttribute, null, 2)
  );

  return (
    <MappingSection
      title={t('product_attribute')}
      fields={productAttribute.map((field, index) => {
        return {
          field,
          name: `woocommerceProductAttributes.${index}.extra_attributes`,
          nameNode: (
            <TextField
              name={`woocommerceProductAttributes.${index}._fieldName`}
              control={formProps.control}
              label={t('product_attribute')}
              variant="standard"
              slotProps={{ htmlInput: { readOnly: true } }}
            />
          ),
          settingsNode: (
            <ExtraAttribute
              index={index}
              extraAttributeDrawer={extraAttributeDrawer}
              setExtraAttributeDrawer={setExtraAttributeDrawer}
              mappingTitle={field.field_name}
              onSubmit={formProps.handleSubmit(
                onSubmit({
                  onSuccess() {},
                  onError() {},
                  onSkip() {},
                })
              )}
            />
          ),
          settingsOnClick: () => setExtraAttributeDrawer(field.field_name),
          mappingNode: (
            <>
              <MappingStack
                icon={
                  !field.is_locked && (
                    <QuickEditButtonAndDialog
                      name={`woocommerceProductAttributes.${index}.field_mapping`}
                    />
                  )
                }
              >
                <Stack sx={{ width: '100%' }}>
                  <Tags
                    placeholder={
                      currentFeed.ioMode === 'in'
                        ? t('mapping_placeholder')
                        : t('export_mapping_placeholder')
                    }
                    name={`woocommerceProductAttributes.${index}.field_mapping`}
                    control={formProps.control}
                    loading={loading}
                    disabled={formProps.watch(
                      `woocommerceProductAttributes.${index}.is_locked`
                    )}
                    whitelist={
                      currentFeed.ioMode === 'in'
                        ? sampleData?.map((data, index) => ({
                            value: data.key
                              ? currentFeed.hasHeader
                                ? compareRegexAsString(data.key)
                                : String(index + 1)
                              : '-',
                            text: data.value ? truncate(data.value, 120) : '-',
                          }))
                        : []
                    }
                    renderOption={(props, item) => (
                      <Box
                        {...props}
                        sx={{
                          pointerEvents:
                            props.value === '-' ? 'none' : 'initial',
                        }}
                      >
                        <Typography component="div" variant="body1">
                          {item.value}
                        </Typography>
                        <Typography
                          component="div"
                          variant="body2"
                          sx={{
                            wordBreak: 'break-all',
                            color: theme.palette.grey[300],
                          }}
                        >
                          {item.text}
                        </Typography>
                      </Box>
                    )}
                    settings={{
                      delimiters: commaNotInsideQuotes,
                      dropdown: {
                        enabled: 0,
                        maxItems: maxDropdownItemDisplay,
                        caseSensitive: true,
                        placeAbove: false,
                      },
                    }}
                    helperTextProps={{ display: 'inline' }}
                    helperText={
                      formProps.getFieldState?.(
                        `woocommerceProductAttributes.${index}.field_mapping`
                      )?.invalid &&
                      formProps.getFieldState?.(
                        `woocommerceProductAttributes.${index}.field_mapping`
                      )?.error?.message !== 'mapping_field_index_only' && (
                        <Link to="">
                          <Button
                            variant="text"
                            size="small"
                            sx={{ padding: 0, marginLeft: '8px' }}
                            startIcon={
                              <Icon type="default" icon={faCircleQuestion} />
                            }
                          />
                        </Link>
                      )
                    }
                  />
                </Stack>
              </MappingStack>
              <ErrorHelperText
                name={`woocommerceProductAttributes.${index}`}
                errors={formProps.formState.errors}
              />
            </>
          ),
          deleteProps: {
            canDelete: true,
            onDelete: () => {
              // useFieldArray.update auto rerenders for us
              productAttributeProps.update(index, {
                ...field,
                _destroy: '1',
                field_name: '',
              });
            },
          },
        };
      })}
      addMoreButtonProps={{
        children: t('add_product_attribute'),
        onClick: () => {
          const productAttributeCounters = (
            formProps.formState.defaultValues?.woocommerceProductAttributes ||
            []
          )
            .concat(productAttribute)
            .map((o) => o.field_name.split('_')[1])
            .filter(Boolean);
          const maxCounter = Math.max(0, ...productAttributeCounters);

          const newCustomField = {
            field_mapping: undefined,
            field_name: `product_attribute_${
              (maxCounter > 0 ? maxCounter : productAttribute.length) + 1
            }`,
            _fieldName: `Product Attribute ${
              (maxCounter > 0 ? maxCounter : productAttribute.length) + 1
            }`,
            user_profile_id: +currentFeed.id,
            extra_attributes: syncFieldConstants['product_attribute'],
          };

          productAttributeProps.append(newCustomField);
        },
      }}
    />
  );
}

function Metadata({ maxDropdownItemDisplay, onSubmit }) {
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: { syncFieldConstants },
  } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { data: sampleData, loading } = useSampleData();
  const formProps = useFormContext();

  const metadataProps = useFieldArray({
    control: formProps.control,
    name: 'metafields',
  });
  // merge latest values from watch
  const metadata = metadataProps.fields.map((field, index) => ({
    ...field,
    ...formProps.watch('metafields')[index],
  }));
  console.log(`merged metadata:`, JSON.stringify(metadata, null, 2));

  return (
    <MappingSection
      title={t('metadata')}
      fields={metadata.map((field, index) => {
        return {
          field,
          name: `metafields.${index}.extra_attributes`,
          nameNode: (
            <TextField
              name={`metafields.${index}._fieldName`}
              control={formProps.control}
              label={t('metadata')}
              variant="standard"
              slotProps={{ htmlInput: { readOnly: true } }}
            />
          ),
          settingsNode: (
            <ExtraAttribute
              index={index}
              extraAttributeDrawer={extraAttributeDrawer}
              setExtraAttributeDrawer={setExtraAttributeDrawer}
              mappingTitle={field.field_name}
              onSubmit={formProps.handleSubmit(
                onSubmit({
                  onSuccess() {},
                  onError() {},
                  onSkip() {},
                })
              )}
            />
          ),
          settingsOnClick: () => setExtraAttributeDrawer(field.field_name),
          mappingNode: (
            <>
              <MappingStack
                icon={
                  !field.is_locked && (
                    <QuickEditButtonAndDialog
                      name={`metafields.${index}.field_mapping`}
                    />
                  )
                }
              >
                <Stack sx={{ width: '100%' }}>
                  <Tags
                    placeholder={
                      currentFeed.ioMode === 'in'
                        ? t('mapping_placeholder')
                        : t('export_mapping_placeholder')
                    }
                    name={`metafields.${index}.field_mapping`}
                    control={formProps.control}
                    loading={loading}
                    disabled={formProps.watch(`metafields.${index}.is_locked`)}
                    whitelist={
                      currentFeed.ioMode === 'in'
                        ? sampleData?.map((data, index) => ({
                            value: data.key
                              ? currentFeed.hasHeader
                                ? compareRegexAsString(data.key)
                                : String(index + 1)
                              : '-',
                            text: data.value ? truncate(data.value, 120) : '-',
                          }))
                        : []
                    }
                    renderOption={(props, item) => (
                      <Box
                        {...props}
                        sx={{
                          pointerEvents:
                            props.value === '-' ? 'none' : 'initial',
                        }}
                      >
                        <Typography component="div" variant="body1">
                          {item.value}
                        </Typography>
                        <Typography
                          component="div"
                          variant="body2"
                          sx={{
                            wordBreak: 'break-all',
                            color: theme.palette.grey[300],
                          }}
                        >
                          {item.text}
                        </Typography>
                      </Box>
                    )}
                    settings={{
                      delimiters: commaNotInsideQuotes,
                      dropdown: {
                        enabled: 0,
                        maxItems: maxDropdownItemDisplay,
                        caseSensitive: true,
                        placeAbove: false,
                      },
                    }}
                    helperTextProps={{ display: 'inline' }}
                    helperText={
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.invalid &&
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.error?.message !== 'mapping_field_index_only' && (
                        <Link to="">
                          <Button
                            variant="text"
                            size="small"
                            sx={{ padding: 0, marginLeft: '8px' }}
                            startIcon={
                              <Icon type="default" icon={faCircleQuestion} />
                            }
                          />
                        </Link>
                      )
                    }
                  />
                </Stack>
              </MappingStack>
            </>
          ),
          deleteProps: {
            canDelete: true,
            onDelete: () => {
              // useFieldArray.update auto rerenders for us
              metadataProps.update(index, {
                ...field,
                _destroy: '1',
                field_name: '',
              });
            },
          },
        };
      })}
      addMoreButtonProps={{
        children: t('add_metadata'),
        onClick: () => {
          const metadataCounters = (
            formProps.formState.defaultValues?.metafields || []
          )
            .concat(metadata)
            .map((o) => o.field_name.split('_')[1])
            .filter(Boolean);
          const maxCounter = Math.max(0, ...metadataCounters);

          const newCustomField = {
            field_mapping: undefined,
            field_name: `metadata_${
              (maxCounter > 0 ? maxCounter : metadata.length) + 1
            }`,
            _fieldName: `Custom Field ${
              (maxCounter > 0 ? maxCounter : metadata.length) + 1
            }`,
            user_profile_id: +currentFeed.id,
            extra_attributes: syncFieldConstants['metadata'],
          };

          metadataProps.append(newCustomField);
        },
      }}
    />
  );
}

function AdditionalInfoSection({ maxDropdownItemDisplay, onSubmit }) {
  const [extraAttributeDrawer, setExtraAttributeDrawer] = React.useState('');
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: { syncFieldConstants },
  } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { data: sampleData, loading } = useSampleData();
  const formProps = useFormContext();

  const additionalInfoSectionProps = useFieldArray({
    control: formProps.control,
    name: 'metafields',
  });
  // merge latest values from watch
  const additionalInfoSection = additionalInfoSectionProps.fields.map(
    (field, index) => ({
      ...field,
      ...formProps.watch('metafields')[index],
    })
  );
  console.log(
    `merged additionalInfoSection:`,
    JSON.stringify(additionalInfoSection, null, 2)
  );

  return (
    <MappingSection
      title={t('additional_info_section')}
      fields={additionalInfoSection.map((field, index) => {
        return {
          field,
          name: `metafields.${index}.extra_attributes`,
          nameNode: (
            <TextField
              name={`metafields.${index}._fieldName`}
              control={formProps.control}
              label={t('additional_info_section')}
              variant="standard"
              slotProps={{ htmlInput: { readOnly: true } }}
            />
          ),
          settingsNode: (
            <ExtraAttribute
              index={index}
              extraAttributeDrawer={extraAttributeDrawer}
              setExtraAttributeDrawer={setExtraAttributeDrawer}
              mappingTitle={field.field_name}
              onSubmit={formProps.handleSubmit(
                onSubmit({
                  onSuccess() {},
                  onError() {},
                  onSkip() {},
                })
              )}
            />
          ),
          settingsOnClick: () => setExtraAttributeDrawer(field.field_name),
          mappingNode: (
            <>
              <MappingStack
                icon={
                  !field.is_locked && (
                    <QuickEditButtonAndDialog
                      name={`metafields.${index}.field_mapping`}
                    />
                  )
                }
              >
                <Stack sx={{ width: '100%' }}>
                  <Tags
                    placeholder={
                      currentFeed.ioMode === 'in'
                        ? t('mapping_placeholder')
                        : t('export_mapping_placeholder')
                    }
                    name={`metafields.${index}.field_mapping`}
                    control={formProps.control}
                    loading={loading}
                    disabled={formProps.watch(`metafields.${index}.is_locked`)}
                    whitelist={
                      currentFeed.ioMode === 'in'
                        ? sampleData?.map((data, index) => ({
                            value: data.key
                              ? currentFeed.hasHeader
                                ? compareRegexAsString(data.key)
                                : String(index + 1)
                              : '-',
                            text: data.value ? truncate(data.value, 120) : '-',
                          }))
                        : []
                    }
                    renderOption={(props, item) => (
                      <Box
                        {...props}
                        sx={{
                          pointerEvents:
                            props.value === '-' ? 'none' : 'initial',
                        }}
                      >
                        <Typography component="div" variant="body1">
                          {item.value}
                        </Typography>
                        <Typography
                          component="div"
                          variant="body2"
                          sx={{
                            wordBreak: 'break-all',
                            color: theme.palette.grey[300],
                          }}
                        >
                          {item.text}
                        </Typography>
                      </Box>
                    )}
                    settings={{
                      delimiters: commaNotInsideQuotes,
                      dropdown: {
                        enabled: 0,
                        maxItems: maxDropdownItemDisplay,
                        caseSensitive: true,
                        placeAbove: false,
                      },
                    }}
                    helperTextProps={{ display: 'inline' }}
                    helperText={
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.invalid &&
                      formProps.getFieldState?.(
                        `metafields.${index}.field_mapping`
                      )?.error?.message !== 'mapping_field_index_only' && (
                        <Link to="">
                          <Button
                            variant="text"
                            size="small"
                            sx={{ padding: 0, marginLeft: '8px' }}
                            startIcon={
                              <Icon type="default" icon={faCircleQuestion} />
                            }
                          />
                        </Link>
                      )
                    }
                  />
                </Stack>
              </MappingStack>
            </>
          ),
          deleteProps: {
            canDelete: true,
            onDelete: () => {
              // useFieldArray.update auto rerenders for us
              additionalInfoSectionProps.update(index, {
                ...field,
                _destroy: '1',
                field_name: '',
              });
            },
          },
        };
      })}
      addMoreButtonProps={{
        children: t('add_additional_info_section'),
        onClick: () => {
          const additionalInfoSectionCounters = (
            formProps.formState.defaultValues?.metafields || []
          )
            .concat(additionalInfoSection)
            .map((o) => o.field_name.split('_')[1])
            .filter(Boolean);
          console.log(
            'additionalInfoSectionCounters',
            additionalInfoSectionCounters
          );
          const maxCounter = Math.max(0, ...additionalInfoSectionCounters);

          const newCustomField = {
            field_mapping: undefined,
            field_name: `additional_info_section_${
              (maxCounter > 0 ? maxCounter : additionalInfoSection.length) + 1
            }`,
            _fieldName: `Additional Info Section ${
              (maxCounter > 0 ? maxCounter : additionalInfoSection.length) + 1
            }`,
            user_profile_id: +currentFeed.id,
            extra_attributes: syncFieldConstants['additional_info_section'],
          };

          additionalInfoSectionProps.append(newCustomField);
        },
      }}
    />
  );
}
interface MappingStackProps {
  icon?: React.ReactNode;
  children: React.ReactNode;
  sx?: StackProps['sx'];
}

function MappingStack({ icon, children, sx, ...props }: MappingStackProps) {
  return (
    <Stack
      direction="row"
      spacing="5px"
      sx={{
        alignItems: 'flex-start',
        paddingTop: '13px',
        paddingLeft: icon
          ? { xs: '5px', sm: '0px' }
          : { xs: '50px', sm: '46px' },
        ...sx,
      }}
      {...props}
    >
      {icon && <Box sx={{ marginTop: '8px' }}>{icon}</Box>}
      {children}
    </Stack>
  );
}

function ResetButtonAndDialog() {
  const { t } = useTranslation();
  const { currentFeed } = useCurrentFeed();
  const {
    reset,
    formState: { defaultValues },
  } = useFormContext();
  const [resetMappingDialog, setResetMappingDialog] = React.useState(false);
  const alert = useAlert();
  const [resetToTemplateState, resetToTemplate] = useMutation(ResetToTemplate, {
    dataKey: 'resetToTemplate',
  });

  const toggleResetMappingDialog = () =>
    setResetMappingDialog(!resetMappingDialog);

  const resetTemplateFeedMapping = () =>
    resetToTemplate(
      { feedId: +currentFeed.id },
      {
        onSuccess({ data: feed, enqueueSnackbar }) {
          reset(defaultValues);
          enqueueSnackbar(`${feed.profileName} reset`, {
            variant: 'success',
          });
        },
        onError({ error }) {
          alert.setMessage(error.toString());
        },
      }
    ).finally(() => toggleResetMappingDialog());

  return (
    <>
      <Button
        danger
        variant="text"
        size="small"
        startIcon={<Icon type="kit" className="fak fa-resetmapping1" />}
        onClick={toggleResetMappingDialog}
        sx={{ paddingLeft: 0, justifyContent: 'left' }}
      >
        {t('reset')}
      </Button>
      <Dialog
        maxWidth="xs"
        open={resetMappingDialog}
        onClose={toggleResetMappingDialog}
      >
        <Dialog.Title>{t('reset_mapping')}</Dialog.Title>
        <Dialog.Content>{t('reset_settings_to_original')}</Dialog.Content>
        <Dialog.Actions>
          <Button
            variant="outlined"
            size="small"
            onClick={toggleResetMappingDialog}
          >
            {t('cancel')}
          </Button>
          <Button
            variant="contained"
            size="small"
            loading={resetToTemplateState.fetching}
            onClick={() => resetTemplateFeedMapping()}
          >
            {t('yes')}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </>
  );
}

function QuickEditButtonAndDialog({ name }) {
  const theme = useTheme();
  const { getValues, setValue } = useFormContext();
  const [open, setOpen] = React.useState(false);
  return (
    <>
      <IconButton
        onClick={() => setOpen(true)}
        sx={{ color: theme.palette.grey[300] }}
        id="quick-edit-modal"
      >
        <Icon
          type="default"
          icon={faArrowUpRightAndArrowDownLeftFromCenter}
          fontSize={16}
        />
      </IconButton>
      <QuickEditDialog
        value={getValues(name)}
        open={open}
        onClose={() => setOpen(false)}
        onSubmit={(value) => setValue(name, value)}
      />
    </>
  );
}

const mandatoryFields = {
  fields: ['product_title', 'price'],
  attributes: (feed) => {
    return [
      {
        field_mapping: '',
        field_name: 'product_title',
        user_profile_id: feed.id,
        extra_attributes: {
          case_convert: undefined,
          ignore_words: '',
          auto_ignore_option_words: false,
          find: '',
          replace: '',
          title_separator: ' - ',
          original_language: 'no change',
          returned_language: 'no change',
        },
      },
      {
        field_mapping: '',
        field_name: 'price',
        user_profile_id: feed.id,
        extra_attributes: {
          price_delimiter: 'auto',
          price_round: 3,
          pricing_conditions: [{ condition: 'any', formula: '*1' }],
          restrict_conditions: [],
          default_currency: 'USD',
          new_currency: undefined,
          currency_converter: false,
          static_flag: false,
        },
      },
    ] satisfies Array<FieldAttribute>;
  },
};

const bigcommerceMandatoryFields = {
  fields: ['product_title', 'price', 'weight'],
  attributes: (feed) => {
    return [
      {
        field_mapping: '',
        field_name: 'product_title',
        user_profile_id: feed.id,
        extra_attributes: {
          case_convert: undefined,
          ignore_words: '',
          auto_ignore_option_words: false,
          find: '',
          replace: '',
          title_separator: ' - ',
          original_language: 'no change',
          returned_language: 'no change',
        },
      },
      {
        field_mapping: '',
        field_name: 'price',
        user_profile_id: feed.id,
        extra_attributes: {
          price_delimiter: 'auto',
          price_round: 3,
          pricing_conditions: [{ condition: 'any', formula: '*1' }],
          restrict_conditions: [],
          default_currency: 'USD',
          new_currency: undefined,
          currency_converter: false,
          static_flag: false,
        },
      },
      {
        field_mapping: '',
        field_name: 'weight',
        user_profile_id: feed.id,
        extra_attributes: {
          weight_unit: undefined,
          weight_formula: undefined,
          static_flag: false,
          weight_delimiter: 'auto',
        },
      },
    ] satisfies Array<FieldAttribute>;
  },
};

const ekmMandatoryFields = {
  fields: ['product_title'],
  attributes: (feed) => {
    return [
      {
        field_mapping: '',
        field_name: 'product_title',
        user_profile_id: feed.id,
        extra_attributes: {
          case_convert: undefined,
          ignore_words: '',
          auto_ignore_option_words: false,
          find: '',
          replace: '',
          title_separator: ' - ',
          original_language: 'no change',
          returned_language: 'no change',
        },
      },
    ] satisfies Array<FieldAttribute>;
  },
};

const nonMandatoryFields = {
  // TODO: fields ia never used? remove?
  fields: [],
  attributes: () => {
    return [] satisfies Array<FieldAttribute>;
  },
};

interface FieldExtraAttributes
  extends Pick<
    SyncFieldSetting['extra_attributes'],
    | 'add_to_init_quantity'
    | 'auto_ignore_option_words'
    | 'case_convert'
    | 'col_sep'
    | 'currency_converter'
    | 'default_currency'
    | 'find'
    | 'force_override'
    | 'ignore_words'
    | 'new_currency'
    | 'only_deduct_quantity'
    | 'original_language'
    | 'price_delimiter'
    | 'price_round'
    | 'pricing_conditions'
    | 'replace'
    | 'restrict_conditions'
    | 'returned_language'
    | 'title_separator'
    | 'url_unescape'
    | 'variant_image_fallback'
    | 'weight_delimiter'
    | 'weight_formula'
    | 'weight_unit'
  > {
  static_flag?: SyncFieldSetting['extra_attributes']['static_flag'];
}

interface FieldAttribute
  extends Pick<
    SyncFieldSetting,
    'field_mapping' | 'field_name' | 'user_profile_id'
  > {
  extra_attributes: FieldExtraAttributes;
  id?: SyncFieldSetting['id'];
  is_locked?: SyncFieldSetting['is_locked'];
}

type FieldAttributes = (props) => Array<FieldAttribute>;

const mandatoryImportMappings: Record<Store['provider'], FieldAttributes> = {
  shopify: mandatoryFields.attributes,
  wix: mandatoryFields.attributes,
  woocommerce: mandatoryFields.attributes,
  bigcommerce: bigcommerceMandatoryFields.attributes,
  ekm: ekmMandatoryFields.attributes,
  squarespace: nonMandatoryFields.attributes,
  quickbooks: nonMandatoryFields.attributes,
  square: mandatoryFields.attributes,
  prestashop: mandatoryFields.attributes,
};
const staticValueBasePlatform: Record<
  Store['provider'],
  Record<UserProfile['humanizeFeedType'], string[]>
> = {
  shopify: {
    update: [
      'barcode',
      'product_type',
      'template_suffix',
      'country_code_of_origin',
      'fulfillment_service',
      'compare_price_at',
      'metafield',
      'policy',
      'tags',
      'weight',
      'taxable',
      'harmonized_system_code',
      'vendor',
      'category',
      'status',
      'categories',
      'mpn',
      'bin_picking_number',
      'standardized_product_type',
      'requires_shipping',
      'published',
      'track_quantity',
      'tax_code',
      'metafields_global_description_tag',
    ],
    remove: [],
    import: [
      'barcode',
      'product_type',
      'template_suffix',
      'country_code_of_origin',
      'fulfillment_service',
      'compare_price_at',
      'metafield',
      'policy',
      'tags',
      'weight',
      'taxable',
      'harmonized_system_code',
      'vendor',
      'category',
      'status',
      'categories',
      'mpn',
      'bin_picking_number',
      'standardized_product_type',
      'requires_shipping',
      'price',
      'published',
      'track_quantity',
      'metafields_global_description_tag',
    ],
    export: [
      'barcode',
      'product_type',
      'template_suffix',
      'country_code_of_origin',
      'fulfillment_service',
      'compare_price_at',
      'metafield',
      'policy',
      'tags',
      'taxable',
      'harmonized_system_code',
      'vendor',
      'category',
      'status',
      'categories',
      'mpn',
      'bin_picking_number',
      'standardized_product_type',
      'requires_shipping',
      'published',
      'track_quantity',
    ],
  },
  wix: {
    update: ['brand', 'visible'],
    remove: [],
    import: ['price', 'brand', 'category', 'visible'],
    export: ['brand'],
  },
  woocommerce: {
    update: [
      'stock_status',
      'categories',
      'tags',
      'weight',
      'length',
      'width',
      'height',
      'brand',
    ],
    remove: [],
    import: [
      'price',
      'categories',
      'tags',
      'weight',
      'length',
      'width',
      'height',
      'brand',
    ],
    export: [
      'stock_status',
      'categories',
      'tags',
      'weight',
      'length',
      'width',
      'height',
      'brand',
    ],
  },
  bigcommerce: {
    update: [
      'weight',
      'barcode',
      'brand',
      'mpn',
      'bin_picking_number',
      'category',
      'visible',
      'availability_description',
      'purchase_availability',
      'condition',
      'show_condition',
      'gtin',
      'search_keywords',
    ],
    remove: [],
    import: [
      'price',
      'weight',
      'width',
      'height',
      'barcode',
      'bin_picking_number',
      'category',
      'visible',
      'mpn',
      'brand',
      'condition',
      'show_condition',
      'gtin',
      'search_keywords',
    ],
    export: [
      'mpn',
      'bin_picking_number',
      'category',
      'brand',
      'visible',
      'weight',
      'barcode',
      'condition',
      'show_condition',
      'gtin',
      'search_keywords',
    ],
  },
  squarespace: {
    update: ['weight'],
    remove: [],
    import: [],
    export: [],
  },
  quickbooks: {
    update: [],
    remove: [],
    import: [],
    export: [],
  },
  ekm: {
    update: ['weight'],
    remove: [],
    import: ['price', 'weight'],
    export: [],
  },
  square: {
    update: [],
    remove: [],
    import: [],
    export: [],
  },
  prestashop: {
    update: [],
    remove: [],
    import: [],
    export: [],
  },
};

const productOptionsVariationsDisplayBasePlatform: Record<
  Store['provider'],
  string[]
> = {
  shopify: ['update', 'import', 'export'],
  wix: ['import', 'update'],
  woocommerce: ['import'],
  bigcommerce: ['import'],
  squarespace: [''],
  quickbooks: [''],
  ekm: [''],
  square: [''],
  prestashop: ['import'],
};

const filterFromSyncFieldSettings = [
  'product_id',
  'variant_group',
  'option1',
  'option2',
  'option3',
];

const onlySelectionForSyncFieldSettings = [
  'policy',
  'status',
  'taxable',
  'requires_shipping',
  'published',
  'visible',
];

const dropdownListByMapping: Record<string, Array<{ value: string }>> = {
  policy: [{ value: 'continue' }, { value: 'deny' }],
  status: [{ value: 'active' }, { value: 'draft' }, { value: 'archived' }],
  taxable: [{ value: 'true' }, { value: 'false' }],
  requires_shipping: [{ value: 'true' }, { value: 'false' }],
  published: [{ value: 'publish' }, { value: 'unpublish' }],
  visible: [{ value: 'true' }, { value: 'false' }],
};
