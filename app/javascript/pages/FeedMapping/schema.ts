import { z } from 'zod';

import { userProfileSchema, type UserProfile } from '@/types';

export const formSchema = (currentFeed: UserProfile) =>
  userProfileSchema.pick({ productIdentifierSyncField: true }).and(
    z.object({
      productIdentifierSyncField: z.object({
        _destroy: z.undefined(),
        field_mapping: z
          .string()
          .min(
            1,
            currentFeed.humanizeFeedType === 'export'
              ? 'mapping_field_index_only'
              : 'mapping_field'
          ),
      }),
    })
  );

// TODO:
// export type FeedMappingFormValues = z.infer<ReturnType<typeof formSchema>>;
// const fv: FeedMappingFormValues = {
//   productIdentifierSyncField: {},
// };
