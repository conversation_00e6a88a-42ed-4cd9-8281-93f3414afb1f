import { Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { CustomAppBar } from '@/components/CustomAppBar';
import { useIsEmbeddedApp } from '@/hooks/useIsEmbeddedApp';
import * as routes from '@/routes';

export function Feedback() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isEmbeddedApp } = useIsEmbeddedApp();

  return (
    <>
      <CustomAppBar
        title={t('feedback')}
        redirectUrl={isEmbeddedApp ? '' : routes.home}
      />
      <Card>
        <Typography variant="h5" sx={{ fontWeight: 500, marginBottom: '8px' }}>
          {t('feedback')}
        </Typography>
        <Typography
          variant="body1"
          sx={{
            color: theme.palette.grey[300],
            marginBottom: '16px',
          }}
        >
          {t('help_us_improve')}
        </Typography>

        <Button
          size="small"
          onClick={() =>
            window.open('https://forms.gle/miST4AU2BgBCJcRdA', '_blank')
          }
        >
          {t('share_my_thought_here')}
        </Button>
      </Card>
    </>
  );
}
