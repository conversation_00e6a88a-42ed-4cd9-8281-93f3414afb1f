module ProductLogHelper
  def revert_link(resource, html_options = {})
    feed_type = resource.user_profile&.feed_type&.titleize || "Process"

    return unless can_revert?(resource)

    if resource.undo_at
      "Reverted #{time_ago_in_words(resource.undo_at)} (#{link_to "Force again",
        revert_process_admin_product_log_url(resource),
        data: {confirm: "Are you very very sure?"}})".html_safe
    else
      link_to "Revert #{feed_type}",
        revert_process_admin_product_log_url(resource),
        html_options.merge(data: {confirm: "Are you sure?"})
    end
  end

  private

  def can_revert?(resource)
    provider = resource.user_profile&.user&.provider
    feed_type = resource.user_profile&.feed_type

    if provider == "shopify"
      ["import", "remove"].include?(feed_type)
    else
      feed_type == "import"
    end
  end
end
