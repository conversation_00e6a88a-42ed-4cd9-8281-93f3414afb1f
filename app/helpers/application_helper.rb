module ApplicationHelper
  def react_json(hash, opts = {})
    JSON.pretty_generate(hash.map { |k, v| {key: k, value: v} })
  end

  def schedule_job_types
    h = Settings.job_type.to_hash
    h.delete(:on_file_uploaded)
    h.delete(:on_email_received)
    h
  end

  def cache_arbre(context, *args)
    if controller.perform_caching
      opts = args.extract_options!.merge namespace: :arbre
      Rails.cache.fetch(*args, opts) { yield.to_s }
    else
      yield
    end
  end

  # def unresolved_tickets_count
  #   context = {}
  #   query_string = "{ unresolvedConversationsCount }"
  #   result = ::CloudcoderShopifySchema.execute(query_string, context: context)
  #   result["data"]["unresolvedConversationsCount"]
  # end
end
