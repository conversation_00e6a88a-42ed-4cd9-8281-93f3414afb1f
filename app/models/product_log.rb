class ProductLog < ApplicationRecord
  serialize :remark_values, coder: <PERSON><PERSON><PERSON>, default: {}

  has_one :webhook_log
  belongs_to :user_profile, -> { with_deleted }
  delegate :user, to: :user_profile, allow_nil: true
  scope :invalid, -> { where("product_logs.status = false") }
  scope :today, -> { where(created_at: Time.now.beginning_of_day..Time.now.end_of_day) }

  def percentage_matched
    if (total_store_skus || 0) > 0
      ((number_product_updated.to_f / total_store_skus.to_f).to_f * 100.to_f).to_f.round(2)
    else
      0
    end
  end

  def queuing_time
    if start_time && queuing_at
      ((start_time - queuing_at) / (Time.now - 3.minutes.ago) / 1.minute).round(1)
    else
      -1
    end
  end

  def pending_number_updated
    if pending_number_product_updated.nil? || pending_number_product_updated == 0
      count = user_stocks.where(product_log_id: id).count
      update(pending_number_product_updated: count)
    end
    pending_number_product_updated
  end

  def after_destroy
    FileUtils.rm_r "#{Rails.root}/public/system/user_profiles/#{user_profile_id}_#{log.id}"
    puts "deleted #{Rails.root}/public/system/user_profiles/#{user_profile_id}_#{log.id}"
  rescue
    # Airbrake.notify(e, message: "#{id} #{e.message}")

    # remove S3 file
  end

  after_create :download_link
  def download_link
    unless download_id
      self.download_id = id
      save
    end
  end

  def total_out_of_stock
    if out_of_stock_ids && out_of_stock_ids.length > 0
      return out_of_stock_ids.split(",").count
    end

    number_out_of_stocks
  end

  def user_stocks
    if user
      user.user_stocks.where(product_log_id: id)
    else
      User.find(user_id).user_stocks.where(product_log_id: id)
    end
  end

  def read_logs(file_name)
    File.new("#{Rails.root}/public/#{log_download_base}/#{file_name}")
  end

  def s3_download_base(file_name)
    "product_logs/user_profiles/#{user_profile_id}_#{download_id}/#{file_name}"
  end

  def log_download_base
    "system/user_profiles/#{user_profile_id}_#{download_id}"
  end

  def get_previous_success_product_log
    @previous_success_product_log ||= user_profile.product_logs
      .where("status = true AND created_at < ?", created_at)
      .order(created_at: :desc)
      .first
  end

  def revert_job_class
    case user_profile.feed_type
    when "import"
      RevertImportJob
    when "remove"
      RevertRemoveJob
    else
      raise ArgumentError, "Unknown feed type: #{user_profile.feed_type}"
    end
  end

  def revert_job_queue
    "web_import_revert"
  end

  def create_revert_job
    job_class = revert_job_class
    Delayed::Job.enqueue(
      job_class.new(id, user_profile_id),
      queue: revert_job_queue
    )
  end
end
