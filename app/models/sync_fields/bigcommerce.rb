module SyncFields
  class Bigcommerce < Shopify
    def self.all_attributes
      @all_attributes ||= {
        quantity: [:add_to_init_quantity, :only_deduct_quantity, :rules_json, :sum_quantities, :skip_before_rules, :quantity_delimiter, :deduct_quantity_from_column, :expand_columns, :column_mapping, :create_location],
        price: [:price_delimiter, :price_round, :pricing_conditions, :restrict_conditions, :default_currency, :new_currency, :currency_converter, :static_flag],
        barcode: [:static_value, :static_flag, :expand_columns],
        sku: [],
        weight: [:weight_unit, :weight_formula, :static_flag, :weight_delimiter],
        width: [:static_flag],
        height: [:static_flag],
        depth: [],
        cost: [:price_delimiter, :price_round, :cost_pricing_conditions, :cost_restrict_conditions, :default_currency, :new_currency, :currency_converter],
        sale_price: [:price_delimiter, :price_round, :pricing_conditions, :restrict_conditions, :default_currency, :new_currency, :currency_converter],
        retail_price: [:price_delimiter, :price_round, :pricing_conditions, :restrict_conditions, :default_currency, :new_currency, :currency_converter],
        images: [:force_override, :url_prefix, :alt_mapping, :alt_col_sep, :clear_current_images, :col_sep, :default_image_url, :url_unescape, :image_cdn],
        shipping_cost: [],
        free_shipping: [:with_free_shipping_value, :without_free_shipping_value],
        mpn: [:static_flag],
        bin_picking_number: [:static_flag],
        option1: [:option1_name, :case_convert, :dynamic_option_name, :expand_columns, :expand_col_sep, :find, :replace],
        option2: [:option2_name, :case_convert, :dynamic_option_name, :expand_columns, :expand_col_sep, :find, :replace],
        option3: [:option3_name, :case_convert, :dynamic_option_name, :expand_columns, :expand_col_sep, :find, :replace],
        product_title: [:case_convert, :ignore_words, :auto_ignore_option_words, :find, :replace, :title_separator],
        custom_field: [:custom_field_key, :create_key_if_not_found],
        body_html: [:case_convert, :wrap_tag, :labels, :find, :replace, :convert_line_break, :force_override_description, :skip_zero_blank_labels, :update_only_if_nil],
        brand: [:static_flag, :find, :replace],
        category: [:static_flag, :find, :override_categories, :replace, :parent_category_id],
        availability_description: [:static_flag],
        product_sku: [],
        visible: [:is_visible_value, :is_not_visible_value, :static_value, :static_flag],
        preorder_message: [],
        preorder_release_date: [:remove_preoder_on_date, :date_format, :set_date_if_zero_qty, :custom_preorder_message],
        product_id: [],
        purchase_availability: [:static_flag, :purchase_available_flag, :purchase_disabled_flag, :purchase_preorder_flag],
        condition: [:static_flag, :skip_if_blank, :condition_is_new, :condition_is_used, :condition_is_refurbished],
        show_condition: [:static_flag, :enable_condition, :disable_condition],
        track_quantity: [:eq_enable_value, :eq_disable_value, :skip_if_blank],
        gtin: [:static_flag],
        search_keywords: [:static_flag],
        page_title: [:case_convert, :ignore_words, :auto_ignore_option_words, :find, :replace, :title_separator],
        warranty: [:case_convert, :wrap_tag, :labels, :find, :replace, :convert_line_break, :force_override_description, :skip_zero_blank_labels, :update_only_if_nil],
        meta_description: [:case_convert, :wrap_tag, :labels, :find, :replace, :convert_line_break, :force_override_description, :skip_zero_blank_labels, :update_only_if_nil]
      }
    end
  end
end
