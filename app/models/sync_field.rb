class SyncField < ApplicationRecord
  belongs_to :user_profile, counter_cache: true

  audited(
    associated_with: :user_profile,
    max_audits: 20,
    if: :is_need_audit?
  )

  serialize :extra_attributes, coder: JSON, default: {}

  validates :field_name, uniqueness: {scope: :user_profile_id}

  before_save :handle_quantity_match_and_delete, if: -> { field_name == "quantity" }
  before_save :set_audited_username
  before_save :parse_extra_attributes

  def is_need_audit?
    changes = audited_changes
    if changes == {}
      # Only update and destroy action will have value on audited_changes
      # Create action will have empty {}
      return true
    end

    # Check only for update and destory action
    if !(changes.keys - ["extra_attributes"]).empty?
      # If there is field(s) other than extra_attributes, return true
      return true
    end

    extra_attr_ignore_list = ["auto_delimiter", "auto_separator"]

    old_v = changes["extra_attributes"][0]
    new_v = changes["extra_attributes"][1]
    keys = (old_v.keys + new_v.keys).uniq

    is_need_audit = false
    keys.each do |key|
      next if extra_attr_ignore_list.include?(key)
      if old_v[key] != new_v[key]
        is_need_audit = true
        break
      end
    end
    is_need_audit
  rescue => e
    Airbrake.notify(e, message: "Audited Needed Error")
    true
  end

  def parse_extra_attributes
    self.extra_attributes = eval(extra_attributes) if extra_attributes.is_a? String
  end

  def handle_quantity_match_and_delete
    return unless extra_attributes_changed? &&
      extra_attributes_change.first["reset_init_quantity"] !=
        extra_attributes_change.last["reset_init_quantity"]

    reset_init_quantity = extra_attributes["reset_init_quantity"]

    if reset_init_quantity == true
      user_profile.update_column(:match_and_delete, false)
    else
      user_profile.update_column(:match_and_delete, true)
    end
  end

  def self.variant_key?(key)
    @variant_keys ||= variant_fields.keys
    @variant_keys.include?(key.to_sym)
  end

  def self.product_key?(key)
    @product_keys ||= product_fields.keys
    @product_keys.include?(key.to_sym)
  end

  def self.variant_fields
    @variant_fields ||= {
      quantity: {shopify_key: :inventory_quantity, data_type: "Integer"},
      price: {shopify_key: :price, data_type: "Float"},
      compare_price_at: {shopify_key: :compare_at_price, data_type: "FloatAllowNull"},
      barcode: {shopify_key: :barcode, data_type: "String"},
      policy: {shopify_key: :inventory_policy, data_type: "String"},
      weight: {shopify_key: :weight, data_type: "Float"},
      # :weight_unit      => { shopify_key: :weight_unit, data_type: "String", class_name: StaticTextField },
      sku: {shopify_key: :sku, data_type: "String"},
      title: {shopify_key: :title, data_type: "String"},
      option1: {shopify_key: :option1, data_type: "String", mandatory: false},
      option2: {shopify_key: :option2, data_type: "String", mandatory: false},
      option3: {shopify_key: :option3, data_type: "String", mandatory: false},
      taxable: {shopify_key: :taxable, data_type: "Boolean"},
      tax_code: {shopify_key: :tax_code, data_type: "String"},
      metafields: {shopify_key: :metafields, data_type: "Metafield", class_name: nil},
      fulfillment_service: {shopify_key: :fulfillment_service, data_type: "String"},
      cost: {shopify_key: :cost, data_type: "Float"},
      inventory_management: {shopify_key: :inventory_management, data_type: "String"},
      requires_shipping: {shopify_key: :requires_shipping, data_type: "Boolean"},
      track_quantity: {shopify_key: :track_quantity, data_type: "Boolean"},
      shopify_variant_id: {shopify_key: :id, data_type: "Integer"},
      variant_created_at: {shopify_key: :created_at, data_type: "Date"}
    }
  end

  def self.product_fields
    @product_fields ||= {
      handle: {shopify_key: :handle, data_type: "String"},
      tags: {shopify_key: :tags, data_type: "Tags"},
      published: {shopify_key: :published_at, data_type: "Exist"},
      product_title: {shopify_key: :title, data_type: "String", mandatory: true},
      body_html: {shopify_key: :body_html, data_type: "Html"},
      metafield: {shopify_key: :metafields, data_type: "Metafield", class_name: nil},
      vendor: {shopify_key: :vendor, data_type: "String"},
      product_type: {shopify_key: :product_type, data_type: "String"},
      images: {shopify_key: :images, data_type: "Array"},
      # :options        => { shopify_key: :options, data_type: "String", class_name: ProductOptionsField },
      metafields_global_title_tag: {shopify_key: :metafields_global_title_tag, data_type: "OnlyExist"},
      metafields_global_description_tag: {shopify_key: :metafields_global_description_tag, data_type: "OnlyExist"},
      collection: {shopify_key: :collections, data_type: "Collection"},
      template_suffix: {shopify_key: :template_suffix, data_type: "String"},
      status: {shopify_key: :status, data_type: "String"},
      shopify_product_id: {shopify_key: :id, data_type: "Integer"},
      product_created_at: {shopify_key: :created_at, data_type: "Date"}
    }
  end

  def self.not_updatable
    [:variant_group, :sku, :collection]
  end

  # update type attributes
  def self.extra_attributes
    @extra_attributes ||= all_attributes.except(*not_updatable)
  end

  def self.all_attributes
    @all_attributes ||= {
      product_id: [],
      quantity: [:add_to_init_quantity, :only_deduct_quantity, :rules_json, :quantity_delimiter, :update_only_when_zero_qty, :sum_quantities, :deduct_quantity_from_column, :expand_columns, :reset_init_quantity, :create_location, :skip_if_blank, :quantity_use_on_hand, :skip_before_rules],
      product_title: [:case_convert, :ignore_words, :auto_ignore_option_words, :find, :replace, :title_separator, :original_language, :returned_language, :deepseek_translation],
      price: [:price_round, :price_delimiter, :pricing_conditions, :restrict_conditions, :default_currency, :new_currency, :currency_converter, :static_flag, :column_mapping, :skip_if_blank],
      compare_price_at: [:price_round, :price_delimiter, :compare_at_price_formula, :compare_pricing_conditions, :force_override_compare_at_price, :fallback_to_price_if_less_than_price, :compare_at_pricing_conditions, :compare_price_at_restrict_conditions, :static_flag, :default_currency, :new_currency, :currency_converter, :column_mapping, :skip_if_blank],
      barcode: [:static_value, :static_flag, :expand_columns],
      published: [:eq_hide_value, :eq_show_value, :override_publish, :static_flag, :skip_if_blank],
      tags: [:override_tags, :field_prefix, :add_tags, :col_sep, :static_flag, :ignore_tags, :only_tags, :find, :replace, :skip_if_blank],
      weight: [:weight_unit, :weight_formula, :static_flag, :weight_delimiter, :skip_if_blank],
      metafield: [:metafield_key, :metafield_namespace, :metafield_owner, :metafield_type, :static_flag, :blank_val, :metafield_date_format, :metafield_weight_unit, :metafield_volume_unit, :is_true_indicator, :is_false_indicator, :find, :replace, :metafield_dimension_unit, :skip_if_blank, :remove_if_blank, :original_language, :returned_language],
      policy: [:policy_deny_value, :policy_continue_value, :override_to_deny, :base_on_quantity, :static_flag],
      body_html: [:case_convert, :labels, :wrap_tag, :find, :replace, :convert_line_break, :force_override_description, :skip_zero_blank_labels, :update_only_if_nil, :original_language, :returned_language, :deepseek_translation],
      vendor: [:static_value, :static_flag, :ignore_words, :case_convert, :skip_if_blank],
      product_type: [:static_value, :static_flag],
      images: [:force_override, :url_prefix, :alt_mapping, :alt_col_sep, :col_sep, :clear_current_images, :ignore_clear_images_reset, :default_image_url, :use_pk_to_match, :url_unescape, :image_cdn, :skip_if_blank, :variant_image_fallback],
      metafields_global_title_tag: [:find, :replace],
      metafields_global_description_tag: [:find, :replace],
      variant_group: [:existing_product_identifier, :existing_product_tag],
      option1: [:case_convert, :option1_name, :dynamic_option_name, :expand_columns, :expand_col_sep, :find, :replace, :original_language, :returned_language],
      option2: [:case_convert, :option2_name, :dynamic_option_name, :expand_columns, :expand_col_sep, :find, :replace, :original_language, :returned_language],
      option3: [:case_convert, :option3_name, :dynamic_option_name, :expand_columns, :expand_col_sep, :find, :replace, :original_language, :returned_language],
      taxable: [:static_value, :static_flag, :taxable_flag, :not_taxable_flag, :skip_if_blank],
      tax_code: [:static_value, :static_flag, :skip_if_blank],
      handle: [],
      sku: [],
      collection: [:static_flag],
      fulfillment_service: [:static_flag],
      cost: [:price_round, :price_delimiter, :cost_pricing_conditions, :cost_restrict_conditions, :default_currency, :new_currency, :currency_converter, :skip_if_blank, :force_override_cost],
      inventory_management: [:static_value, :static_flag],
      requires_shipping: [:static_flag, :false_values, :skip_if_blank],
      harmonized_system_code: [:hs_code_suffix, :static_flag, :skip_if_blank],
      country_code_of_origin: [:static_flag],
      sale_price: [:price_round, :price_delimiter, :pricing_conditions, :restrict_conditions],
      retail_price: [:price_round, :price_delimiter, :pricing_conditions, :restrict_conditions],
      free_shipping: [:with_free_shipping_value, :without_free_shipping_value],
      preorder_release_date: [:date_format, :remove_preoder_on_date, :set_date_if_zero_qty, :custom_preorder_message],
      metadata: [:metadata_key, :create_key_if_not_found],
      custom_field: [:custom_field_key, :create_key_if_not_found],
      product_url: [],
      brand: [:static_flag],
      template_suffix: [:static_value, :static_flag],
      category: [:static_flag, :find, :replace, :parent_category_id, :override_categories, :original_language, :returned_language],
      discount_amount: [:discount_type],
      status: [:static_flag, :status_active_flag, :status_draft_flag, :status_archived_flag, :skip_if_blank],
      exported_at: [],
      categories: [:override_categories, :static_flag, :find, :replace, :category_name],
      standardized_product_type: [:language_code, :static_flag],
      mpn: [:static_flag],
      bin_picking_number: [:static_flag],
      visible: [:is_visible_value, :is_not_visible_value, :static_value, :static_flag],
      track_quantity: [:eq_enable_value, :eq_disable_value, :skip_if_blank],
      stock_status: [:static_value, :static_flag, :in_stock_flag, :out_stock_flag],
      purchase_availability: [:static_flag, :purchase_available_flag, :purchase_disabled_flag, :purchase_preorder_flag],
      availability_description: [:static_flag],
      product_attribute: [:custom_field_key, :create_key_if_not_found],
      quantity_quality_control: [:reset_init_quantity, :skip_if_blank, :deduct_quantity_from_column, :rules_json, :skip_before_rules],
      quantity_safety_stock: [:reset_init_quantity, :skip_if_blank, :deduct_quantity_from_column, :rules_json, :skip_before_rules],
      quantity_on_hand: [:reset_init_quantity, :skip_if_blank, :deduct_quantity_from_column, :rules_json, :skip_before_rules],
      quantity_damaged: [:reset_init_quantity, :skip_if_blank, :deduct_quantity_from_column, :rules_json, :skip_before_rules],
      backorders: [:disallow_backorder, :allow_but_notify, :allow_backorder, :static_flag],
      quantity_incoming: [:reset_init_quantity, :skip_if_blank, :deduct_quantity_from_column, :rules_json, :skip_before_rules],
      quantity_reserved: [:reset_init_quantity, :skip_if_blank, :deduct_quantity_from_column, :rules_json, :skip_before_rules],
      price_region: [:price_round, :price_delimiter, :pricing_conditions, :restrict_conditions, :default_currency, :static_flag, :column_mapping, :skip_if_blank, :additional_currencies],
      compare_price_at_region: [:price_round, :price_delimiter, :compare_at_price_formula, :compare_pricing_conditions, :force_override_compare_at_price, :fallback_to_price_if_less_than_price, :compare_at_pricing_conditions, :compare_price_at_restrict_conditions, :static_flag, :default_currency, :column_mapping, :skip_if_blank, :additional_currencies],
      width: [:static_flag],
      length: [:static_flag],
      height: [:static_flag],
      short_description: [:case_convert, :labels, :wrap_tag, :find, :replace, :convert_line_break, :force_override_description, :skip_zero_blank_labels, :update_only_if_nil, :original_language, :returned_language],
      condition: [:static_flag, :skip_if_blank, :condition_is_new, :condition_is_used, :condition_is_refurbished],
      show_condition: [:static_flag, :enable_condition, :disable_condition],
      additional_info_section: [:section_title, :create_key_if_not_found],
      gtin: [:static_flag],
      search_keywords: [:static_flag],
      page_title: [:case_convert, :ignore_words, :auto_ignore_option_words, :find, :replace, :title_separator],
      warranty: [:case_convert, :wrap_tag, :labels, :find, :replace, :convert_line_break, :force_override_description, :skip_zero_blank_labels, :update_only_if_nil],
      meta_description: [:case_convert, :labels, :find, :replace, :convert_line_break, :force_override_description, :skip_zero_blank_labels, :update_only_if_nil]
    }
  end

  def self.extra_attributes_with_defaut_values
    {price: {
       price_delimiter: "auto",
       price_round: 3,
       pricing_conditions: [{condition: "any", formula: "*1"}],
       restrict_conditions: [],
       default_currency: "USD",
       new_currency: "USD",
       currency_converter: false,
       static_flag: false,
       skip_if_blank: true
     },
     metafield: {
       metafield_key: nil,
       metafield_namespace: nil,
       metafield_owner: "product",
       metafield_type: "single_line_text_field",
       blank_val: "-",
       metafield_date_format: "auto",
       metafield_weight_unit: "auto",
       metafield_volume_unit: "ml",
       is_true_indicator: "true",
       is_false_indicator: "false",
       static_flag: false,
       find: "",
       replace: "",
       metafield_dimension_unit: "cm",
       skip_if_blank: true,
       remove_if_blank: false,
       original_language: "no change",
       returned_language: "no change"
     },
     product_title: {
       case_convert: nil,
       ignore_words: "",
       auto_ignore_option_words: false,
       find: "",
       replace: "",
       title_separator: " - ",
       original_language: "no change",
       returned_language: "no change",
       deepseek_translation: false
     },
     barcode: {
       static_value: nil,
       static_flag: false,
       expand_columns: false
     },
     quantity: {
       add_to_init_quantity: false,
       only_deduct_quantity: false,
       rules_json: [{operator: "<=", key: "0", value: "0"}],
       zero_qty: true,
       sum_quantities: true,
       reset_init_quantity: false,
       quantity_delimiter: "auto",
       deduct_quantity_from_column: nil,
       expand_columns: false,
       column_mapping: false,
       create_location: true,
       skip_if_blank: true,
       quantity_use_on_hand: false,
       only_update_stock_location: false,
       skip_before_rules: false
     },
     compare_price_at: {
       price_delimiter: "auto",
       fallback_to_price_if_less_than_price: false,
       force_override_compare_at_price: false,
       compare_at_pricing_conditions: [{condition: "any", formula: "*1"}],
       price_round: -100,
       compare_price_at_restrict_conditions: [],
       static_flag: false,
       default_currency: "USD",
       new_currency: "USD",
       currency_converter: false,
       column_mapping: false,
       skip_if_blank: true
     },
     published: {
       eq_hide_value: "false",
       eq_show_value: "true",
       override_publish: false,
       static_flag: false
     },
     tags: {
       add_tags: true,
       override_tags: false,
       field_prefix: nil,
       ignore_tags: nil,
       only_tags: nil,
       static_flag: false,
       find: "",
       replace: "",
       col_sep: "auto",
       skip_if_blank: true
     },
     weight: {
       weight_unit: "g",
       weight_formula: nil,
       static_flag: false,
       weight_delimiter: "auto",
       skip_if_blank: true
     },
     policy: {
       static_flag: false,
       base_on_quantity: false,
       override_to_deny: false,
       policy_deny_value: "deny",
       policy_continue_value: "continue"
     },
     vendor: {
       ignore_words: "",
       static_flag: false,
       case_convert: nil,
       find: "",
       replace: "",
       title_separator: " - ",
       skip_if_blank: true
     },
     product_type: {
       static_value: nil,
       static_flag: false
     },
     body_html: {
       case_convert: nil,
       wrap_tag: "p",
       labels: nil,
       find: "",
       replace: "",
       convert_line_break: true,
       force_override_description: false,
       skip_zero_blank_labels: false,
       update_only_if_nil: false,
       skip_if_blank: true,
       original_language: "no change",
       returned_language: "no change",
       deepseek_translation: false
     },
     images: {
       force_override: false,
       url_prefix: nil,
       alt_mapping: nil,
       alt_col_sep: nil,
       clear_current_images: false,
       col_sep: "auto",
       default_image_url: nil,
       url_unescape: false,
       image_cdn: false,
       skip_if_blank: true,
       variant_image_fallback: nil
     },
     video_links: {
       force_override: false,
       alt_mapping: nil,
       alt_col_sep: nil,
       col_sep: "auto",
       url_unescape: false,
       skip_if_blank: true
     },
     option1: {
       option1_name: "",
       case_convert: nil,
       dynamic_option_name: false,
       expand_columns: false,
       expand_col_sep: ",",
       find: "",
       replace: "",
       auto_remove_default_title: false,
       original_language: "no change",
       returned_language: "no change"
     },
     option2: {
       option2_name: "",
       case_convert: nil,
       dynamic_option_name: false,
       expand_columns: false,
       expand_col_sep: ",",
       find: "",
       replace: "",
       auto_remove_default_title: false,
       original_language: "no change",
       returned_language: "no change"
     },
     option3: {
       option3_name: "",
       case_convert: nil,
       dynamic_option_name: false,
       expand_columns: false,
       expand_col_sep: ",",
       find: "",
       replace: "",
       auto_remove_default_title: false,
       original_language: "no change",
       returned_language: "no change"
     },
     taxable: {
       static_value: nil,
       static_flag: false,
       taxable_flag: "true",
       not_taxable_flag: "false",
       skip_if_blank: true
     },
     tax_code: {
       static_value: nil,
       skip_if_blank: true
     },
     fulfillment_service: {
       static_value: nil,
       static_flag: false
     },
     cost: {
       price_delimiter: "auto",
       price_round: 3,
       cost_pricing_conditions: [{condition: "any", formula: "*1"}],
       cost_restrict_conditions: [],
       default_currency: "USD",
       new_currency: "USD",
       currency_converter: false,
       skip_if_blank: true,
       force_override_cost: false
     },
     requires_shipping: {
       static_flag: false,
       false_values: "",
       skip_if_blank: true
     },
     template_suffix: {
       static_value: nil,
       static_flag: false
     },
     variant_group: {
       existing_product_identifier: nil,
       existing_product_tag: ""
     },
     harmonized_system_code: {
       hs_code_suffix: nil,
       static_flag: false,
       skip_if_blank: true
     },
     country_code_of_origin: {
       static_flag: false
     },
     sale_price: {
       price_delimiter: "auto",
       price_round: 3,
       pricing_conditions: [{condition: "any", formula: "*1"}],
       restrict_conditions: [],
       default_currency: "USD",
       new_currency: "USD",
       currency_converter: false
     },
     retail_price: {
       price_delimiter: "auto",
       price_round: 3,
       pricing_conditions: [{condition: "any", formula: "*1"}],
       restrict_conditions: [],
       default_currency: "USD",
       new_currency: "USD",
       currency_converter: false
     },
     free_shipping: {
       with_free_shipping_value: nil,
       without_free_shipping_value: nil
     },
     preorder_release_date: {
       remove_preoder_on_date: true,
       date_format: "",
       set_date_if_zero_qty: false,
       custom_preorder_message: nil
     },
     metadata: {
       create_key_if_not_found: true,
       metadata_key: nil
     },
     custom_field: {
       custom_field_key: nil,
       create_key_if_not_found: false
     },
     brand: {
       static_flag: false,
       find: "",
       replace: ""
     },
     category: {
       static_flag: false,
       find: "",
       override_categories: false,
       replace: "",
       parent_category_id: "",
       original_language: "no change",
       returned_language: "no change"
     },
     status: {
       static_flag: false,
       status_active_flag: "active",
       status_draft_flag: "draft",
       status_archived_flag: "archived",
       skip_if_blank: true
     },
     categories: {
       static_flag: false,
       override_categories: false,
       find: "",
       replace: "",
       category_name: nil
     },
     discount_amount: {
       discount_type: true
     },
     standardized_product_type: {
       language_code: "en",
       static_flag: false
     },
     mpn: {
       static_flag: false
     },
     bin_picking_number: {
       static_flag: false
     },
     metafields_global_title_tag: {
       find: "",
       replace: "",
       skip_if_blank: true
     },
     metafields_global_description_tag: {
       find: "",
       replace: "",
       skip_if_blank: true,
       static_flag: false
     },
     handle: {},
     sku: {},
     collection: {
       static_flag: false
     },
     product_url: {},
     exported_at: {},
     shopify_product_id: {},
     shopify_variant_id: {},
     visible: {
       is_visible_value: nil,
       is_not_visible_value: nil,
       static_value: nil,
       static_flag: false
     },
     track_quantity: {
       eq_enable_track_qty_value: "true",
       eq_disable_track_qty_value: "false",
       skip_if_blank: true,
       static_flag: false
     },
     stock_status: {
       static_value: nil,
       static_flag: false,
       in_stock_flag: "true",
       out_stock_flag: "false"
     },
     length: {static_flag: false},
     width: {static_flag: false},
     height: {static_flag: false},
     preorder_message: {},
     ribbon: {},
     purchase_availability: {
       static_flag: false,
       purchase_available_flag: "available",
       purchase_preorder_flag: "preorder",
       purchase_disabled_flag: "disabled"
     },
     availability_description: {static_flag: false},
     quantity_quality_control: {
       rules_json: [{operator: "<=", key: "0", value: "0"}],
       reset_init_quantity: false,
       deduct_quantity_from_column: nil,
       skip_if_blank: true,
       skip_before_rules: false
     },
     quantity_on_hand: {
       rules_json: [{operator: "<=", key: "0", value: "0"}],
       reset_init_quantity: false,
       deduct_quantity_from_column: nil,
       skip_if_blank: true,
       skip_before_rules: false
     },
     quantity_safety_stock: {
       rules_json: [{operator: "<=", key: "0", value: "0"}],
       reset_init_quantity: false,
       deduct_quantity_from_column: nil,
       skip_if_blank: true,
       skip_before_rules: false
     },
     quantity_damaged: {
       rules_json: [{operator: "<=", key: "0", value: "0"}],
       reset_init_quantity: false,
       deduct_quantity_from_column: nil,
       skip_if_blank: true,
       skip_before_rules: false
     },
     product_attribute: {
       custom_field_key: nil,
       create_key_if_not_found: true
     },
     backorders: {
       static_flag: false,
       disallow_backorder: "no",
       allow_but_notify: "notify",
       allow_backorder: "yes"
     },
     quantity_reserved: {
       rules_json: [{operator: "<=", key: "0", value: "0"}],
       reset_init_quantity: false,
       deduct_quantity_from_column: nil,
       skip_if_blank: true,
       skip_before_rules: false
     },
     quantity_incoming: {
       rules_json: [{operator: "<=", key: "0", value: "0"}],
       reset_init_quantity: false,
       deduct_quantity_from_column: nil,
       skip_if_blank: true,
       skip_before_rules: false
     },
     price_region: {
       price_delimiter: "auto",
       price_round: 3,
       pricing_conditions: [{condition: "any", formula: "*1"}],
       restrict_conditions: [],
       default_currency: "USD",
       static_flag: false,
       skip_if_blank: true,
       additional_currencies: []
     },
     compare_price_at_region: {
       price_delimiter: "auto",
       fallback_to_price_if_less_than_price: false,
       force_override_compare_at_price: false,
       compare_at_pricing_conditions: [{condition: "any", formula: "*1"}],
       price_round: -100,
       compare_price_at_restrict_conditions: [],
       static_flag: false,
       default_currency: "USD",
       column_mapping: false,
       skip_if_blank: true,
       additional_currencies: []
     },
     short_description: {
       case_convert: nil,
       wrap_tag: "p",
       labels: nil,
       find: "",
       replace: "",
       convert_line_break: true,
       force_override_description: false,
       skip_zero_blank_labels: false,
       update_only_if_nil: false,
       skip_if_blank: true,
       original_language: "no change",
       returned_language: "no change"
     },
     condition: {
       static_flag: false,
       skip_if_blank: true,
       condition_is_new: "new",
       condition_is_used: "used",
       condition_is_refurbished: "refurbished"
     },
     show_condition: {
       static_flag: false,
       enable_condition: "true",
       disable_condition: "false"
     },
     additional_info_section: {
       create_key_if_not_found: true,
       section_title: nil
     },
     gtin: {
       static_flag: false
     },
     search_keywords: {
       static_flag: false
     },
     page_title: {
       case_convert: nil,
       ignore_words: "",
       auto_ignore_option_words: false,
       find: "",
       replace: "",
       title_separator: " - "
     },
     warranty: {
       case_convert: nil,
       wrap_tag: "p",
       labels: nil,
       find: "",
       replace: "",
       convert_line_break: true,
       force_override_description: false,
       skip_zero_blank_labels: false,
       update_only_if_nil: false,
       skip_if_blank: true,
       original_language: "no change",
       returned_language: "no change",
       deepseek_translation: false
     },
     meta_description: {
       case_convert: nil,
       labels: nil,
       find: "",
       replace: "",
       force_override_description: false,
       skip_zero_blank_labels: false,
       update_only_if_nil: false,
       skip_if_blank: true,
       original_language: "no change",
       returned_language: "no change",
       deepseek_translation: false
     }}
  end
end
