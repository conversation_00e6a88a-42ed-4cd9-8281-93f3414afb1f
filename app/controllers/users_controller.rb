class UsersController < AuthenticatedController
  before_action :find_user, except: [:unsubscribe]
  before_action :authenticate_account!, except: [:unsubscribe]

  def preferences
  end

  def billings
  end

  def unsubscribe
    # todo change params shopify domain to current shop?
    if params[:shopify_domain]
      user = User.find_by(shopify_domain: params[:shopify_domain])
      if user&.inactive?
        # unsubscribe to uninstalled users directly
        user.email_subscriptions = []
        user.save

        redirect_to "https://app.stock-sync.com"
      else
        redirect_to preferences_users_path(tab: "notifications")
      end
    else
      redirect_to preferences_users_path(tab: "notifications")
    end
  end

  def extend
    session[:coupon_code] = params[:coupon_code] if params[:coupon_code]
    if @user
      @user.verify_coupon_code(params[:coupon_code])
      @user.save
    end

    redirect_to :root
  end

  def products
    render "dashboard/index"
  end

  def extend_trial
    current_shop.update!(trial_extension_used: true, trial_days: current_shop.trial_days + 14) unless current_shop.trial_extension_used
    redirect_to "/billing", notice: "Trial has been extended!"
  rescue => e
    Rails.logger.error("Failed to extend trial: #{e.message}")
    redirect_to billings_users_path, alert: "Something went wrong"
  end

  private

  def find_user
    @user = current_shop
  end
end
