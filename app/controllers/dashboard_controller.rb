class DashboardController < AuthenticatedController
  skip_before_action :check_if_store_validated, only: [:update_woocommerce_credentials]

  def index
    current_path = request.env["PATH_INFO"]
    if current_path === "/v2"
      redirect_to "/"
    end

    respond_to do |format|
      format.html
      format.any { head :no_content }
    end
  end

  def product_changes
    render "dashboard/index"
  end

  def update_woocommerce_credentials
    render "dashboard/index"
  end
end
