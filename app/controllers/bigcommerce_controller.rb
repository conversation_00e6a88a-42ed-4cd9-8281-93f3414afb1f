class BigcommerceController < ApplicationController
  include BigcommerceDecoder

  def uninstall
    payload = decode_signed_payload(params[:signed_payload])
    user = Shop::Bigcommerce.find_by(bigcommerce_store_hash: payload["store_hash"])
    user&.process_uninstall_event
    head :ok
  end

  def revoke
    payload = decode_signed_payload(params[:signed_payload])
    if payload["store_hash"].present?
      if (shop = Shop::Bigcommerce.find_by(bigcommerce_store_hash: payload["store_hash"]))
        acc = shop.account.find_by(email: payload["user"]["email"])
        acc&.destroy
      end
    end
    head :ok
  end
end
