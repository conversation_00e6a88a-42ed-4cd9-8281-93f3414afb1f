class XeroController < ConnectionsController
  def authorize
    grant_url = "https://login.xero.com/identity/connect/authorize?response_type=code&client_id=#{ENV["XERO_CLIENT_ID"]}&redirect_uri=#{callback_xero_index_url}&scope=offline_access accounting.settings&state=#{params[:profile_id]}"
    redirect_to grant_url
  end

  def callback
    notice = "Failed to authorize Xero, please try again"

    if params[:code]
      authorization_code = params[:code]
      uri = "https://identity.xero.com/connect/token"
      user_profile = UserProfile.find_by(id: params[:state])

      headers = {
        "Content-Type" => "application/x-www-form-urlencoded"
      }

      auth = {username: ENV["XERO_CLIENT_ID"], password: ENV["XERO_CLIENT_SECRET"]}

      body = {
        "code" => authorization_code,
        "grant_type" => "authorization_code",
        "redirect_uri" => callback_xero_index_url
      }

      body = URI.encode_www_form body

      response = HTTParty.post(uri, body: body, basic_auth: auth, headers: headers)

      if response && response.dig("access_token").present? && response.dig("refresh_token").present?
        user_profile.xero_access_token = response["access_token"]
        user_profile.xero_refresh_token = response["refresh_token"]
        begin
          event = JWT.decode(response["access_token"], nil, false).first["authentication_event_id"]
        rescue
          event = nil
        end
        user_profile.oauth_granted = true
        user_profile.oauth_granted_to = "xero"

        uri = "https://api.xero.com/connections"
        if event.present?
          uri += "?authEventId=#{event}"
        end

        headers = {
          "Authorization" => "Bearer #{response["access_token"]}"
        }

        begin
          response = HTTParty.get(uri, headers: headers).parsed_response.max_by { |json| json["updatedDateUtc"] }
          if response.nil? && event.present?
            uri = "https://api.xero.com/connections"
            response = HTTParty.get(uri, headers: headers).parsed_response.max_by { |json| json["updatedDateUtc"] }
          end
        rescue
          response = {}
        end

        if response && response.dig("tenantId").present? && response.dig("id").present?
          if user_profile.xero_tenant_id.present? && user_profile.xero_tenant_id != "#{response["id"]}:#{response["tenantId"]}"
            uri += "/#{user_profile.xero_tenant_id.split(":").first}"
            HTTParty.delete(uri, headers: headers)
          end

          user_profile.xero_tenant_id = "#{response["id"]}:#{response["tenantId"]}"
          user_profile.save
          notice = "Successfully authorized Xero"
        end
      end
    end

    redirect_to UserProfile.feed_manager_link(params[:state], user_profile&.user), notice: notice
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    tries = 0

    begin
      uri = "https://api.xero.com/connections/#{user_profile.xero_tenant_id.split(":").first}"

      headers = {
        "Content-Type" => "application/json",
        "Authorization" => "Bearer #{user_profile.xero_access_token}"
      }

      if (code = HTTParty.delete(uri, headers: headers).response.code) == 204
        user_profile.xero_tenant_id = nil
      else
        raise HTTParty::Error.new code
      end
    rescue HTTParty::Error => e
      if e.message == "403"
        if tries < 1
          tries += 1
          uri = "https://identity.xero.com/connect/token"

          headers = {
            "Content-Type" => "application/x-www-form-urlencoded"
          }

          auth = {username: ENV["XERO_CLIENT_ID"], password: ENV["XERO_CLIENT_SECRET"]}

          body = {
            "grant_type" => "refresh_token",
            "refresh_token" => user_profile.xero_refresh_token
          }

          response = HTTParty.post(uri, body: body, basic_auth: auth, headers: headers)
          if response.dig("access_token").present?
            user_profile.xero_access_token = response["access_token"]
            retry
          end
        else
          exist = false
          uri = "https://api.xero.com/connections"

          headers = {
            "Content-Type" => "application/json",
            "Authorization" => "Bearer #{user_profile.xero_access_token}"
          }

          response = HTTParty.get(uri, headers: headers)
          if response.response.code == 200
            response.parsed_response.each { |json|
              if user_profile.xero_tenant_id == "#{json["id"]}:#{json["tenantId"]}"
                exist = true
                break
              end
            }
          end

          if !exist && response.response.code == 200
            user_profile.xero_tenant_id = nil
          end
        end
      end
    end

    user_profile.xero_access_token = nil
    user_profile.xero_refresh_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
