module ActiveAdmin
  module Custom
    class SessionsController < ActiveAdmin::Devise::SessionsController
      include ::ActiveAdmin::Devise::Controller

      def new
        unless Rails.env.development? || Rails.env.test?
          token = request.cookies["CF_Authorization"]
          return @current_admin_user if @current_admin_user.present? && admin_user_signed_in?
          unless token.present?
            return render plain: "Unauthorized access", status: :ok
          end

          jwk_loader = lambda do |options|
            @cached_keys = nil if options[:invalidate]
            @cached_keys ||= keys
          end

          payload, = JWT.decode(token, nil, true, {
            nbf_leeway: 30, # allowed drift in seconds
            exp_leeway: 30, # allowed drift in seconds
            iss: cf_teams_url,
            verify_iss: true,
            aud: cf_aud,
            verify_aud: true,
            verify_iat: true,
            algorithm: "RS256",
            jwks: jwk_loader
          })
          email = payload["email"]

          adminuser = AdminUser.find_by(email: email)
          unless adminuser
            return render plain: "Not found", status: :ok
          end
          sign_in(:admin_user, adminuser)
          flash[:notice] = "Login success"
          return redirect_to admin_root_path
        end
        super
      end

      private

      def cf_aud
        Settings.cloudflare.cf_jwt_aud || ENV["CF_JWT_AUD"]
      end

      def cf_teams_url
        Settings.cloudflare.cf_teams_url || ENV["CF_TEAMS_URL"]
      end

      def keys
        @keys ||= HTTParty.get("#{cf_teams_url}/cdn-cgi/access/certs").deep_symbolize_keys!
      end
    end
  end
end
