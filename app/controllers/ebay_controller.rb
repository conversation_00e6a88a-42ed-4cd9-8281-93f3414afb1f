class EbayController < ConnectionsController
  # ? eBay will not support this traditional API in the future.
  # ? eBay will support the OAuth2 API instead which used in EbayV2Controller.
  def authorize
    session_id = EbayStore.save_ebay_session_id(current_profile)
    Rails.logger.info "session #{session_id} #{current_profile.id}"
    state = params[:profile_id]
    redirect_to "https://signin.ebay.com/ws/eBayISAPI.dll?SignIn&runame=Cloud_Coder_Sdn-CloudCod-StockS-jpwdrd&SessID=#{session_id}&ruparams=state%3D#{state}"
  end

  def granted
    user_profile_id = params[:state]
    user_profile = UserProfile.find_by(id: user_profile_id)
    ebay_auth_token = EbayStore.get_ebay_auth_token(user_profile)

    if ebay_auth_token.present?
      user_profile.ebay_auth_token = ebay_auth_token
      user_profile.oauth_granted = true
      user_profile.oauth_granted_to = "ebay"
      user_profile.source_type = "ebay"
      user_profile.save
    end

    redirect_to UserProfile.feed_manager_link(user_profile_id, user_profile.user), notice: "Ebay authorize access successfully"
  end

  def declined
    redirect_to edit_user_profile_url(current_profile.id), error: "Ebay authorize access failed."
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.ebay_auth_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
