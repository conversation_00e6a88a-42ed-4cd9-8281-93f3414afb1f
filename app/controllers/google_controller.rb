require "google/apis/content_v2"
require "google/api_client/client_secrets"

class GoogleController < ConnectionsController
  def authorize
    user_profile = current_profile
    if user_profile.nil?
      user_profile = UserProfile.find_by(id: params[:profile_id])

      if user_profile.nil? && params[:state]
        state_data = JSON.parse(params[:state])
        user_profile = UserProfile.find_by(id: state_data["profile_id"])
      end
    end

    client_secrets = Google::APIClient::ClientSecrets.load("google_client_secrets.json")
    auth_client = client_secrets.to_authorization
    auth_client.update!(
      scope: "https://www.googleapis.com/auth/content",
      redirect_uri: "#{Settings.hostname}/google/authorize",
      additional_parameters: {
        "access_type" => "offline",
        "state" => {profile_id: user_profile.id}.to_json
      }
    )
    if request["code"].nil?
      auth_uri = auth_client.authorization_uri.to_s
      redirect_to auth_uri
    else

      auth_client.code = request["code"]
      auth_client.refresh_token = user_profile.google_refresh_token
      auth_client.fetch_access_token!
      auth_client.client_secret = nil

      user_profile.google_refresh_token = auth_client.refresh_token if auth_client.refresh_token
      user_profile.google_auth_client = auth_client.to_json
      user_profile.oauth_granted = true
      user_profile.oauth_granted_to = "google_shopping"
      user_profile.source_type = "google_shopping"
      user_profile.save

      redirect_to UserProfile.feed_manager_link(user_profile.id, user_profile.user), notice: "Google authorize access successfully. Please proceed by clicking 'Next'"
    end
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])

    uri = URI("https://accounts.google.com/o/oauth2/revoke")
    params = {token: user_profile.google_refresh_token}
    uri.query = URI.encode_www_form(params)
    _response = Net::HTTP.get(uri)

    user_profile.google_auth_client = nil
    user_profile.google_refresh_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
