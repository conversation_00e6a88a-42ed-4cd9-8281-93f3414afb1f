module CustomPlanValidator
  extend ActiveSupport::Concern

  LOWER_PRICE_LIMIT = 25
  UPPER_PRICE_LIMIT = 300

  def self.check(**attributes)
    Rails.logger.debug(attributes)
    price = attributes[:price].to_f
    variant_limit = attributes[:variant_limit].to_i
    profile_limit = attributes[:profile_limit].to_i
    schedule_limit = (attributes[:schedule_limit]).to_i

    calculate_variant_limit = variant_limit / 5000
    calculate_profile_limit = profile_limit / 1.5
    calculate_schedule_limit = schedule_limit / 2.0

    custom_plan_price = calculate_variant_limit * calculate_profile_limit * calculate_schedule_limit
    custom_plan_price = UPPER_PRICE_LIMIT if custom_plan_price > UPPER_PRICE_LIMIT
    custom_plan_price = LOWER_PRICE_LIMIT if custom_plan_price < LOWER_PRICE_LIMIT

    Rails.logger.debug("Price from UI: #{price}")
    Rails.logger.debug("Price from BE: #{custom_plan_price}")
    price.round(2) == custom_plan_price.round(2)
  end
end
