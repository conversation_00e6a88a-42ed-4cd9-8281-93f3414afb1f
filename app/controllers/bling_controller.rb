class BlingController < ConnectionsController
  def authorize
    user_profile = UserProfile.where(id: params[:profile_id]).last

    # client_id =  "debf0c2799a5bae21b635bb74aafcbf5043af1c9",
    # client_secret  =  "870bd43facc1173e9aef8f8c0bcff4eb86d71941c571ea51ed78ce4cb5a8"

    query = {
      client_id: user_profile.woocommerce_consumer_key,
      state: user_profile.id,
      redirect_uri: callback_bling_index_url,
      response_type: "code"
    }
    uri = URI.parse("https://www.bling.com.br/Api/v3/oauth/authorize")
    uri.query = query.to_query

    redirect_to uri.to_s
  end

  def callback
    profile = UserProfile.find_by(id: params[:state])
    if (code = params[:code])
      body = {code: code, grant_type: "authorization_code"}

      url = "https://www.bling.com.br/Api/v3/oauth/token"

      response = HTTParty.post(url, body: body, headers: {"Content-Type" => "application/x-www-form-urlencoded"}, basic_auth: {username: profile.woocommerce_consumer_key, password: profile.woocommerce_consumer_secret})
      response = response.parsed_response

      if response.has_key?("access_token")
        profile.xero_access_token = response["access_token"]
        profile.xero_refresh_token = response["refresh_token"]
        profile.oauth_granted = true
        profile.oauth_granted_to = "bling"
        profile.source_type = "bling"
        profile.save!
      end
    end
    redirect_to UserProfile.feed_manager_link(params[:state], profile.user)
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.update(oauth_granted: false, connection_settings: {})
    redirect_back(fallback_location: root_path)
  end
end
