class AirtableController < ConnectionsController
  def authorize
    redis = RedisManager.client
    profile = UserProfile.where(id: params[:profile_id]).last
    code_verifier = SecureRandom.urlsafe_base64(43)
    state = SecureRandom.urlsafe_base64(100) + "_#{profile.id}"
    code_challenge = Base64.urlsafe_encode64(Digest::SHA256.digest(code_verifier)).gsub(/[=+\/]/, "")
    redis.set("airtable_v2_#{profile.id}", code_verifier)

    query = {
      client_id: ENV["AIRTABLE_CLIENT_ID"],
      redirect_uri: callback_airtable_index_url,
      response_type: "code",
      scope: "data.records:read data.records:write schema.bases:read schema.bases:write",
      state: state,
      code_challenge: code_challenge,
      code_challenge_method: "S256"
    }
    uri = URI.parse("https://airtable.com/oauth2/v1/authorize")
    uri.query = query.to_query

    redirect_to uri.to_s
  end

  def callback
    profile_id = params[:state].split("_").last
    profile = UserProfile.find_by(id: profile_id)
    url = UserProfile.feed_manager_link(profile_id, profile.user)
    notice = "Airtable authorize access successfully. Please proceed by clicking 'Next'"
    error = "Airtable authorization failed."

    if (code = params[:code])
      code_verifier = RedisManager.get("airtable_v2_#{profile.id}")
      credentials = Base64.strict_encode64("#{ENV["AIRTABLE_CLIENT_ID"]}:#{ENV["AIRTABLE_CLIENT_SECRET"]}")
      body = {
        code: code,
        client_id: ENV["AIRTABLE_CLIENT_ID"],
        redirect_uri: callback_airtable_index_url,
        grant_type: "authorization_code",
        code_verifier: code_verifier
      }

      token_url = "https://airtable.com/oauth2/v1/token"

      response = HTTParty.post(token_url, body: body, headers: {"Authorization" => "Basic #{credentials}", "Content-Type" => "application/x-www-form-urlencoded"})
      response = response.parsed_response

      if response.has_key?("access_token")
        profile.update(oauth_granted: true, oauth_granted_to: "airtable", source_type: "airtable_v2", xero_refresh_token: response["refresh_token"], xero_access_token: response["access_token"])
        profile.save!
      end

      redirect_to url, notice: notice
    elsif redirect_to url, error: error
    end
  end

  def disconnect
    profile = UserProfile.find_by(id: params[:profile_id])
    profile.update(oauth_granted: false, oauth_granted_to: nil, xero_refresh_token: nil, xero_access_token: nil)
    profile.save!
    redirect_back(fallback_location: root_path)
  end
end
