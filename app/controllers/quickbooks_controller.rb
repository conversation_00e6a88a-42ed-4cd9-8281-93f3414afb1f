class QuickbooksController < ConnectionsController
  before_action :authenticate_account!, except: ["revoke"]

  def authorize
    session[:profile_id] = params[:profile_id]
    redirect_uri = oauth_callback_quickbooks_url
    oauth_client = IntuitOAuth::Client.new(ENV["QUICKBOOKS_CLIENT_ID"], ENV["QUICKBOOKS_CLIENT_SECRET"], redirect_uri, "production")
    scopes = [IntuitOAuth::Scopes::ACCOUNTING]

    url = oauth_client.code.get_auth_uri(scopes)
    redirect_to url
  end

  def oauth_callback
    redirect_uri = oauth_callback_quickbooks_url
    oauth_client = IntuitOAuth::Client.new(ENV["QUICKBOOKS_CLIENT_ID"], ENV["QUICKBOOKS_CLIENT_SECRET"], redirect_uri, "production")
    oauth_token = oauth_client.token.get_bearer_token(params[:code])

    if params[:state]

      user_profile = UserProfile.find_by(id: session[:profile_id])
      user_profile.quickbooks_access_token = oauth_token.access_token
      user_profile.quickbooks_refresh_token = oauth_token.refresh_token
      user_profile.quickbooks_realm_id = params[:realmId]
      user_profile.oauth_granted = true
      user_profile.oauth_granted_to = "quickbooks"
      user_profile.source_type = "quickbooks"
      user_profile.save
      # Airbrake.notify("#{session[:profile_id]} QuickBook expired in : #{oauth_token.expires_in}")
    end

    redirect_to UserProfile.feed_manager_link(session[:profile_id], user_profile.user), notice: "Successfully authorized Quickbooks"
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.quickbooks_access_token = nil
    user_profile.quickbooks_refresh_token = nil
    user_profile.quickbooks_realm_id = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end

  def revoke
    realm_id = params["realmId"]
    user = Shop::Quickbooks.find_by(quickbooks_realm_id: realm_id)
    user&.process_uninstall_event
    user.quickbooks_realm_id = nil
    user.save

    # ? need to relogin qb users when user delete app from qb admin
    cookies[:stocksync_current_store] = user.quickbooks_domain
    account = Account.find_by(shop_id: user.id)

    if account
      sign_in_and_redirect account, event: :authentication
    else
      redirect_to :root
    end
  end

  def sign_out
    reset_session
    cookies.delete(:stocksync_current_store)
    redirect_to new_account_session_url
  end
end
