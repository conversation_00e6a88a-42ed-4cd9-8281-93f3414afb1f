class ApplicationController < ActionController::Base
  protect_from_forgery
  impersonates :account

  expose(:current_profile) {
    if current_shop
      profile = nil
      if params[:profile_id] && current_shop.user_profiles.where(id: params[:profile_id].to_i).exists?
        profile = current_shop.user_profiles.find(params[:profile_id])
      elsif session[:profile_id]
        begin
          profile = current_shop.user_profiles.find(session[:profile_id])
        rescue ActiveRecord::RecordNotFound
          profile = current_shop.user_profiles.first
        end
      else
        profile = current_shop.user_profiles.first
      end
      session[:profile_id] = profile.id if profile.present?
      profile
    end
  }

  def current_shop
    account_signed_in? ? (current_account.shop) : (GuestUser.new)
  end
  helper_method :current_shop

  def audited_username
    if current_admin_user.present?
      # if data updated in admin or impersonate
      current_admin_user.email
    else
      "User"
    end
  rescue => e
    Airbrake.notify(e, message: "Audited Username Error")
  end

  def routing_error
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/404", layout: false, status: :not_found }
      format.xml { head :not_found }
      format.any { redirect_to "/file_not_found" }
    end
  end

  def file_not_found
    render file: "#{Rails.root}/public/404", layout: false, status: :not_found
  end

  protected

  def user_for_paper_trail
    admin_user_signed_in? ? current_admin_user.try(:id) : nil
  end

  class GuestUser < User
    def id
      0
    end

    def shopify_domain
      ""
    end

    def email
      "<EMAIL>"
    end

    def created_at
      DateTime.new(2010, 9, 1)
    end
  end
end
