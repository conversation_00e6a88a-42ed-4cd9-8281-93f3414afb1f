class PasswordsController < Devise::PasswordsController
  layout "basic"
  require "action_view"

  include ActionView::Helpers::SanitizeHelper

  ## add more VALID_PLATFORMS here as necessary

  VALID_PLATFORMS = ["Woocommerce", "Square", "Prestashop", "Quickbooks"].freeze

  def new
    if params[:platform].present? && VALID_PLATFORMS.include?(params[:platform].titleize)
      @platform = sanitize(params[:platform]).titleize
    else
      redirect_to root_path and return
    end
  end

  def update
    token = Devise.token_generator.digest(Account, :reset_password_token, reset_password_params[:reset_password_token])
    account = Account.find_by(reset_password_token: token)

    if account
      if reset_password_params[:password] === reset_password_params[:password_confirmation]
        account.password = reset_password_params[:password]
        account.save
        flash[:success] = "Your credentials have been updated. Please sign in again."
        redirect_to new_account_session_path and return
      else
        flash[:error] = "The passwords do not match. Please try again."
        redirect_back fallback_location: root_path and return
      end
    else
      redirect_to root_path
    end
  end

  private

  def reset_password_params
    params.require(:user).permit(:reset_password_token, :password, :password_confirmation)
  end
end
