class ShopifyFlowController < ApplicationController
  skip_before_action :verify_authenticity_token

  def disable_schedule
    status = false
    find_shop_and_feed do |shop, profile|
      if profile.scheduler_enabled?
        # profile.update(scheduler_enabled: false, status: "pause")
        # profile.disable_scheduler("Shopify Flow")
        profile.deactivate("Shopify Flow", "user")
        status = true
      end
    end
    render json: {success: status}
  end

  def enable_schedule
    status = false
    find_shop_and_feed do |shop, profile|
      if !profile.scheduler_enabled? && shop.is_valid?(profile.feed_type)
        # profile.update(scheduler_enabled: true, status: "start")
        # event = Schedulable::CreateEvent.new(schedule: profile.as_schedule, trigger_by: 'enable_schedule', queue: profile.job_queue).call
        profile.activate_profile_scheduler("enable_schedule", "user")
        status = true
      end
    end
    render json: {success: status}
  end

  def run_feed
    status = false
    find_shop_and_feed do |shop, profile|
      if check_subscription_plan(shop, profile)
        if profile.start? || profile.pause?
          profile.start
          feed_process_job = ProcessFeedJob.new(profile.id, trigger_by: "shopify_flow")
          Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue, priority: 2)
          profile.queuing
        end
        status = true
      end
    end
    render json: {success: status}
  end

  def find_shop_and_feed
    verified = verify_webhook(request.body.read, request.headers["HTTP_X_SHOPIFY_HMAC_SHA256"])
    if verified
      if (shop = Shop::Shopify.find_by(shopify_domain: params[:shopify_domain]))
        profile_id = params.dig(:properties, :profile_id)
        if shop.user_profiles.exists?(profile_id)
          profile = shop.user_profiles.find(profile_id)
          yield(shop, profile)
        end
      end
    else
      render json: {success: false}
    end
  end

  def check_subscription_plan(shop, profile)
    return true if profile.fast_track
    return false if profile.feed_type == "import" && !shop.sufficient_credits?
    return false unless shop.is_valid?(profile.feed_type)
    if shop.package == Settings.first_plan && shop.charge_id
      if profile.feed_type == "import" || profile.feed_type == "update"
        return false
      end
    end
    true
  end

  private

  def verify_webhook(data, hmac_header)
    calculated_hmac = Base64.strict_encode64(OpenSSL::HMAC.digest("sha256", ENV["SHOPIFY_API_SECRET"], data))
    ActiveSupport::SecurityUtils.secure_compare(calculated_hmac, hmac_header)
  end
end
