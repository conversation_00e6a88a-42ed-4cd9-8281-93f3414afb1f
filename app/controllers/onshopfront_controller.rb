class OnshopfrontController < ConnectionsController
  def authorize
    user_profile = UserProfile.where(id: params[:profile_id]).last

    # client_id =  "mjmfKgRQHQ3xKCX0CpCL2K4CgAlQ2LXW",
    # client_secret  =  "ZQy6DRA9tsAGWrLgvftKduiB86S4HdofD0cGFSk9"

    query = {
      client_id: user_profile.woocommerce_consumer_key,
      state: user_profile.id,
      redirect_uri: callback_onshopfront_index_url,
      response_type: "see_products"
    }
    uri = URI.parse("https://onshopfront.com/oauth/authorize")
    uri.query = query.to_query

    redirect_to uri.to_s
  end

  def callback
    profile = UserProfile.find_by(id: params[:state])
    user_profile = UserProfile.find_by(id: params[:profile_id])
    if (code = params[:code])
      body = {client_id: user_profile.woocommerce_consumer_key, client_secret: user_profile.woocommerce_consumer_secret, redirect_uri: UserProfile.feed_manager_link(params[:state], profile.user), grant_type: "authorization_code", code: code}

      url = "https://onshopfront.com/oauth/token"

      response = HTTParty.post(url, body: body, headers: {"Accept" => "application/json", "Content-Type" => "application/json"})
      response = response.parsed_response

      if response.has_key?("access_token")
        profile.xero_access_token = response["access_token"]
        profile.xero_refresh_token = response["refresh_token"]
        profile.oauth_granted = true
        profile.oauth_granted_to = "onshopfront"
        profile.source_type = "onshopfront"
        profile.save!
      end
    end
    redirect_to UserProfile.feed_manager_link(params[:state], profile.user)
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.update(oauth_granted: false, connection_settings: {})
    redirect_back(fallback_location: root_path)
  end
end
