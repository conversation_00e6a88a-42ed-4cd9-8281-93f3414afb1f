class MicrosoftController < ConnectionsController
  def authorize
    code = params[:code]
    user_profile_id = params[:state]

    if code.present?
      user_profile = UserProfile.find_by(id: user_profile_id)

      # Step 2 - https://learn.microsoft.com/en-us/onedrive/developer/rest-api/getting-started/graph-oauth?view=odsp-graph-online
      token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
      body = {
        client_id: ENV["MICROSOFT_GRAPH_API_KEY"],
        redirect_uri: authorize_microsoft_index_url(host: Settings.hostname),
        client_secret: ENV["MICROSOFT_GRAPH_API_SECRET"], # must be changed every 2 years. <EMAIL> account
        code: code,
        grant_type: "authorization_code"
      }
      response = HTTParty.post(token_url, body: body, headers: {"Content-Type" => "application/x-www-form-urlencoded"})
      response = response.parsed_response

      access_token = response.fetch("access_token", nil)
      refresh_token = response.fetch("refresh_token", nil)

      user_profile.body_raw = response.to_s
      user_profile.one_drive_token = access_token
      user_profile.one_drive_refresh_token = refresh_token
      user_profile.oauth_granted = true
      user_profile.oauth_granted_to = "one_drive_file"
      user_profile.save

      redirect_to UserProfile.feed_manager_link(user_profile_id, user_profile.user) and return
    else
      Airbrake.notify("OneDriveAuthError", payload: params)
      redirect_to UserProfile.feed_manager_link(user_profile_id, nil), error: "One Drive authorize access failed."
    end
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.one_drive_token = nil
    user_profile.one_drive_refresh_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
