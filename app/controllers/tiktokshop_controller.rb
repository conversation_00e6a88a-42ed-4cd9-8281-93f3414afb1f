class TiktokshopController < ConnectionsController
  skip_before_action :authenticate_account!, only: [:callback]

  def authorize
    current_profile
    state = params[:profile_id]
    grant_url = "https://services.tiktokshops.us/open/authorize?service_id=#{Settings.tiktokshop.service_id}&state=#{state}"
    redirect_to grant_url
  end

  def callback
    notice = "Failed to authorize TikTok Shop, please try again"

    if params[:code]
      auth_code = params[:code]
      user_profile_id = params[:state]

      uri = "https://auth.tiktok-shops.com/api/v2/token/get"

      response = Faraday.get(uri) do |req|
        req.params["app_key"] = ENV["TIKTOK_CLIENT_ID"]
        req.params["auth_code"] = auth_code
        req.params["app_secret"] = ENV["TIKTOK_CLIENT_SECRET"]
        req.params["grant_type"] = "authorized_code"
      end

      if response.success?
        response_data = JSON.parse(response.body)
        if response_data["data"] && response_data["data"]["access_token"]
          data = response_data["data"]
          user_profile = UserProfile.find_by(id: user_profile_id)
          tiktokshop_settings = {
            tiktokshop_access_token: data["access_token"],
            tiktokshop_refresh_token: data["refresh_token"]
          }

          user_profile.update(
            connection_settings: tiktokshop_settings,
            oauth_granted: true,
            oauth_granted_to: "tiktokshop"
          )

          notice = "Successfully authorized TikTok Shop"
        end
      end
    end

    redirect_to UserProfile.feed_manager_link(user_profile_id, user_profile&.user), notice: notice
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])

    user_profile.connection_settings = {}
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
