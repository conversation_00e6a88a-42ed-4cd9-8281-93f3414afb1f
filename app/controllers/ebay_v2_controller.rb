class EbayV2Controller < ConnectionsController
  def authorize
    url = Rails.env.production? ? "https://auth.ebay.com/oauth2/authorize" : "https://auth.sandbox.ebay.com/oauth2/authorize"
    user_profile = UserProfile.where(id: params[:profile_id]).last
    query = {
      client_id: ENV["EBAY_APP_ID"],
      state: user_profile.id,
      redirect_uri: Settings.ebay.app_keys.ru_name, # "Cloud_Coder_Sdn-CloudCod-StockS-hrkzxvw",
      scope: Settings.ebay.app_keys.scope,
      response_type: "code"
    }
    uri = URI.parse(url)
    uri.query = query.to_query

    redirect_to uri.to_s
  end

  def granted
    profile = UserProfile.find_by(id: params[:state])
    if (code = params[:code])
      body = {code: code,
              grant_type: "authorization_code",
              redirect_uri: Settings.ebay.app_keys.ru_name}

      url = Rails.env.production? ? "https://api.ebay.com/identity/v1/oauth2/token" : "https://api.sandbox.ebay.com/identity/v1/oauth2/token"
      response = HTTParty.post(url, body: body, headers: {"Content-Type" => "application/x-www-form-urlencoded"}, basic_auth: {username: ENV["EBAY_APP_ID"], password: ENV["EBAY_CERT_ID"]})
      response = response.parsed_response

      if response.has_key?("access_token")
        # ? use xero_access_token and xero_refresh_token because we dont have ebay_refresh_token attribute in user_profile table
        profile.xero_access_token = response["access_token"]
        profile.xero_refresh_token = response["refresh_token"]
        profile.oauth_granted = true
        profile.oauth_granted_to = "ebay_v2"
        profile.source_type = "ebay_v2"
        profile.save!
      end
    end

    redirect_to UserProfile.feed_manager_link(params[:state], profile&.user), notice: "Ebay authorize access successfully"
  end

  def declined
    redirect_to UserProfile.feed_manager_link(params[:state], nil), notice: "Ebay authorize access failed."
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.xero_refresh_token = nil
    user_profile.xero_access_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
