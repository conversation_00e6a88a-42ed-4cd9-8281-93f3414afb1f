# frozen_string_literal: true

class WixController < ApplicationController
  skip_before_action :verify_authenticity_token

  def webhook_uninstall_callback
    decode_webhook_data do |_user, data|
      user = Shop::Wix.find_by(wix_code: data.instanceId)
      user&.process_uninstall_event
    end
    render json: {status: 200}
  end

  def webhook_wix_payment
    decode_webhook_data do |user, data|
      data = JSON.parse(data.data)
      annually = data["cycle"] == "YEARLY"
      charge_id = data["invoiceId"]
      plan_name = data["vendorProductId"]

      user.handle_subscription_event(charge_id, "active", nil, annually, get_plan_name(plan_name))
    end
    render json: {status: 200}
  end

  # { "operationTimeStamp": "2021-10-11T17:12:55.369Z", "vendorProductId": "prox2", "cycle": "MONTHLY", "expiresOn": "2021-11-11T17:12:55Z", "invoiceId": "960612463" }
  def webhook_wix_upgrade
    decode_webhook_data do |user, data|
      data = JSON.parse(data.data)
      annually = data["cycle"] == "YEARLY"
      charge_id = data["invoiceId"]
      plan_name = data["vendorProductId"]

      user.handle_subscription_event(charge_id, "active", nil, annually, get_plan_name(plan_name))
    end
    render json: {status: 200}
  end

  def webhook_cancel_subscription
    decode_webhook_data do |user, data|
      if user
        prev_plan = user.package
        package = Plan.find_by_key("Snappy")
        package.activate(user, nil)
        billing = user.billings.new(shop_name: user.wix_domain, plan_name: package.key, charge_type: Billing::DOWNGRADE)
        if current_shop.save
          billing&.save
          UserMailer.delay(queue: "mailers").plan_change_email(current_shop, prev_plan) if current_shop.email_subscribed_to?(:transactions)
        end
      end
    end
    render json: {status: 200}
  end

  # ? plan key cannot be modified on wix, so we need to change it here
  def get_plan_name(plan_name)
    case plan_name
    when "basic-credit"
      plan_name = "Basic + Credit"
    when "prox-credit"
      plan_name = "ProX + Credit"
    when "proxl-credit"
      plan_name = "ProXL + Credit"
    end
    plan_name
  end

  private

  def decode_webhook_data
    public_key = Settings.wix.public_key
    payload = request.body.read
    decoded_payload = JWT.decode payload, public_key, false
    data = JSON.parse(decoded_payload.first["data"], object_class: OpenStruct)
    user = Rails.env.development? ? Shop::Wix.first : Shop::Wix.find_by(wix_code: data.instanceId)
    yield(user, data)
  end
end
