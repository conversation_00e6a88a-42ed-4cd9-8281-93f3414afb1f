class WebhooksController < ApplicationController
  include ShopifyApp::WebhookVerification

  def index
    Rails.logger.info(params.inspect)
    Rails.logger.info(request.headers.inspect)
    head :ok
  end

  def customer_request
  rescue ActiveRecord::RecordNotFound
  ensure
    head :ok
  end

  def customer_redact
  rescue ActiveRecord::RecordNotFound
  ensure
    head :ok
  end

  def shop_redact
    user = User.find_by!(shopify_domain: params[:shop_domain])

    info_attrs = {
      email: nil,
      customer_email: nil,
      province: nil,
      shop_owner: nil,
      phone: nil,
      city: nil,
      country: nil,
      notification_email: nil,
      email_subscriptions: []
    }

    user.update(info_attrs)
    user.save(validate: false)
    AnalyticsEventTracker.push("user", "redact")
  rescue ActiveRecord::RecordNotFound
  ensure
    head :ok
  end

  def uninstall
    user = User.where(shopify_domain: params[:myshopify_domain]).take
    user&.process_uninstall_event

    head :ok
  end
end
