class UserProfilesController < AuthenticatedController
  before_action :authenticate_account!, except: ["sample_file", "download_product_logs", "download_not_in_feed", "download_s3_link"]

  def new
  end

  def edit
    if current_shop.user_profiles.where(id: params[:id].to_i).exists?
      session[:profile_id] = params[:id]
    else
      redirect_to :root
    end
  end

  def change_logs
    if current_shop.user_profiles.where(id: params[:id].to_i).exists?
      session[:profile_id] = params[:id]
    else
      redirect_to :root
    end
  end

  def show
  end

  def download_product_logs
    # TODO make this secured
    profile = UserProfile.find_by(id: params[:profile_id])

    csv = CSV.generate do |csv|
      csv << ["id", "status", "Total Matched", "Total Updated", "Total Variants", "Total out-of-stock Variants", "Created at", "Trigger_by"]
      profile.product_logs.where("created_at > ?", 10.days.ago).each do |log|
        created_at = log.created_at.in_time_zone(profile.timezone)
        csv << [log.id, log.status, "#{log.percentage_matched}%", log.number_product_updated, log.total_store_skus, log.number_out_of_stocks, created_at, log.trigger_by]
      end
    end
    respond_to do |format|
      format.csv { send_data csv, filename: "activity_logs_last_10_days.csv" }
    end
  end

  def download_s3_link
    # TODO make this secured
    s3 = AwsServices.s3_resource_client
    file = s3.bucket(Settings.s3.bucket_name).object("product_logs/user_profiles/#{params[:profile_id]}_#{params[:log_id]}/#{params[:file]}")
    # file = s3.buckets["stock_sync"].objects["product_logs/user_profiles/#{params[:profile_id]}_#{params[:log_id]}/#{params[:file]}"]
    if file.exists?
      url = file.presigned_url(:get, expires_in: 86400, response_content_disposition: "attachment; filename=\"#{params[:name]}\"") # 24 hours expiry
      redirect_to url, allow_other_host: true
      # redirect_to file.url_for(:read, :response_content_disposition => "attachment; filename=\"#{params[:name]}\"").to_s
    else
      redirect_to "#{Settings.hostname}/file_not_ready"
      # redirect_to "#{Settings.hostname}/system/user_profiles/#{params[:profile_id]}_#{params[:log_id]}/#{params[:file]}"
    end
  end

  def download_not_in_feed
    # TODO make this secured
    profile = UserProfile.find_by(id: params[:profile_id])
    if profile&.active_product_log_id.present?
      nif = NotInFeed.where(user_profile_id: profile.id, product_log_id: profile.active_product_log_id)
      if nif.present?
        csv = CSV.generate do |csv|
          csv << ["ID", "Product Log ID", "Key", "Title", "Variant ID", "Created At", "Updated At"]
          nif.each do |log|
            csv << [log.id, log.product_log_id, log.key, log.title, log.shopify_variant_id, log.created_at, log.updated_at]
          end
        end
        respond_to do |format|
          format.csv { send_data csv, filename: "not_in_feed.csv" }
        end
      else
        redirect_to "#{Settings.hostname}/file_not_ready"
      end
    else
      redirect_to "#{Settings.hostname}/file_not_ready"
    end
  end

  def sample_file
    current_shop = User.where(public_token: params[:api_token]).take
    profile = params[:id] ? current_shop.user_profiles.find(params[:id]) : current_shop.user_profiles.first
    xml = if current_shop && profile
      (profile.source_file && File.extname(profile.source_file_file_name).delete(".").downcase == "xml") ? profile.source_file : nil
    end

    respond_to do |format|
      format.xml { send_data Paperclip.io_adapters.for(xml).read, filename: "sample.xml", disposition: "inline" }
    end
  end
end
