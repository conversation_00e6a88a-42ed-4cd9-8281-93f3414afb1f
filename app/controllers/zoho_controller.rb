class ZohoController < ConnectionsController
  skip_before_action :authenticate_account!, only: [:callback]

  def authorize
    user_profile = UserProfile.where(id: params[:profile_id]).last

    query = {
      scope: Settings.zoho.scope,
      client_id: ENV["ZOHO_CLIENT_ID"],
      state: user_profile.id,
      redirect_uri: callback_zoho_index_url,
      response_type: "code",
      access_type: "offline",
      prompt: "Consent"
    }

    uri = URI.parse("https://accounts.zoho.com/oauth/v2/auth")
    uri.query = query.to_query

    redirect_to uri.to_s
  end

  def callback
    code = params[:code]
    if code
      body = {
        code: code,
        client_id: ENV["ZOHO_CLIENT_ID"],
        client_secret: ENV["ZOHO_CLIENT_SECRET"],
        scope: Settings.zoho.scope,
        state: params[:state],
        redirect_uri: callback_zoho_index_url,
        grant_type: "authorization_code"
      }

      uri = URI.parse("https://accounts.zoho.com/oauth/v2/token")
      uri.query = body.to_query

      response = HTTParty.post(uri.to_s).parsed_response
      profile = UserProfile.find_by(id: params[:state])

      if response["access_token"] && profile
        profile.xero_access_token = response["access_token"]
        profile.xero_refresh_token = response["refresh_token"]
        profile.oauth_granted = true
        profile.oauth_granted_to = "zoho_inventory"
        profile.source_type = "zoho_inventory"
        profile.save
      end
    end

    redirect_to UserProfile.feed_manager_link(params[:state], profile&.user)
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.xero_access_token = nil
    user_profile.xero_refresh_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
