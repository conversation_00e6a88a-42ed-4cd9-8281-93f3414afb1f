class OmniauthCallbacksController < Devise::OmniauthCallbacksController
  skip_before_action :verify_authenticity_token
  include BigcommerceDecoder

  def shopify
    params.permit!
    params.merge!(JSON.parse(Faraday.post("https://#{params[:shop]}/admin/oauth/access_token?client_id=#{ShopifyApp.configuration.api_key}&client_secret=#{ShopifyApp.configuration.secret}&code=#{params[:code]}").body))

    @user, @account = User.from_omniauth(auth_hash)

    if @user.persisted?
      params[:associated_user] = @user.id
      setup_shopify_session
      # sign_in_and_redirect @account, event: :authentication
      sign_in @account, event: :authentication
      redirect_to "/?force_standalone=true"
    else
      raise StandardError.new("Failed shopify auth callback")
    end
  rescue => e
    session["devise.omniauth_data"] = auth_hash
    Rails.logger.error @user.errors
    Rails.logger.error e
    redirect_to :root
  end

  def woocommerce
    if params[:key_id]
      @user = User.find(params[:user_id])
      @user.woocommerce_key = params[:consumer_key]
      @user.woocommerce_secret = params[:consumer_secret]
      @user.status = "trial"
      @user.uninstalled_at = nil

      currency = WoocommerceClient.new(@user).get_currency_info
      @user.currency = currency["code"] if currency && currency["code"].present? && currency["symbol"].present?
      @user.save(validate: false)

      account = Account.find_by(shop_id: @user.id)
      sign_in_and_redirect account, event: :authentication
      # head :ok
    end
  end

  def bigcommerce
    url = "https://login.bigcommerce.com/oauth2/token"
    payload = {
      client_id: ENV["BIGCOMMERCE_CLIENT_ID"],
      client_secret: ENV["BIGCOMMERCE_CLIENT_SECRET"],
      code: params[:code],
      scope: params[:scope],
      grant_type: "authorization_code",
      redirect_uri: auth_bigcommerce_callback_url,
      context: params[:context]
    }
    Rails.logger.info "BC OmniauthCallbacksController payload: #{payload}"
    resp = Faraday.post(url, payload.to_json, "Content-Type" => "application/json")
    Rails.logger.info "BC OmniauthCallbacksController Res: #{resp}"
    if resp.status == 200
      Rails.logger.info "BC OmniauthCallbacksController 200"
      result = JSON.parse(resp.body, object_class: OpenStruct)
      @user, @account = Shop::Bigcommerce.from_callback(result)

      if @user.persisted?
        cookies[:stocksync_current_store] = @user.bigcommerce_domain
        sign_in_and_redirect @account, event: :authentication
      else
        Rails.logger.info "BC OmniauthCallbacksController not persisted"
        redirect_to :root
      end
    else
      Rails.logger.info "BC OmniauthCallbacksController not 200"
      redirect_to :root
    end
  end

  def bigcommerce_signin
    payload = decode_signed_payload(params[:signed_payload])
    store_hash = payload["store_hash"]

    store = Shop::Bigcommerce.find_by(bigcommerce_store_hash: store_hash)

    if store
      account = Account.where(email: payload["user"]["email"], shop_id: store.id).first_or_initialize do |account|
        account.password = Devise.friendly_token[0, 20]
        account.save
      end
      cookies[:stocksync_current_store] = store.bigcommerce_domain
      sign_in_and_redirect account, event: :authentication
    end
  end

  def wix
    token = params[:token]
    redirect_to "https://www.wix.com/installer/install?appId=#{Settings.wix.client_id}&redirectUrl=#{Settings.wix.redirect_url}&token=#{token}", allow_other_host: true
  end

  def wix_signup
    code = params[:code]
    instance_id = params[:instanceId]
    tokens = wix_tokens(code)
    tokens.instance_id = instance_id
    @user, @account = Shop::Wix.from_callback(tokens)
    if @user.persisted?
      cookies[:stocksync_current_store] = @user.wix_domain
      sign_in_and_redirect @account, event: :authentication
    else
      redirect_to :root
    end
  end

  def wix_signin
    decode_data do |user|
      if user
        cookies[:stocksync_current_store] = user.wix_domain
        sign_in_and_redirect user.account.first, event: :authentication
      else
        redirect_to :root
      end
    end
  end

  def wix_pricing_page
    if decode_data
      decode_data do |user|
        if user
          cookies[:stocksync_current_store] = user.wix_domain
          redirect_to "/billing"
        end
      end
    else
      @plans = Plan.non_customized.where(group: ["starter", "basic", "enterprise"], version: Settings.latest_plan_version, monthly_credit_plan: false).where.not(key: ["Snappy", "Trial"]).published
      render "integrations/wix/pricing_page"
    end
  end

  def square
    public_store_token = params[:state].split("_").last
    if params[:error] && params[:error] == "access_denied"
      redirect_to new_account_session_url, flash: {error: "Unauthorized. Please try again."} and return
    end

    result = SquareClient.app_client.o_auth.obtain_token(
      body: {
        client_id: ENV["SQUARE_APP_ID"],
        client_secret: ENV["SQUARE_APP_SECRET"],
        code: params[:code],
        grant_type: "authorization_code"
      }
    )

    if result.success?
      access_token = result.data.access_token
      merchant_id = result.data.merchant_id
      refresh_token = result.data.refresh_token

      user = Shop::Square.find_by(public_token: public_store_token)
      if user
        user.uninstalled_at = nil

        user.status = "trial" if user.status == "inactive"
        user.square_merchant_id = merchant_id
        user.square_access_token = access_token
        user.square_refresh_token = refresh_token
        user = Shop::Square.update_business_info(user)
        user.save(validate: false)

        account = user.account.last
        cookies[:auth_message] = {value: "Success", max_age: 5.seconds}
        cookies[:stocksync_current_store] = user.square_domain
        sign_in_and_redirect account, event: :authentication
      else
        # redirect to sign up form along with auth result
        render "integrations/register_square"
      end
    elsif result.error?
      flash[:error] = result.errors
      redirect_to square_integrations_path
    end
  end

  def ekm
    # redirect_to :root
    code = params[:code]
    @user, @account = Shop::Ekm.from_callback(code)

    if @user.persisted?
      sign_in_and_redirect @account, event: :authentication
    else
      redirect_to :root
    end
  end

  def squarespace
    redirect_to :root if params[:error] == "access_denied"
    code = params[:code]
    @user, @account = Shop::Squarespace.from_callback(code)

    if @user.persisted?
      sign_in_and_redirect @account, event: :authentication
    else
      redirect_to :root
    end
  end

  def quickbooks
    if params[:state].present?
      redirect_uri = Settings.quickbooks_platform.redirect_uri
      if (resp = QuickbooksConfig.oauth_client.auth_code.get_token(params[:code], redirect_uri: redirect_uri))
        @user, @account = Shop::Quickbooks.from_callback({access_token: resp.token, refresh_token: resp.refresh_token, realm_id: params[:realmId], state: params[:state]})

        if @user.nil? && @account.nil?
          redirect_to new_account_session_url, flash: {error: "Email is not verified. Please go to https://accounts.intuit.com/app/account-manager/security to verify your account."} and return
        end

        if @user.persisted?
          cookies[:stocksync_current_store] = @user.shopify_domain
          sign_in_and_redirect @account, event: :authentication
        else
          redirect_to :root
        end
      else
        redirect_to new_account_session_url, flash: {error: "Unauthorized. Please try again."} and return
      end
    end
  rescue OAuth2::Error
    redirect_to new_account_session_url, flash: {error: "Unauthorized. Please try again."} and return
  end

  private

  def auth_hash
    params
  end

  def shop_name
    auth_hash[:shop]
  end

  def token
    auth_hash[:access_token]
  end

  def associated_user
    auth_hash[:associated_user]
  end

  def setup_shopify_session
    return false unless auth_hash

    login_shop
    # scriptags deprecated, webhook setup move config toml, coupon code deprecated
    # install_webhooks
    # install_scripttags
    # perform_after_authenticate_job
  end

  def login_shop
    request.session_options[:renew] = true
    session.delete(:_csrf_token)

    session[:shop_id] = ShopifyApp::SessionRepository.store_shop_session(
      ShopifyAPI::Auth::Session.new(
        shop: shop_name,
        access_token: token
      )
    )
    session[:shopify_domain] = shop_name
    session[:shopify_user] = associated_user if associated_user.present?
  end

  # def install_webhooks
  #   return unless ShopifyApp.configuration.has_webhooks?

  #   ShopifyApp::WebhooksManager.queue(
  #     shop_name,
  #     token,
  #     ShopifyApp.configuration.webhooks
  #   )
  # end

  # def install_scripttags
  #   return unless ShopifyApp.configuration.has_scripttags?

  #   ShopifyApp::ScripttagsManager.queue(
  #     shop_name,
  #     token,
  #     ShopifyApp.configuration.scripttags
  #   )
  # end

  # def perform_after_authenticate_job
  #   config = ShopifyApp.configuration.after_authenticate_job

  #   return unless config && config[:job].present?

  #   if config[:inline] == true
  #     config[:job].perform_now(session: session)
  #   else
  #     config[:job].perform_later(session: session)
  #   end
  # end

  def wix_tokens(code)
    url = "https://www.wix.com/oauth/access"
    payload = {
      client_id: ENV["WIX_CLIENT_ID"],
      client_secret: ENV["WIX_CLIENT_SECRET"],
      code: code,
      grant_type: "authorization_code"
    }
    resp = Faraday.post(
      url,
      payload.to_json,
      "Content-Type" => "application/json"
    )
    JSON.parse(resp.body, object_class: OpenStruct)
  end

  def decode_data
    public_key = Settings.wix.public_key
    signed_instance = params[:instance]

    raise "Missing signed instance" if signed_instance.blank? || signed_instance == "undefined"

    signature, encoded_json = signed_instance&.split(".", 2)

    raise "Invalid signature." if signature.blank?
    raise "Invalid encoded JSON." if encoded_json.blank?

    # Need to add Base64 padding to make base64 decode work in Ruby. (ref: http://stackoverflow.com/questions/4987772/decoding-facebooks-signed-request-in-ruby-sinatra)
    begin
      encoded_json_hack = encoded_json + ("=" * (4 - encoded_json.length.modulo(4)))
      json_str = Base64.decode64(encoded_json_hack)
      hmac = OpenSSL::HMAC.digest(OpenSSL::Digest.new("SHA256"), public_key, encoded_json)

      # bug in ruby. why are there '=' chars on urlsafe_encode ?!
      _my_signature = Base64.urlsafe_encode64(hmac).delete("=")

      # raise "the signatures do not match" if (signature != my_signature)

      data = JSON.parse(json_str)
      user = Rails.env.development? ? Shop::Wix.first : Shop::Wix.find_by(wix_code: data["instanceId"])
      yield(user, data)
    rescue => e
      Rails.logger.error(e.message)
      Airbrake.notify(e, params: params) unless e.message == "Missing signed instance"
      redirect_to "https://www.wix.com/app-market/stock-sync"
    end
  end
end
