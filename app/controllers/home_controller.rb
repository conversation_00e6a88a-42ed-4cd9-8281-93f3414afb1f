class HomeController < AuthenticatedController
  before_action :authenticate_account!, except: ["pricing", "pricing_plans", "download_products", "download_updated_products", "download_fast_track_products", "get_price", "flexi_charge_callback", "callback_charge", "custom_callback_charge"]
  before_action :create_shopify_session, except: ["pricing", "pricing_plans", "download_products", "download_updated_products", "download_fast_track_products", "get_price", "flexi_charge_callback", "callback_charge", "custom_callback_charge"]
  before_action :redirect_authentication, except: ["pricing", "pricing_plans", "download_products", "download_updated_products", "download_fast_track_products", "get_price", "flexi_charge_callback", "callback_charge", "custom_callback_charge"]

  protect_from_forgery except: [:get_price, :callback_charge, :flexi_charge_callback, :custom_callback_charge]

  include CustomPlanValidator

  def pricing
    # Load React unauthenticated v2 pricing page
  end

  def pricing_plans
    # Used in public pricing page
    plans = Rails.cache.fetch("public_pricing_plans", expires_in: 1.month) do
      plans = Plan.non_customized.where(version: Settings.latest_plan_version).order("price asc").map { |plan| plan.attributes.except("version", "group", "published") }
      plans.each do |plan|
        plan.transform_keys! { |key| key.camelize(:lower) }
        plan["hidden"] = plan["key"] == "Trial"
        plan["schedulePerDay"] = begin
          24 / plan["minHour"]
        rescue
          0
        end
      end
      plans
    end

    render json: plans
  end

  def get_price
    packages = []
    Settings.packages.each do |package|
      packages << {name: package.key, price: package.price, populate: package.popular, features: [
        "Store when less than #{package.limit} SKU", "Up to #{package.source_limit} external data feeds", "Runs #{package.schedule}"
      ]}
    end
    render js: "#{params[:callback]}(' #{{"free_trial_days" => Settings.free_trial_days.to_s, "packages" => packages}.to_json}')"
  end

  # Needed in ActiveAdmin
  def download_products
    shop = User.where(public_token: params[:token]).take
    respond_to do |format|
      format.csv { send_data shop ? shop.download_product_csv(params[:fields], params[:location_id]) : "Invalid token", filename: "product-list.csv" }
    end
  end

  # Needed in snappy inventory update
  def download_fast_track_products
    shop = User.where(public_token: params[:token]).take

    respond_to do |format|
      format.csv { send_data shop.download_fast_track_products_csv, filename: "products.csv" }
    end
  end

  # handle shopify credit purchase without SS session
  def flexi_charge_callback
    public_token = params[:public_token]
    shopify_shop = Shop::Shopify.where(public_token: public_token).take
    shopify_shop.handle_flexi_charge_callback(params)
    redirect_to "/login?shop=#{shopify_shop.shopify_domain}"
  end

  def callback_charge
    public_token = params[:public_token]
    shopify_shop = Shop::Shopify.where(public_token: public_token).take

    subscription_log = StockSyncPurchaseLog.new(store_domain: shopify_shop.try(:shopify_domain), raw_params: params.to_json)
    params[:charge_id] ||= Rails.cache.fetch("#{shopify_shop.try(:id)}_charge")
    Rails.cache.delete("#{shopify_shop.try(:id)}_charge")

    if params[:charge_id]
      annually = params[:annually] == "true"
      charge = ::ShopifyAPI::LightGraphQL.get_billing(shopify_shop.shopify_domain, shopify_shop.shopify_token, params[:charge_id])
      subscription_log.shopify_charge = charge.to_json if charge
      Rails.logger.info "charge: #{charge.inspect}"

      shopify_shop&.handle_subscription_event(params[:charge_id], charge["status"].downcase, nil, annually, params[:package])
    end

    subscription_log.save
    redirect_to "/login?shop=#{shopify_shop.shopify_domain}"
  end

  def custom_callback_charge
    public_token = params[:public_token]
    shopify_shop = Shop::Shopify.where(public_token: public_token).take

    subscription_log = StockSyncPurchaseLog.new(store_domain: shopify_shop.try(:shopify_domain), raw_params: params.to_json)
    params[:charge_id] ||= Rails.cache.fetch("#{shopify_shop.try(:id)}_charge")
    Rails.cache.delete("#{shopify_shop.try(:id)}_charge")

    if params[:charge_id]
      annually = params[:annually] == "true"
      charge = ::ShopifyAPI::LightGraphQL.get_billing(shopify_shop.shopify_domain, shopify_shop.shopify_token, params[:charge_id])

      subscription_log.shopify_charge = charge.to_json if charge
      Rails.logger.info "charge: #{charge.inspect}"

      shopify_shop&.handle_subscription_event(params[:charge_id], charge["status"].downcase, nil, annually, params[:plan_id], true)
    end

    subscription_log.save
    redirect_to "/login?shop=#{shopify_shop.shopify_domain}"
  end
end
