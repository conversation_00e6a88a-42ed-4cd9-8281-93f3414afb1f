class EtsyController < ConnectionsController
  def authorize
    code = params[:code]
    user_profile_id = params[:state]

    code_verifier = RedisManager.get("etsy_#{user_profile_id}")

    payload = {
      grant_type: "authorization_code",
      client_id: ENV["ETSY_API_KEY"],
      redirect_uri: File.join(Settings.webhook_host, "/etsy/authorize"),
      code: code,
      code_verifier: code_verifier
    }
    response = Faraday.post("https://api.etsy.com/v3/public/oauth/token") do |req|
      req.headers["Content-Type"] = "application/x-www-form-urlencoded"
      req.body = URI.encode_www_form(payload)
    end

    response = JSON.parse(response.body)
    user_profile = UserProfile.find_by(id: user_profile_id)

    if response.is_a?(Hash) && response.has_key?("access_token") && user_profile
      user_profile.etsy_token = response.fetch("access_token")
      user_profile.etsy_secret = response.fetch("refresh_token")
      user_profile.oauth_granted = true
      user_profile.oauth_granted_to = "etsy"
      user_profile.source_type = "etsy"
      user_profile.save

      redirect_to UserProfile.feed_manager_link(user_profile_id, user_profile.user), notice: "Etsy authorize access successfully. Please proceed by clicking 'Next'"
    else
      redirect_to UserProfile.feed_manager_link(user_profile_id, user_profile.user), error: "Etsy authorize access failed."
    end
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.etsy_token = nil
    user_profile.etsy_secret = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
