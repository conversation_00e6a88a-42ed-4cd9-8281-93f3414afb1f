class AuthenticatedController < ApplicationController
  include Devise::Controllers::Helpers
  include ShopifyApp::LoginProtection

  before_action :redirect_authentication
  before_action :create_shopify_session
  before_action :authenticate_account!
  before_action :relogin_if_different_store
  before_action :check_if_store_validated
  after_action :set_locale
  # before_action :check_wix_permissions

  class DuplicateUserDetectedError < StandardError; end

  private

  def current_shop
    return nil unless account_signed_in?
    current_account.shop
  end

  def is_embed
    params[:embedded] == "1" && params[:id_token].present?
  end

  def redirect_authentication
    tries = 0

    begin
      Rails.logger.info("#{current_shop&.shopify_domain} Auth redirect_authentication first")
      if is_embed
        Rails.logger.info("#{current_shop&.shopify_domain} Auth redirect_authentication is embed")

        sign_out(current_account) if account_signed_in?
        id_token = params[:id_token]
        user, account, is_fresh_store = Shop::Shopify.from_jwt_callback(id_token)
        redirect_to request.url and return if user.nil? && account.nil? && is_fresh_store == false
        Rails.logger.info("#{user&.shopify_domain} Auth redirect_authentication is after jwt")
        user.handle_after_validate_before_signin(request, session, is_fresh_store)
        Rails.logger.info("#{user&.shopify_domain} Auth redirect_authentication is after validate")

        sign_in(account)
      else
        Rails.logger.info("#{current_shop&.shopify_domain} Auth redirect_authentication is NOT embed")
        return if params[:shop].nil?
        return if params[:shop] == "undefined"
        return if account_signed_in? && current_shop.shopify_domain == params[:shop]
        redirect_to login_url_with_optional_shop
      end
    rescue DuplicateUserDetectedError => e
      tries += 1
      if tries < 10
        sleep 1.seconds
        retry
      else
        raise e
      end
    rescue => e
      Rails.logger.info("#{current_shop&.shopify_domain} Auth redirect_authentication error #{e.message}")
      raise e
    end
  end

  def create_shopify_session
    Rails.logger.info("#{current_shop&.shopify_domain} Auth create_shopify_session first")
    return unless account_signed_in?
    # ? qb, wc and ps store should can access even if its uninstalled
    sign_out(current_account) if current_account&.shop&.uninstalled_at.present? &&
      ["quickbooks", "woocommerce", "prestashop"].exclude?(current_account&.shop&.provider)

    if is_embed && current_shop
      Rails.logger.info("#{current_shop&.shopify_domain} Auth create_shopify_session is embed")
      cookies[:stocksync_current_store] = current_shop.shopify_domain
      return
    end

    Rails.logger.info("#{current_shop&.shopify_domain} Auth create_shopify_session is not embed")

    if current_shop
      if current_shop.provider == "shopify"
        if ShopifyClient.new(current_shop).valid_user?
          cookies[:stocksync_current_store] = current_shop.shopify_domain
        else
          shopify_domain = current_shop.shopify_domain
          if shopify_domain && account_signed_in?
            sign_out(current_account)
            redirect_to "/auth/shopify?shop=#{shopify_domain}"
          else
            redirect_to login_url_with_optional_shop
          end
        end
      end
    else
      redirect_to new_account_session_url
    end
  end

  def relogin_if_different_store
    Rails.logger.info("#{current_shop&.shopify_domain} Auth relogin_if_different_store first")
    return false unless params[:shop]
    Rails.logger.info("#{current_shop&.shopify_domain} Auth relogin_if_different_store 2")
    shop = User.where(shopify_domain: params[:shop]).take
    return false unless shop
    Rails.logger.info("#{current_shop&.shopify_domain} Auth relogin_if_different_store 3")
    # TODO: only applies to Shopify. For bigcommerce, get query based on email and shop_id
    account = Account.find_by(shop_id: shop.id)
    if account_signed_in? && current_shop && current_shop.shopify_domain != params[:shop]
      Rails.logger.info("#{current_shop&.shopify_domain} Auth relogin_if_different_store 4")
      sign_out(current_account)
      sign_in(account)
    end
  end

  def set_locale
    cookies[:lang] = current_shop.locale.present? ? current_shop.locale : "en" if current_shop
  end

  def check_if_store_validated
    return unless account_signed_in?
    return unless current_shop.provider == "woocommerce"
    if current_shop.woocommerce_key.nil? && current_shop.woocommerce_secret.nil?
      endpoint = "/wc-auth/v1/authorize"
      param = {
        app_name: "syncX: Stock Sync",
        scope: "read_write",
        user_id: current_shop.id,
        return_url: woocommerce_session_integrations_url,
        callback_url: auth_woocommerce_callback_url
      }
      query_string = URI.encode_www_form(param)
      oauth_url = "#{current_shop.woocommerce_protocol}://#{current_shop.woocommerce_domain}#{endpoint}?#{query_string}"
      redirect_to oauth_url, allow_other_host: true
    else
      # Check if WooCommerce credentials are valid - cache the result?
      status, _error_message = WoocommerceClient.new(current_shop).valid_user?
      status
    end
  end

  # TODO: Remove this code once all active Wix users have accepted the new scope
  def check_wix_permissions
    return unless current_shop && current_shop.provider == "wix"

    store_info = WixClient.new(current_shop).store_info
    permissions = store_info.dig("instance", "permissions")
    required_parent_scopes = Settings.wix.required_parent_scopes
    missing_parent_scopes = required_parent_scopes.reject do |parent_scope|
      permissions&.any? { |perm| perm.start_with?("#{parent_scope}.") }
    end

    if missing_parent_scopes.any?
      meta_site_id = store_info.dig("site", "siteId")

      return if meta_site_id.blank?

      installer_url = "https://www.wix.com/installer/install"
      installer_params = {
        metaSiteId: meta_site_id,
        appId: ENV["WIX_CLIENT_ID"],
        redirectUrl: Settings.wix.redirect_url
      }

      action_url = "#{installer_url}?#{installer_params.to_query}"
      redirect_to action_url, allow_other_host: true, alert: "Media permission is required for this app."
    end
  end
end
