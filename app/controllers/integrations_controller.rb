require "uri"

class IntegrationsController < ApplicationController
  include ShopifyApp::LoginProtection
  protect_from_forgery

  UNMATCHED_PASSWORD = "The passwords do not match. Please try again."
  STORE_FOUND = "This store has been registered. Please sign in instead."
  STORE_NOT_FOUND = "Store does not exist. Create an account to continue."
  INCORRECT_CREDENTIALS = "Incorrect credentials. Please try again."
  MISSING_SHOP_PARAMS = "You must enter your shop domain"
  INVALID_STORE_URL = "The store URL is not valid."
  INVALID_EMAIL = "The email is not valid."
  EMAIL_FOUND = "This email has been registered. Please sign in instead."

  def shopify
  end

  def shopify_authorize
    if params[:shop].present?
      if params[:shop].include?(".myshopify.com")
        params[:shop].gsub!(/https?:\/\//, "")
        params[:shop].gsub!(/(\.com).*$/i, '\1')
        redirect_to "/auth/shopify?shop=#{params[:shop]}"
      else
        flash[:error] = INVALID_STORE_URL
        redirect_back fallback_location: root_path
        nil
      end
    else
      flash[:error] = MISSING_SHOP_PARAMS
      redirect_back fallback_location: root_path
    end
  end

  def woocommerce
  end

  def quickbooks
  end

  def register_woocommerce
  end

  # from woocommerce sign up page
  def woocommerce_validate
    if account_creation_params[:password] != account_creation_params[:password_confirmation]
      flash[:error] = UNMATCHED_PASSWORD
      redirect_back fallback_location: root_path
      return
    end

    store_url = account_creation_params[:shop].gsub(/(https?:\/\/)/, "")
    url = if account_creation_params[:protocol].blank?
      "http://#{store_url}"
    else
      "https://#{store_url}"
    end

    domain = URI.parse(url)
    woocommerce_host = domain.host
    woocommerce_host = woocommerce_host.chomp("/") # remove last "/" if any

    unless woocommerce_host
      flash[:error] = MISSING_SHOP_PARAMS
      redirect_back fallback_location: root_path
      return
    end

    if !User.is_valid_domain?(url) || [".myshopify.com", ".mybigcommerce.com", "wixsite.com", "ekm.shop", "squarespace.com"].any? { |site| woocommerce_host.index(site) }
      flash[:error] = INVALID_STORE_URL
      redirect_back fallback_location: root_path
      return
    end

    user = Shop::Woocommerce.where(woocommerce_domain: woocommerce_host.downcase).first_or_initialize do |user|
      user.email = account_creation_params[:email]
      user.notification_email = account_creation_params[:email]
      user.password = Devise.friendly_token[0, 20]
      user.provider = "woocommerce"
      user.woocommerce_protocol = (domain.scheme || account_creation_params[:protocol])
      user.installed_date = Time.now
      user.status = "inactive" # only activate if user acceps the auth
    end

    user.uninstalled_at = nil
    if user.new_record?
      user.email_subscriptions << "daily_summary" unless user.email_subscribed_to?(:daily_summary)
      user.email_subscriptions << "sync_failure" unless user.email_subscribed_to?(:sync_failure)
      user.save(validate: false)
      acc = Account.new(email: user.email, shop_id: user.id, password: account_creation_params[:password])
      acc.save!

      endpoint = "/wc-auth/v1/authorize"
      param = {
        app_name: "syncX: Stock Sync",
        scope: "read_write",
        user_id: user.id,
        return_url: woocommerce_session_integrations_url,
        callback_url: auth_woocommerce_callback_url
      }
      query_string = URI.encode_www_form(param)
      oauth_url = "#{user.woocommerce_protocol}://#{user.woocommerce_domain}#{endpoint}?#{query_string}"

      redirect_to oauth_url, allow_other_host: true
    else
      flash[:error] = STORE_FOUND
      redirect_to woocommerce_integrations_path
      nil
    end
  end

  # from woocommerce sign in page
  def woocommerce_authorize
    store_url = woocommerce_login_params[:shop]&.strip&.downcase
    store_url = store_url.gsub(/(https?:\/\/)/, "")
    url = if woocommerce_login_params[:protocol].blank?
      "http://#{store_url}"
    else
      "https://#{store_url}"
    end
    woocommerce_host = URI.parse(url).host&.downcase
    store = Shop::Woocommerce.find_by(woocommerce_domain: woocommerce_host) || Shop::Woocommerce.find_by(woocommerce_domain: store_url)

    if store
      account = Account.find_by(shop_id: store.id)
      if account.valid_password?(woocommerce_login_params[:password])
        cookies[:stocksync_current_store] = account.shop.woocommerce_domain
        sign_in_and_redirect account, event: :authentication
      else
        flash[:error] = INCORRECT_CREDENTIALS
        redirect_back fallback_location: root_path
      end
    else
      flash[:error] = STORE_NOT_FOUND
      redirect_back fallback_location: root_path
    end
  end

  # redirected here after successful woocommerce sign up
  def woocommerce_session
    if params["success"]
      account = Account.find_by(shop_id: params["user_id"])
      cookies[:stocksync_current_store] = account.shop.woocommerce_domain
      sign_in_and_redirect account, event: :authentication
    end
  end

  def bigcommerce
    redirect_to "https://login.bigcommerce.com/app/#{ENV["BIGCOMMERCE_CLIENT_ID"]}/install", allow_other_host: true if Rails.env.production?
  end

  def bigcommerce_authorize
    if Rails.env.development?
      user = Shop::Bigcommerce.find_by(bigcommerce_domain: params[:shop])
      account = Account.find_by(shop_id: user.id)
      sign_in_and_redirect account, event: :authentication
    end
  end

  # For woocommerce only
  def reset_password
    shop_url = reset_password_params[:shop]

    if shop_url.present?
      begin
        store_host = URI.parse(shop_url).host
      rescue URI::InvalidURIError
        store_host = shop_url
      end
    else
      store_host = nil
    end

    store_host ||= shop_url

    case reset_password_params[:platform]&.downcase
    when "woocommerce"
      store = Shop::Woocommerce.find_by(woocommerce_domain: store_host)
    when "square"
      store = Shop::Square.find_by(square_domain: store_host)
    when "quickbooks"
      store = Shop::Quickbooks.find_by(quickbooks_domain: store_host)
    when "prestashop"
      store = Shop::Prestashop.find_by(prestashop_domain: store_host)
    end

    if store
      account = Account.find_by(shop_id: store.id)
      token, hashed = Devise.token_generator.generate(Account, :reset_password_token)
      account.reset_password_token = hashed
      account.reset_password_sent_at = Time.now
      account.save

      UserMailer.reset_password(token, store).deliver
    end
    flash[:success] = "If your store is registered with us, an email will be sent to you soon to reset your password"
    redirect_back fallback_location: root_path
  end

  def wix
    redirect_to "https://www.wix.com/installer/install?appId=#{ENV["WIX_CLIENT_ID"]}&redirectUrl=#{Settings.wix.redirect_url}", allow_other_host: true
  end

  def squarespace
    redirect_to "https://login.squarespace.com/api/1/login/oauth/provider/authorize?client_id=#{ENV["SQUARESPACE_CLIENT_ID"]}&redirect_uri=#{Settings.squarespace.redirect_url}&state=#{SecureRandom.alphanumeric}&access_type=offline&scope=#{Settings.squarespace.scope}", allow_other_host: true
  end

  def square
  end

  # from woocommerce sign in page
  def square_authorize
    # ?regex check if in the param has https or http
    store_url = square_login_params[:shop].gsub(/(https?:\/\/)/, "")
    url = if square_login_params[:protocol].blank?
      "http://#{store_url}"
    else
      "https://#{store_url}"
    end

    domain = URI.parse(url)

    square_host = domain.host

    store = Shop::Square.find_by(square_domain: square_host)

    if store
      account = Account.find_by(shop_id: store.id)
      if account.valid_password?(square_login_params[:password])

        # ? handle user reinstalled the app
        client = SquareClient.new(store)
        if client&.get_merchant_info&.errors&.any?
          redirect_to "#{Settings.square.base_url}/oauth2/authorize?client_id=#{ENV["SQUARE_APP_ID"]}&scope=#{Settings.square.scope}&session=false&state=#{SecureRandom.urlsafe_base64(43)}_#{store.public_token}", allow_other_host: true
        else
          cookies[:stocksync_current_store] = account.shop.square_domain
          sign_in_and_redirect account, event: :authentication
        end
      else
        flash[:error] = INCORRECT_CREDENTIALS
        redirect_back fallback_location: root_path
      end
    else
      flash[:error] = STORE_NOT_FOUND
      redirect_back fallback_location: root_path
    end
  end

  def register_square
  end

  # from square sign up page
  def square_validate
    if account_creation_params[:password] != account_creation_params[:password_confirmation]
      flash[:error] = UNMATCHED_PASSWORD
      redirect_back fallback_location: root_path
      return
    end

    store_url = account_creation_params[:shop].gsub(/(https?:\/\/)/, "")
    url = if account_creation_params[:protocol].blank?
      "http://#{store_url}"
    else
      "https://#{store_url}"
    end

    domain = URI.parse(url)
    square_host = domain.host

    unless square_host
      flash[:error] = MISSING_SHOP_PARAMS
      redirect_back fallback_location: root_path
      return
    end

    if !User.is_valid_domain?(url) || [".myshopify.com", ".mybigcommerce.com", "wixsite.com", "ekm.shop", "squarespace.com"].any? { |site| square_host.index(site) }
      flash[:error] = INVALID_STORE_URL
      redirect_back fallback_location: root_path
      return
    end

    user = Shop::Square.where(square_domain: square_host).first_or_initialize do |user|
      user.email = account_creation_params[:email]
      user.notification_email = account_creation_params[:email]
      user.password = Devise.friendly_token[0, 20]
      user.provider = "square"
      user.installed_date = Time.now
      user.status = "inactive" # only activate if user acceps the auth
    end

    if user.new_record?
      user.email_subscriptions << "daily_summary" unless user.email_subscribed_to?(:daily_summary)
      user.email_subscriptions << "sync_failure" unless user.email_subscribed_to?(:sync_failure)
      user.save(validate: false)
      acc = Account.new(email: user.email, shop_id: user.id, password: account_creation_params[:password])
      acc.save!

      # Trigger Square OAuth
      redirect_to "#{Settings.square.base_url}/oauth2/authorize?client_id=#{ENV["SQUARE_APP_ID"]}&scope=#{Settings.square.scope}&session=false&state=#{SecureRandom.urlsafe_base64(43)}_#{user.public_token}", allow_other_host: true
    else
      flash[:error] = STORE_FOUND
      redirect_to square_integrations_path
      nil
    end
  end

  def ekm
    redirect_to "https://api.ekm.net/connect/authorize?client_id=#{ENV["EKM_CLIENT_ID"]}&scope=#{Settings.ekm.scope}&response_type=code&redirect_uri=#{Settings.ekm.redirect_url}", allow_other_host: true
  end

  def register_quickbooks
  end

  def quickbooks_validate
    if account_creation_params[:password] != account_creation_params[:password_confirmation]
      flash[:error] = UNMATCHED_PASSWORD
      redirect_back fallback_location: root_path
      return
    end

    unless URI::MailTo::EMAIL_REGEXP.match?(account_creation_params[:email])
      flash[:error] = INVALID_EMAIL
      redirect_back fallback_location: root_path
      return
    end

    unless Shop::Quickbooks.where(email: account_creation_params[:email]).empty?
      flash[:error] = EMAIL_FOUND
      redirect_to register_quickbooks_path
      nil
    end

    Shop::Quickbooks.where(email: account_creation_params[:email]).first_or_initialize do |user|
      user.email = account_creation_params[:email]
      user.notification_email = account_creation_params[:email]
      user.quickbooks_domain = "QuickBooksOnline"
      user.password = Devise.friendly_token[0, 20]
      user.provider = "quickbooks"
      user.installed_date = Time.now
      user.status = "inactive" # only activate if user acceps the auth
      user.email_subscriptions << "daily_summary" unless user.email_subscribed_to?(:daily_summary)
      user.email_subscriptions << "sync_failure" unless user.email_subscribed_to?(:sync_failure)
      user.save(validate: false)
      cookies[:stocksync_current_store] = user.quickbooks_domain

      account = Account.where(email: user.email, shop_id: user.id).first_or_initialize do |acc|
        acc.password = account_creation_params[:password]
        acc.save
      end

      sign_in_and_redirect account, event: :authentication
    end
  end

  def quickbooks_authorize
    store = Shop::Quickbooks.find_by(email: quickbooks_login_params[:email])

    if store
      account = Account.find_by(shop_id: store.id)
      if account&.valid_password?(quickbooks_login_params[:password])
        cookies[:stocksync_current_store] = account.shop.quickbooks_domain
        sign_in_and_redirect account, event: :authentication
      else
        flash[:error] = INCORRECT_CREDENTIALS
        redirect_back fallback_location: root_path
      end
    else
      flash[:error] = STORE_NOT_FOUND
      redirect_back fallback_location: root_path
    end
  end

  def quickbooks_authenticate
    state = SecureRandom.alphanumeric
    redirect_uri = Settings.quickbooks_platform.redirect_uri
    grant_url = QuickbooksConfig.oauth_client.auth_code.authorize_url(redirect_uri: redirect_uri, response_type: "code", state: state, scope: "com.intuit.quickbooks.accounting openid profile email phone address")
    redirect_to grant_url, allow_other_host: true
  end

  def intuit_authorize
    scopes = [IntuitOAuth::Scopes::OPENID, IntuitOAuth::Scopes::EMAIL, IntuitOAuth::Scopes::PROFILE]
    authorize_url = QuickbooksConfig.intuit_client.code.get_auth_uri(scopes)
    redirect_to authorize_url, allow_other_host: true
  end

  def prestashop
  end

  def register_prestashop
  end

  def prestashop_authorize
    shop = prestashop_login_params[:shop].strip
    protocol = prestashop_login_params[:protocol]

    domain = URI.parse(protocol + "://" + shop)
    prestashop_protocol = domain.scheme || prestashop_login_params[:protocol]
    prestashop_host = domain.host || prestashop_login_params[:shop]
    prestashop_host = prestashop_host.chomp("/") # remove last "/" if any
    prestashop_path = domain.path
    port = domain.port

    prestashop_port = if prestashop_protocol == "https" && port == 443
      ""
    elsif prestashop_protocol == "http" && port == 80
      ""
    else
      ":" + port.to_s
    end

    prestashop_domain = prestashop_host + prestashop_port + prestashop_path

    store = Shop::Prestashop.find_by(prestashop_domain: prestashop_domain)

    if store
      account = Account.find_by(shop_id: store.id)
      if account.valid_password?(prestashop_login_params[:password])
        cookies[:stocksync_current_store] = account.shop.prestashop_domain
        sign_in_and_redirect account, event: :authentication
      else
        flash[:error] = INCORRECT_CREDENTIALS
        redirect_back fallback_location: root_path
      end
    else
      flash[:error] = STORE_NOT_FOUND
      redirect_back fallback_location: root_path
    end
  end

  def prestashop_validate
    api_key = account_creation_params[:api_key]
    email = account_creation_params[:email]
    password = account_creation_params[:password]
    password_confirmation = account_creation_params[:password]
    protocol = account_creation_params[:protocol]
    shop = account_creation_params[:shop].strip

    if password != password_confirmation
      flash[:error] = UNMATCHED_PASSWORD
      redirect_back fallback_location: root_path
      return
    end

    domain = URI.parse(protocol + "://" + shop)
    prestashop_protocol = domain.scheme || protocol
    prestashop_host = domain.host || shop
    prestashop_host = prestashop_host.chomp("/") # remove last "/" if any
    prestashop_path = domain.path
    port = domain.port

    prestashop_port = if prestashop_protocol == "https" && port == 443
      ""
    elsif prestashop_protocol == "http" && port == 80
      ""
    else
      ":" + port.to_s
    end

    prestashop_domain = prestashop_host + prestashop_port + prestashop_path
    prestashop_url = "#{prestashop_protocol}://#{prestashop_domain}"

    unless prestashop_host
      flash[:error] = MISSING_SHOP_PARAMS
      redirect_back fallback_location: root_path
      return
    end

    if [".myshopify.com", ".mybigcommerce.com", "wixsite.com", "ekm.shop", "squarespace.com"].any? { |site| prestashop_host.index(site) }
      flash[:error] = INVALID_STORE_URL
      redirect_back fallback_location: root_path
      return
    end

    test_res = PrestashopApi.test_connection(
      url: prestashop_url,
      api_key: api_key
    )

    if !test_res
      flash[:error] = INVALID_STORE_URL
      redirect_back fallback_location: root_path
      return
    end

    user = Shop::Prestashop.where(prestashop_domain: prestashop_domain).first_or_initialize do |user|
      user.email = email
      user.notification_email = email
      user.password = Devise.friendly_token[0, 20]
      user.provider = "prestashop"
      user.prestashop_protocol = prestashop_protocol
      user.prestashop_api_key = api_key
      user.installed_date = Time.now
    end

    if user.new_record?
      user.email_subscriptions << "daily_summary" unless user.email_subscribed_to?(:daily_summary)
      user.email_subscriptions << "sync_failure" unless user.email_subscribed_to?(:sync_failure)
      user.save(validate: false)
      acc = Account.new(email: user.email, shop_id: user.id, password: password)
      acc.save!

      cookies[:stocksync_current_store] = user.prestashop_domain
      sign_in_and_redirect acc, event: :authentication
    else
      flash[:error] = STORE_FOUND
      redirect_to prestashop_integrations_path
    end
  end

  private

  def reset_password_params
    params.permit(:shop, :platform)
  end

  def woocommerce_login_params
    params.permit(:shop, :password, :protocol)
  end

  def square_login_params
    params.permit(:shop, :password, :authenticity_token, :utf8)
  end

  def quickbooks_login_params
    params.permit(:email, :password, :authenticity_token, :utf8)
  end

  def account_creation_params
    params.permit(:shop, :email, :password, :password_confirmation, :utf8, :authenticity_token, :protocol, :api_key)
  end

  def square_account_creation_params
    params.permit(:shop, :email, :square_merchant_id, :square_access_token, :square_refresh_token)
  end

  def prestashop_login_params
    params.permit(:shop, :password, :protocol)
  end
end
