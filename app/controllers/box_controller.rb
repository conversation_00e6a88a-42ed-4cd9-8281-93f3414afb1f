class BoxController < ConnectionsController
  def authorize
    current_profile
    authorize_url = Boxr.oauth_url(params[:profile_id], host: "account.box.com", response_type: "code", scope: "root_readwrite", client_id: ENV["BOX_CLIENT_ID"])

    redirect_to authorize_url.to_s
  end

  def save_access_token(profile, access, refresh)
    profile.box_access_token = access
    profile.box_refresh_token = refresh
    profile.oauth_granted = true
    profile.oauth_granted_to = "box"
    profile.source_type = "box"
    profile.save
  end

  def callback
    user_profile = UserProfile.where(id: params[:state]).last if params[:state]
    message = "Box authorize access failed"

    if params[:code]
      response = Boxr.get_tokens(params[:code], grant_type: "authorization_code", client_id: ENV["BOX_CLIENT_ID"], client_secret: ENV["BOX_CLIENT_SECRET"])

      if response[:access_token]
        save_access_token(user_profile, response[:access_token], response[:refresh_token])
        message = "Box authorize access successfully"
      end
    end

    redirect_to UserProfile.feed_manager_link(user_profile.id, user_profile.user), notice: message
  end

  def disconnect
    user_profile = UserProfile.where(id: params[:profile_id]).last
    user_profile.box_access_token = nil
    user_profile.box_refresh_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
