require "uri"

class WoocommerceController < ConnectionsController
  skip_before_action :authenticate_account!, only: [:callback]

  def authorize
    user_profile = UserProfile.find_by(id: params[:profile_id])

    store_url = user_profile.source_url
    endpoint = "/wc-auth/v1/authorize"
    params = {
      app_name: "syncX: Stock Sync",
      scope: "read_write",
      user_id: user_profile.id,
      return_url: UserProfile.feed_manager_link(user_profile.id, user_profile.user),
      callback_url: callback_woocommerce_index_url
    }
    query_string = URI.encode_www_form(params)

    redirect_to "#{store_url}#{endpoint}?#{query_string}", allow_other_host: true
  end

  def callback
    head :ok

    if params[:key_id]
      user_profile = UserProfile.find_by(id: params[:user_id])

      user_profile.woocommerce_consumer_key = params[:consumer_key]
      user_profile.woocommerce_consumer_secret = params[:consumer_secret]
      user_profile.oauth_granted = true
      user_profile.oauth_granted_to = "woocommerce"
      user_profile.source_type = "woocommerce"
      user_profile.save
    end
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.woocommerce_consumer_key = nil
    user_profile.woocommerce_consumer_secret = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end

  def sign_out
    reset_session
    cookies.delete(:stocksync_current_store)
    redirect_to new_account_session_url
  end
end
