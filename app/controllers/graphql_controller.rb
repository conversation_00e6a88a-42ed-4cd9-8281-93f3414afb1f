class GraphqlController < AuthenticatedController
  skip_before_action :verify_authenticity_token, only: [:execute]
  skip_before_action :redirect_authentication, only: [:execute]
  skip_before_action :create_shopify_session, only: [:execute]
  skip_before_action :check_if_store_validated, only: [:execute]
  skip_before_action :authenticate_account!, only: [:execute]
  skip_before_action :relogin_if_different_store, only: [:execute]
  skip_after_action :set_locale, only: [:execute]

  def execute
    variables = ensure_hash(params[:variables])
    query = params[:query]
    @operation_name = params[:operationName]
    gql_user = authenticate_user

    unless gql_user.present?
      raise GraphQL::ExecutionError.new("unauthorized", options: {status: :unauthorized, code: 401})
    end

    context = {
      current_shop: gql_user,
      is_impersonating_admin: session["impersonate"].present?
    }
    result = CloudcoderShopifySchema.execute(query, variables: variables, context: context, operation_name: @operation_name)

    if result["error"].present?
      # sometimes this error shows up -> { error: "You need to sign in or sign up before continuing."}
      # temporary logging to debug
      Airbrake.notify(result["error"]) do |notice|
        notice[:context][:environment] = "graphql"
        notice[:params] = {
          shop: current_shop.shopify_domain,
          query: query,
          impersonate: session["impersonate"],
          is_impersonating_admin: cookies.encrypted[:impersonating_session].present?
        }
      end
      redirect_to new_account_session_url
    else
      render json: result
    end
  rescue => e
    Airbrake.notify(e) do |notice|
      notice[:context][:environment] = "graphql"
      notice[:params] = {
        shop: current_shop&.shopify_domain,
        shop_id: current_shop&.id,
        query: query
      }
    end
    handle_error_in_development e
  end

  private

  # Handle form data, JSON body, or a blank value
  def ensure_hash(ambiguous_param)
    case ambiguous_param
    when String
      if ambiguous_param.present?
        ensure_hash(JSON.parse(ambiguous_param))
      else
        {}
      end
    when Hash, ActionController::Parameters
      ambiguous_param
    when nil
      {}
    else
      raise ArgumentError, "Unexpected parameter: #{ambiguous_param}"
    end
  end

  def handle_error_in_development(e)
    return unless Rails.env.development?

    logger.error e.message
    logger.error e.backtrace.join("\n")

    render json: {error: {message: e.message, backtrace: e.backtrace}, data: {}}, status: 500
  end

  def authenticate_user
    gql_token = if rapidapi_request?
      return nil unless Settings.rapid_api.allowed_mutations.include?(@operation_name)
      request.env["HTTP_STOCKSYNC_TOKEN"]
    else
      return nil if Rails.env.production? && request.headers["Origin"] != Settings.hostname
      params[:gql_token]
    end

    return nil if gql_token.blank?

    Rails.cache.fetch("gql_user_#{gql_token}", expires_in: 5.minutes) do
      User.find_by(public_token: gql_token)
    end
  end

  def rapidapi_request?
    return false unless request.env["HTTP_X_RAPIDAPI_PROXY_SECRET"].present?
    request.env["HTTP_X_RAPIDAPI_PROXY_SECRET"] == ENV["RAPIDAPI_API_SECRET"]
  end
end
