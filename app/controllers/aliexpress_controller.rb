class AliexpressController < ConnectionsController
  def authorize
    current_profile
    redirect_uri = callback_aliexpress_index_url
    grant_url = "https://oauth.aliexpress.com/authorize?response_type=code&client_id=#{current_profile.aliexpress_app_id}&redirect_uri=#{redirect_uri}&view=web&sp=ae"
    redirect_to grant_url
  end

  def callback
    notice = "Failed to authorize AliExpress, please try again"

    if params[:code]
      authorization_code = params[:code]
      uri = "https://oauth.aliexpress.com/token"
      user_profile = UserProfile.where(id: session[:profile_id]).last

      headers = {
        "Content-Type" => "application/x-www-form-urlencoded"
      }

      body = {
        "code" => authorization_code,
        "client_id" => user_profile.aliexpress_app_id,
        "client_secret" => user_profile.aliexpress_app_secret,
        "grant_type" => "authorization_code",
        "redirect_uri" => callback_aliexpress_index_url
      }

      body = URI.encode_www_form body

      response = HTTParty.post(uri, body: body, headers: headers)

      if response["access_token"]
        user_profile.aliexpress_access_token = response["access_token"]
        user_profile.oauth_granted = true
        user_profile.oauth_granted_to = "aliexpress"
        user_profile.save

        notice = "Successfully authorized AliExpress"
      end
    end

    redirect_to UserProfile.feed_manager_link(session[:profile_id], user_profile.user), notice: notice
  end

  def disconnect
    user_profile = UserProfile.where(id: session[:profile_id]).last
    user_profile.aliexpress_access_token = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
