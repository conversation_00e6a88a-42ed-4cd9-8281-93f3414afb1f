class SquareController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:revoke]

  def revoke
    merchant_id = params[:merchant_id]
    user = Shop::Square.find_by(square_merchant_id: merchant_id)
    user&.process_uninstall_event
    render json: {status: "success"}, status: :ok
  end

  def sign_out
    reset_session
    cookies.delete(:stocksync_current_store)
    redirect_to new_account_session_url
  end
end
