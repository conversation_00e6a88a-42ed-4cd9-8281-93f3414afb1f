class VendController < ConnectionsController
  def authorize
    current_profile
    redirect_uri = callback_vend_index_url
    client_id = ENV["VEND_CLIENT_ID"]
    state = params[:profile_id]
    grant_url = "https://secure.vendhq.com/connect?response_type=code&client_id=#{client_id}&redirect_uri=#{redirect_uri}&state=#{state}"

    redirect_to grant_url
  end

  def callback
    notice = "Failed to authorize V<PERSON>, please try again"

    if params[:code]
      authorization_code = params[:code]
      domain_prefix = params[:domain_prefix]
      user_profile_id = params[:state]
      uri = "https://#{domain_prefix}.vendhq.com/api/1.0/token"

      headers = {
        "Content-Type" => "application/x-www-form-urlencoded"
      }

      body = {
        "code" => authorization_code,
        "client_id" => ENV["VEND_CLIENT_ID"],
        "client_secret" => ENV["VEND_CLIENT_SECRET"],
        "grant_type" => "authorization_code",
        "redirect_uri" => callback_vend_index_url
      }

      body = URI.encode_www_form body

      response = HTTParty.post(uri, body: body, headers: headers)

      if response["access_token"]
        user_profile = UserProfile.find_by(id: user_profile_id)
        user_profile.vend_access_token = response["access_token"]
        user_profile.vend_refresh_token = response["refresh_token"]
        user_profile.vend_domain_prefix = domain_prefix
        user_profile.oauth_granted = true
        user_profile.oauth_granted_to = "vend"
        user_profile.save

        notice = "Successfully authorized Vend"
      end
    end

    redirect_to UserProfile.feed_manager_link(user_profile_id, user_profile&.user), notice: notice
  end

  def disconnect
    user_profile = UserProfile.find_by(id: params[:profile_id])
    user_profile.vend_access_token = nil
    user_profile.vend_refresh_token = nil
    user_profile.vend_domain_prefix = nil
    user_profile.oauth_granted = false
    user_profile.oauth_granted_to = nil
    user_profile.save
    redirect_back(fallback_location: root_path)
  end
end
